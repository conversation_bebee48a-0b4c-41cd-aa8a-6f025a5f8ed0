import React from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const UpcomingTasks = ({ className = "" }) => {
  const tasks = [
    {
      id: 1,
      type: 'inspection',
      title: 'État des lieux sortant',
      property: 'T2 Boulevard Saint-Germain',
      tenant: '<PERSON>',
      date: new Date(2025, 8, 2, 14, 0),
      priority: 'high',
      status: 'scheduled'
    },
    {
      id: 2,
      type: 'maintenance',
      title: 'Réparation plomberie',
      property: 'Studio Rue de la Paix',
      tenant: '<PERSON>',
      date: new Date(2025, 8, 2, 16, 30),
      priority: 'urgent',
      status: 'pending'
    },
    {
      id: 3,
      type: 'lease',
      title: 'Renouvellement de bail',
      property: 'T3 Avenue des Champs-Élysées',
      tenant: '<PERSON>',
      date: new Date(2025, 8, 3, 10, 0),
      priority: 'medium',
      status: 'draft'
    },
    {
      id: 4,
      type: 'payment',
      title: 'Relance paiement loyer',
      property: 'T1 Rue de Rivoli',
      tenant: '<PERSON>',
      date: new Date(2025, 8, 3, 9, 0),
      priority: 'high',
      status: 'overdue'
    },
    {
      id: 5,
      type: 'inspection',
      title: 'Visite technique annuelle',
      property: 'T4 Place Vendôme',
      tenant: 'Claire Rous<PERSON>',
      date: new Date(2025, 8, 4, 11, 0),
      priority: 'low',
      status: 'scheduled'
    }
  ];

  const formatDate = (date) => {
    return date?.toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const formatTime = (date) => {
    return date?.toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getTaskIcon = (type) => {
    switch (type) {
      case 'inspection': return 'Eye';
      case 'maintenance': return 'Wrench';
      case 'lease': return 'FileText';
      case 'payment': return 'CreditCard';
      default: return 'Calendar';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'urgent': return 'text-error bg-error/10';
      case 'high': return 'text-warning bg-warning/10';
      case 'medium': return 'text-primary bg-primary/10';
      case 'low': return 'text-muted-foreground bg-muted';
      default: return 'text-muted-foreground bg-muted';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'scheduled': return 'text-success bg-success/10';
      case 'pending': return 'text-warning bg-warning/10';
      case 'overdue': return 'text-error bg-error/10';
      case 'draft': return 'text-accent bg-accent/10';
      default: return 'text-muted-foreground bg-muted';
    }
  };

  const getStatusLabel = (status) => {
    switch (status) {
      case 'scheduled': return 'Programmé';
      case 'pending': return 'En attente';
      case 'overdue': return 'En retard';
      case 'draft': return 'Brouillon';
      default: return 'Inconnu';
    }
  };

  return (
    <div className={`bg-card border border-border rounded-lg ${className}`}>
      <div className="p-6 border-b border-border">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-foreground">Tâches à venir</h3>
          <Button variant="ghost" size="sm">
            <Icon name="Calendar" size={16} className="mr-2" />
            Planning
          </Button>
        </div>
      </div>
      <div className="max-h-96 overflow-y-auto">
        {tasks?.map((task) => (
          <div key={task?.id} className="p-4 border-b border-border last:border-b-0 hover:bg-muted transition-smooth">
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-start space-x-3">
                <div className={`p-2 rounded-lg ${getPriorityColor(task?.priority)}`}>
                  <Icon name={getTaskIcon(task?.type)} size={16} />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="font-medium text-sm text-foreground mb-1">{task?.title}</p>
                  <p className="text-sm text-muted-foreground mb-1">{task?.property}</p>
                  <p className="text-xs text-muted-foreground">{task?.tenant}</p>
                </div>
              </div>
              <div className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(task?.status)}`}>
                {getStatusLabel(task?.status)}
              </div>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                <div className="flex items-center space-x-1">
                  <Icon name="Calendar" size={12} />
                  <span>{formatDate(task?.date)}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Icon name="Clock" size={12} />
                  <span>{formatTime(task?.date)}</span>
                </div>
              </div>
              <Button variant="ghost" size="sm">
                <Icon name="ChevronRight" size={14} />
              </Button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default UpcomingTasks;