"""
PropertyFlow Backend - API Contrats
Gestion des contrats de location
"""

from flask import Blueprint, request, jsonify, g
from flask_jwt_extended import jwt_required
import logging
from datetime import datetime, timedelta
from database import contrat_service, bien_service, locataire_service
from utils.errors import NotFoundError, ValidationError, AuthorizationError, BusinessLogicError
from utils.middleware import require_agence_access, validate_json, log_activity

logger = logging.getLogger(__name__)

# Blueprint pour les contrats
contrats_bp = Blueprint('contrats', __name__)

@contrats_bp.route('', methods=['GET'])
@jwt_required()
@require_agence_access
@log_activity('list_contrats')
def get_contrats():
    """Récupérer tous les contrats de l'agence"""
    try:
        # Paramètres de filtrage
        statut = request.args.get('statut')
        bien_id = request.args.get('bien_id')
        locataire_id = request.args.get('locataire_id')
        
        filters = {'agence_id': g.agence_id}
        if statut:
            filters['statut'] = statut
        if bien_id:
            filters['bien_id'] = bien_id
        if locataire_id:
            filters['locataire_id'] = locataire_id
        
        contrats = contrat_service.get_all(filters=filters)
        
        return jsonify({
            'success': True,
            'data': contrats,
            'total': len(contrats)
        }), 200
        
    except Exception as e:
        logger.error(f"Get contrats error: {str(e)}")
        raise

@contrats_bp.route('/<contrat_id>', methods=['GET'])
@jwt_required()
@require_agence_access
@log_activity('get_contrat')
def get_contrat(contrat_id):
    """Récupérer un contrat par ID"""
    try:
        contrat = contrat_service.get_by_id(contrat_id)
        if not contrat:
            raise NotFoundError("Contrat not found")
        
        # Vérifier que le contrat appartient à l'agence
        if contrat['agence_id'] != g.agence_id:
            raise AuthorizationError("Access denied to this contrat")
        
        return jsonify({
            'success': True,
            'data': contrat
        }), 200
        
    except Exception as e:
        logger.error(f"Get contrat error: {str(e)}")
        raise

@contrats_bp.route('', methods=['POST'])
@jwt_required()
@require_agence_access
@validate_json(['bien_id', 'locataire_id', 'date_debut', 'date_fin', 'loyer_mensuel', 'depot_garantie'])
@log_activity('create_contrat')
def create_contrat():
    """Créer un nouveau contrat"""
    try:
        data = request.get_json()
        
        # Vérifier que le bien existe et appartient à l'agence
        bien = bien_service.get_by_id(data['bien_id'])
        if not bien:
            raise NotFoundError("Bien not found")
        
        if bien['agence_id'] != g.agence_id:
            raise AuthorizationError("Bien does not belong to your agency")
        
        # Vérifier que le bien est disponible
        if bien['statut'] != 'disponible':
            raise BusinessLogicError("Bien is not available for rent")
        
        # Vérifier que le locataire existe et appartient à l'agence
        locataire = locataire_service.get_by_id(data['locataire_id'])
        if not locataire:
            raise NotFoundError("Locataire not found")
        
        if locataire['agence_id'] != g.agence_id:
            raise AuthorizationError("Locataire does not belong to your agency")
        
        # Vérifier qu'il n'y a pas déjà un contrat actif pour ce bien
        contrats_actifs = contrat_service.get_all(filters={
            'bien_id': data['bien_id'],
            'statut': 'actif'
        })
        
        if contrats_actifs:
            raise BusinessLogicError("There is already an active contract for this property")
        
        # Calculer la durée en mois
        date_debut = datetime.fromisoformat(data['date_debut'].replace('Z', '+00:00'))
        date_fin = datetime.fromisoformat(data['date_fin'].replace('Z', '+00:00'))
        duree_mois = (date_fin.year - date_debut.year) * 12 + (date_fin.month - date_debut.month)
        
        # Générer un numéro de contrat unique
        # TODO: Utiliser la fonction SQL generate_contract_number
        numero_contrat = f"CTR-{datetime.now().year}-{len(contrat_service.get_by_agence(g.agence_id)) + 1:04d}"
        
        # Créer le contrat
        contrat_data = {
            'agence_id': g.agence_id,
            'bien_id': data['bien_id'],
            'locataire_id': data['locataire_id'],
            'numero_contrat': numero_contrat,
            'type_contrat': data.get('type_contrat', 'habitation'),
            'date_debut': data['date_debut'],
            'date_fin': data['date_fin'],
            'duree_mois': duree_mois,
            'loyer_mensuel': data['loyer_mensuel'],
            'charges_mensuelles': data.get('charges_mensuelles', 0),
            'depot_garantie': data['depot_garantie'],
            'frais_agence': data.get('frais_agence', 0),
            'indexation_loyer': data.get('indexation_loyer', True),
            'indice_reference': data.get('indice_reference', 'IRL'),
            'date_revision': data.get('date_revision'),
            'clauses_particulieres': data.get('clauses_particulieres'),
            'conditions_resiliation': data.get('conditions_resiliation'),
            'documents_annexes': data.get('documents_annexes', []),
            'statut': 'brouillon'
        }
        
        new_contrat = contrat_service.create(contrat_data)
        
        logger.info(f"New contrat created: {new_contrat['id']}")
        
        return jsonify({
            'success': True,
            'message': 'Contrat created successfully',
            'data': new_contrat
        }), 201
        
    except Exception as e:
        logger.error(f"Create contrat error: {str(e)}")
        raise

@contrats_bp.route('/<contrat_id>', methods=['PUT'])
@jwt_required()
@require_agence_access
@validate_json()
@log_activity('update_contrat')
def update_contrat(contrat_id):
    """Mettre à jour un contrat"""
    try:
        # Vérifier que le contrat existe et appartient à l'agence
        contrat = contrat_service.get_by_id(contrat_id)
        if not contrat:
            raise NotFoundError("Contrat not found")
        
        if contrat['agence_id'] != g.agence_id:
            raise AuthorizationError("Access denied to this contrat")
        
        data = request.get_json()
        
        # Champs modifiables selon le statut
        if contrat['statut'] == 'actif':
            # Contrat actif : modifications limitées
            allowed_fields = [
                'charges_mensuelles', 'clauses_particulieres', 'conditions_resiliation',
                'documents_annexes', 'date_revision'
            ]
        else:
            # Contrat brouillon : toutes modifications
            allowed_fields = [
                'type_contrat', 'date_debut', 'date_fin', 'duree_mois',
                'loyer_mensuel', 'charges_mensuelles', 'depot_garantie', 'frais_agence',
                'indexation_loyer', 'indice_reference', 'date_revision',
                'clauses_particulieres', 'conditions_resiliation', 'documents_annexes'
            ]
        
        update_data = {k: v for k, v in data.items() if k in allowed_fields}
        
        if not update_data:
            raise ValidationError("No valid fields to update")
        
        # Recalculer la durée si les dates changent
        if 'date_debut' in update_data or 'date_fin' in update_data:
            date_debut = datetime.fromisoformat(update_data.get('date_debut', contrat['date_debut']).replace('Z', '+00:00'))
            date_fin = datetime.fromisoformat(update_data.get('date_fin', contrat['date_fin']).replace('Z', '+00:00'))
            update_data['duree_mois'] = (date_fin.year - date_debut.year) * 12 + (date_fin.month - date_debut.month)
        
        updated_contrat = contrat_service.update(contrat_id, update_data)
        
        logger.info(f"Contrat updated: {contrat_id}")
        
        return jsonify({
            'success': True,
            'message': 'Contrat updated successfully',
            'data': updated_contrat
        }), 200
        
    except Exception as e:
        logger.error(f"Update contrat error: {str(e)}")
        raise

@contrats_bp.route('/<contrat_id>/activate', methods=['POST'])
@jwt_required()
@require_agence_access
@log_activity('activate_contrat')
def activate_contrat(contrat_id):
    """Activer un contrat (passer de brouillon à actif)"""
    try:
        contrat = contrat_service.get_by_id(contrat_id)
        if not contrat:
            raise NotFoundError("Contrat not found")
        
        if contrat['agence_id'] != g.agence_id:
            raise AuthorizationError("Access denied to this contrat")
        
        if contrat['statut'] != 'brouillon':
            raise BusinessLogicError("Only draft contracts can be activated")
        
        # Vérifier que le bien est toujours disponible
        bien = bien_service.get_by_id(contrat['bien_id'])
        if bien['statut'] != 'disponible':
            raise BusinessLogicError("Property is no longer available")
        
        # Activer le contrat
        contrat_service.update(contrat_id, {'statut': 'actif'})
        
        # Marquer le bien comme occupé
        bien_service.update(contrat['bien_id'], {'statut': 'occupe'})
        
        logger.info(f"Contrat activated: {contrat_id}")
        
        return jsonify({
            'success': True,
            'message': 'Contrat activated successfully'
        }), 200
        
    except Exception as e:
        logger.error(f"Activate contrat error: {str(e)}")
        raise

@contrats_bp.route('/<contrat_id>/terminate', methods=['POST'])
@jwt_required()
@require_agence_access
@validate_json(['date_resiliation', 'motif_resiliation'])
@log_activity('terminate_contrat')
def terminate_contrat(contrat_id):
    """Résilier un contrat"""
    try:
        contrat = contrat_service.get_by_id(contrat_id)
        if not contrat:
            raise NotFoundError("Contrat not found")
        
        if contrat['agence_id'] != g.agence_id:
            raise AuthorizationError("Access denied to this contrat")
        
        if contrat['statut'] != 'actif':
            raise BusinessLogicError("Only active contracts can be terminated")
        
        data = request.get_json()
        
        # Résilier le contrat
        update_data = {
            'statut': 'resilie',
            'date_resiliation': data['date_resiliation'],
            'motif_resiliation': data['motif_resiliation']
        }
        
        contrat_service.update(contrat_id, update_data)
        
        # Marquer le bien comme disponible
        bien_service.update(contrat['bien_id'], {'statut': 'disponible'})
        
        logger.info(f"Contrat terminated: {contrat_id}")
        
        return jsonify({
            'success': True,
            'message': 'Contrat terminated successfully'
        }), 200
        
    except Exception as e:
        logger.error(f"Terminate contrat error: {str(e)}")
        raise

@contrats_bp.route('/<contrat_id>/generate-pdf', methods=['POST'])
@jwt_required()
@require_agence_access
@log_activity('generate_contrat_pdf')
def generate_contrat_pdf(contrat_id):
    """Générer le PDF du contrat"""
    try:
        contrat = contrat_service.get_by_id(contrat_id)
        if not contrat:
            raise NotFoundError("Contrat not found")
        
        if contrat['agence_id'] != g.agence_id:
            raise AuthorizationError("Access denied to this contrat")
        
        # TODO: Implémenter la génération PDF avec ReportLab
        pdf_url = f"/documents/contrats/{contrat_id}.pdf"
        
        # Mettre à jour l'URL du document
        contrat_service.update(contrat_id, {'document_pdf_url': pdf_url})
        
        return jsonify({
            'success': True,
            'message': 'PDF generated successfully',
            'pdf_url': pdf_url
        }), 200
        
    except Exception as e:
        logger.error(f"Generate PDF error: {str(e)}")
        raise
