import React from 'react';
import Icon from '../../../components/AppIcon';

const FinancialMetricsCards = ({ metrics, period }) => {
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    })?.format(amount);
  };

  const formatPercentage = (value) => {
    return `${value > 0 ? '+' : ''}${value?.toFixed(1)}%`;
  };

  const getMetricIcon = (type) => {
    switch (type) {
      case 'revenue': return 'TrendingUp';
      case 'expenses': return 'TrendingDown';
      case 'profit': return 'DollarSign';
      case 'collection': return 'Target';
      default: return 'BarChart3';
    }
  };

  const getMetricColor = (type, change) => {
    if (type === 'expenses') {
      return change > 0 ? 'text-error' : 'text-success';
    }
    return change > 0 ? 'text-success' : 'text-error';
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {metrics?.map((metric) => (
        <div key={metric?.id} className="bg-card border border-border rounded-lg p-6 elevation-1">
          <div className="flex items-center justify-between mb-4">
            <div className={`p-3 rounded-lg ${
              metric?.type === 'revenue' ? 'bg-success/10 text-success' :
              metric?.type === 'expenses' ? 'bg-warning/10 text-warning' :
              metric?.type === 'profit'? 'bg-primary/10 text-primary' : 'bg-accent/10 text-accent'
            }`}>
              <Icon name={getMetricIcon(metric?.type)} size={24} />
            </div>
            <div className="text-right">
              <p className="text-xs text-muted-foreground uppercase tracking-wide">
                {period}
              </p>
            </div>
          </div>
          
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-muted-foreground">
              {metric?.label}
            </h3>
            <p className="text-2xl font-bold text-foreground">
              {metric?.type === 'collection' ? `${metric?.value}%` : formatCurrency(metric?.value)}
            </p>
            <div className="flex items-center space-x-2">
              <Icon 
                name={metric?.change > 0 ? "TrendingUp" : "TrendingDown"} 
                size={16} 
                className={getMetricColor(metric?.type, metric?.change)}
              />
              <span className={`text-sm font-medium ${getMetricColor(metric?.type, metric?.change)}`}>
                {formatPercentage(metric?.change)}
              </span>
              <span className="text-xs text-muted-foreground">
                vs période précédente
              </span>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default FinancialMetricsCards;