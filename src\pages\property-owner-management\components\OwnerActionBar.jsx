import React from 'react';
import Button from '../../../components/ui/Button';
import Select from '../../../components/ui/Select';
import Icon from '../../../components/AppIcon';

const OwnerActionBar = ({ 
  onAddOwner, 
  onExport, 
  onBulkAction, 
  selectedCount = 0, 
  totalCount = 0, 
  className = "" 
}) => {
  const exportOptions = [
    { value: 'excel', label: 'Export Excel' },
    { value: 'pdf', label: 'Export PDF' },
    { value: 'csv', label: 'Export CSV' }
  ];

  const bulkActionOptions = [
    { value: 'email', label: 'Envoyer Email' },
    { value: 'sms', label: 'Envoyer SMS' },
    { value: 'document', label: 'Générer Documents' },
    { value: 'update_rate', label: 'Modifier Taux' },
    { value: 'archive', label: 'Archiver' }
  ];

  const handleExportSelect = (format) => {
    onExport?.(format);
  };

  const handleBulkActionSelect = (action) => {
    if (selectedCount > 0) {
      onBulkAction?.(action);
    }
  };

  return (
    <div className={`bg-card rounded-lg p-4 border border-border ${className}`}>
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        {/* Left Side - Selection Info */}
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Icon name="Users" size={20} className="text-muted-foreground" />
            <span className="text-sm font-medium text-foreground">
              {totalCount} bailleurs
            </span>
            {selectedCount > 0 && (
              <span className="text-sm text-muted-foreground">
                ({selectedCount} sélectionnés)
              </span>
            )}
          </div>

          {/* Bulk Actions */}
          {selectedCount > 0 && (
            <div className="flex items-center gap-2">
              <Select
                onValueChange={handleBulkActionSelect}
                options={bulkActionOptions}
                placeholder={`Actions (${selectedCount})`}
                className="min-w-[160px]"
              />
            </div>
          )}
        </div>

        {/* Right Side - Actions */}
        <div className="flex items-center gap-3">
          {/* Export */}
          <Select
            onValueChange={handleExportSelect}
            options={exportOptions}
            placeholder="Exporter"
            className="min-w-[120px]"
          />

          {/* Quick Actions */}
          <Button
            variant="outline"
            iconName="FileText"
            onClick={() => console.log('Generate report')}
          >
            Rapport
          </Button>

          <Button
            variant="outline"
            iconName="Mail"
            onClick={() => console.log('Send newsletter')}
          >
            Newsletter
          </Button>

          {/* Primary Action */}
          <Button
            iconName="Plus"
            onClick={onAddOwner}
            className="whitespace-nowrap"
          >
            Ajouter Bailleur
          </Button>
        </div>
      </div>

      {/* Quick Stats Bar */}
      <div className="mt-4 pt-4 border-t border-border">
        <div className="grid grid-cols-2 sm:grid-cols-4 lg:grid-cols-6 gap-4">
          <div className="text-center">
            <div className="text-lg font-semibold text-foreground">85.5%</div>
            <div className="text-xs text-muted-foreground">Taux Satisfaction</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-foreground">€2.1M</div>
            <div className="text-xs text-muted-foreground">Revenus Annuels</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-foreground">7.8%</div>
            <div className="text-xs text-muted-foreground">Taux Moyen</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-foreground">€15.2K</div>
            <div className="text-xs text-muted-foreground">Loyer Moyen</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-foreground">98%</div>
            <div className="text-xs text-muted-foreground">Conformité</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-foreground">24h</div>
            <div className="text-xs text-muted-foreground">Délai Réponse</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OwnerActionBar;