import React from 'react';
import Icon from '../../../components/AppIcon';

const TenantStats = ({ stats = {}, className = "" }) => {
  const defaultStats = {
    totalTenants: 0,
    activeLeases: 0,
    expiringLeases: 0,
    latePayments: 0,
    totalRentCollected: 0,
    occupancyRate: 0,
    ...stats
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    })?.format(amount);
  };

  const formatPercentage = (value) => {
    return `${Math.round(value)}%`;
  };

  const statCards = [
    {
      id: 'total-tenants',
      title: 'Total Locataires',
      value: defaultStats?.totalTenants,
      icon: 'Users',
      color: 'text-primary bg-primary/10',
      trend: null
    },
    {
      id: 'active-leases',
      title: 'Baux Actifs',
      value: defaultStats?.activeLeases,
      icon: 'FileText',
      color: 'text-success bg-success/10',
      trend: null
    },
    {
      id: 'expiring-leases',
      title: 'Baux Expirant',
      value: defaultStats?.expiringLeases,
      subtitle: 'Dans 30 jours',
      icon: 'Clock',
      color: 'text-warning bg-warning/10',
      trend: null
    },
    {
      id: 'late-payments',
      title: 'Paiements en Retard',
      value: defaultStats?.latePayments,
      icon: 'AlertCircle',
      color: 'text-error bg-error/10',
      trend: null
    },
    {
      id: 'rent-collected',
      title: 'Loyers Collectés',
      value: formatCurrency(defaultStats?.totalRentCollected),
      subtitle: 'Ce mois',
      icon: 'TrendingUp',
      color: 'text-accent bg-accent/10',
      trend: '+12%'
    },
    {
      id: 'occupancy-rate',
      title: 'Taux d\'Occupation',
      value: formatPercentage(defaultStats?.occupancyRate),
      icon: 'Home',
      color: 'text-secondary bg-secondary/10',
      trend: '+2%'
    }
  ];

  return (
    <div className={`grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4 ${className}`}>
      {statCards?.map((card) => (
        <div
          key={card?.id}
          className="bg-card border border-border rounded-lg p-4 hover:elevation-2 transition-smooth"
        >
          <div className="flex items-center justify-between mb-3">
            <div className={`p-2 rounded-lg ${card?.color}`}>
              <Icon name={card?.icon} size={20} />
            </div>
            {card?.trend && (
              <span className={`text-xs font-medium px-2 py-1 rounded-full ${
                card?.trend?.startsWith('+') 
                  ? 'bg-success/10 text-success' :'bg-error/10 text-error'
              }`}>
                {card?.trend}
              </span>
            )}
          </div>
          
          <div>
            <p className="text-2xl font-bold text-foreground mb-1">
              {card?.value}
            </p>
            <p className="text-sm font-medium text-foreground">
              {card?.title}
            </p>
            {card?.subtitle && (
              <p className="text-xs text-muted-foreground mt-1">
                {card?.subtitle}
              </p>
            )}
          </div>
        </div>
      ))}
    </div>
  );
};

export default TenantStats;