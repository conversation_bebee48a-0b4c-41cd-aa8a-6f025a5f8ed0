import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';
import Select from '../../../components/ui/Select';
import { Checkbox } from '../../../components/ui/Checkbox';

const PropertyFilters = ({ 
  isOpen = false, 
  onToggle = () => {},
  filters = {},
  onFiltersChange = () => {},
  onClearFilters = () => {},
  className = "" 
}) => {
  const [localFilters, setLocalFilters] = useState(filters);

  const propertyTypes = [
    { value: 'apartment', label: 'Appartement' },
    { value: 'house', label: 'Maison' },
    { value: 'studio', label: 'Studio' },
    { value: 'duplex', label: 'Duplex' },
    { value: 'loft', label: 'Loft' },
    { value: 'commercial', label: 'Commercial' }
  ];

  const occupancyStatuses = [
    { value: 'occupied', label: 'Occupé' },
    { value: 'vacant', label: 'Vacant' },
    { value: 'maintenance', label: 'En maintenance' },
    { value: 'reserved', label: 'Réservé' }
  ];

  const cities = [
    { value: 'paris', label: 'Paris' },
    { value: 'lyon', label: 'Lyon' },
    { value: 'marseille', label: 'Marseille' },
    { value: 'toulouse', label: 'Toulouse' },
    { value: 'nice', label: 'Nice' },
    { value: 'nantes', label: 'Nantes' }
  ];

  const owners = [
    { value: 'martin-jean', label: 'Jean Martin' },
    { value: 'dubois-marie', label: 'Marie Dubois' },
    { value: 'laurent-pierre', label: 'Pierre Laurent' },
    { value: 'moreau-sophie', label: 'Sophie Moreau' },
    { value: 'bernard-luc', label: 'Luc Bernard' }
  ];

  const handleFilterChange = (key, value) => {
    const updatedFilters = { ...localFilters, [key]: value };
    setLocalFilters(updatedFilters);
    onFiltersChange(updatedFilters);
  };

  const handleClearAll = () => {
    const clearedFilters = {
      search: '',
      propertyType: '',
      occupancyStatus: '',
      city: '',
      owner: '',
      minRent: '',
      maxRent: '',
      minRooms: '',
      maxRooms: '',
      hasParking: false,
      hasBalcony: false,
      furnished: false
    };
    setLocalFilters(clearedFilters);
    onFiltersChange(clearedFilters);
    onClearFilters();
  };

  const getActiveFiltersCount = () => {
    return Object.values(localFilters)?.filter(value => 
      value !== '' && value !== false && value !== null && value !== undefined
    )?.length;
  };

  if (!isOpen) return null;

  return (
    <div className={`bg-card border border-border rounded-lg p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <Icon name="Filter" size={20} className="text-primary" />
          <h3 className="font-semibold text-foreground">Filtres avancés</h3>
          {getActiveFiltersCount() > 0 && (
            <span className="bg-primary text-primary-foreground text-xs px-2 py-1 rounded-full font-medium">
              {getActiveFiltersCount()}
            </span>
          )}
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClearAll}
            disabled={getActiveFiltersCount() === 0}
          >
            <Icon name="X" size={14} className="mr-1" />
            Effacer tout
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={onToggle}
            className="md:hidden"
          >
            <Icon name="X" size={16} />
          </Button>
        </div>
      </div>
      {/* Filter Groups */}
      <div className="space-y-6">
        {/* Basic Filters */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Select
            label="Type de propriété"
            placeholder="Tous les types"
            options={propertyTypes}
            value={localFilters?.propertyType || ''}
            onChange={(value) => handleFilterChange('propertyType', value)}
          />
          
          <Select
            label="Statut d'occupation"
            placeholder="Tous les statuts"
            options={occupancyStatuses}
            value={localFilters?.occupancyStatus || ''}
            onChange={(value) => handleFilterChange('occupancyStatus', value)}
          />
          
          <Select
            label="Ville"
            placeholder="Toutes les villes"
            options={cities}
            value={localFilters?.city || ''}
            onChange={(value) => handleFilterChange('city', value)}
            searchable
          />
          
          <Select
            label="Propriétaire"
            placeholder="Tous les propriétaires"
            options={owners}
            value={localFilters?.owner || ''}
            onChange={(value) => handleFilterChange('owner', value)}
            searchable
          />
        </div>

        {/* Price Range */}
        <div>
          <label className="block text-sm font-medium text-foreground mb-3">
            Fourchette de loyer (€/mois)
          </label>
          <div className="grid grid-cols-2 gap-4">
            <Input
              type="number"
              placeholder="Min"
              value={localFilters?.minRent || ''}
              onChange={(e) => handleFilterChange('minRent', e?.target?.value)}
            />
            <Input
              type="number"
              placeholder="Max"
              value={localFilters?.maxRent || ''}
              onChange={(e) => handleFilterChange('maxRent', e?.target?.value)}
            />
          </div>
        </div>

        {/* Room Range */}
        <div>
          <label className="block text-sm font-medium text-foreground mb-3">
            Nombre de pièces
          </label>
          <div className="grid grid-cols-2 gap-4">
            <Input
              type="number"
              placeholder="Min"
              value={localFilters?.minRooms || ''}
              onChange={(e) => handleFilterChange('minRooms', e?.target?.value)}
            />
            <Input
              type="number"
              placeholder="Max"
              value={localFilters?.maxRooms || ''}
              onChange={(e) => handleFilterChange('maxRooms', e?.target?.value)}
            />
          </div>
        </div>

        {/* Features */}
        <div>
          <label className="block text-sm font-medium text-foreground mb-3">
            Caractéristiques
          </label>
          <div className="space-y-3">
            <Checkbox
              label="Parking inclus"
              checked={localFilters?.hasParking || false}
              onChange={(e) => handleFilterChange('hasParking', e?.target?.checked)}
            />
            <Checkbox
              label="Balcon/Terrasse"
              checked={localFilters?.hasBalcony || false}
              onChange={(e) => handleFilterChange('hasBalcony', e?.target?.checked)}
            />
            <Checkbox
              label="Meublé"
              checked={localFilters?.furnished || false}
              onChange={(e) => handleFilterChange('furnished', e?.target?.checked)}
            />
          </div>
        </div>
      </div>
      {/* Apply Button (Mobile) */}
      <div className="mt-6 md:hidden">
        <Button
          variant="default"
          fullWidth
          onClick={onToggle}
        >
          Appliquer les filtres ({getActiveFiltersCount()})
        </Button>
      </div>
    </div>
  );
};

export default PropertyFilters;