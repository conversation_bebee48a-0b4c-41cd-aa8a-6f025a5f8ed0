import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';
import Select from '../../../components/ui/Select';

const AddTenantModal = ({ 
  isOpen = false, 
  onClose = () => {},
  onSubmit = () => {},
  properties = [],
  className = ""
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    // Personal Information
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    dateOfBirth: '',
    nationality: '',
    
    // Address
    currentAddress: '',
    city: '',
    postalCode: '',
    
    // Professional Information
    occupation: '',
    employer: '',
    monthlyIncome: '',
    
    // Lease Information
    propertyId: '',
    leaseStartDate: '',
    leaseDuration: '12',
    monthlyRent: '',
    securityDeposit: '',
    
    // Emergency Contact
    emergencyContactName: '',
    emergencyContactPhone: '',
    emergencyContactRelation: ''
  });

  const [errors, setErrors] = useState({});

  const nationalityOptions = [
    { value: 'french', label: 'Française' },
    { value: 'european', label: 'Union Européenne' },
    { value: 'other', label: 'Autre' }
  ];

  const leaseDurationOptions = [
    { value: '6', label: '6 mois' },
    { value: '12', label: '12 mois' },
    { value: '24', label: '24 mois' },
    { value: '36', label: '36 mois' }
  ];

  const relationOptions = [
    { value: 'parent', label: 'Parent' },
    { value: 'sibling', label: 'Frère/Sœur' },
    { value: 'friend', label: 'Ami(e)' },
    { value: 'colleague', label: 'Collègue' },
    { value: 'other', label: 'Autre' }
  ];

  const propertyOptions = properties?.map(property => ({
    value: property?.id,
    label: `${property?.address} - ${property?.city} (${property?.rent}€/mois)`
  }));

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors?.[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateStep = (step) => {
    const newErrors = {};

    if (step === 1) {
      if (!formData?.firstName) newErrors.firstName = 'Prénom requis';
      if (!formData?.lastName) newErrors.lastName = 'Nom requis';
      if (!formData?.email) newErrors.email = 'Email requis';
      if (!formData?.phone) newErrors.phone = 'Téléphone requis';
      if (!formData?.dateOfBirth) newErrors.dateOfBirth = 'Date de naissance requise';
    } else if (step === 2) {
      if (!formData?.currentAddress) newErrors.currentAddress = 'Adresse requise';
      if (!formData?.city) newErrors.city = 'Ville requise';
      if (!formData?.postalCode) newErrors.postalCode = 'Code postal requis';
    } else if (step === 3) {
      if (!formData?.occupation) newErrors.occupation = 'Profession requise';
      if (!formData?.monthlyIncome) newErrors.monthlyIncome = 'Revenus requis';
    } else if (step === 4) {
      if (!formData?.propertyId) newErrors.propertyId = 'Propriété requise';
      if (!formData?.leaseStartDate) newErrors.leaseStartDate = 'Date de début requise';
      if (!formData?.monthlyRent) newErrors.monthlyRent = 'Loyer requis';
      if (!formData?.securityDeposit) newErrors.securityDeposit = 'Dépôt de garantie requis';
    }

    setErrors(newErrors);
    return Object.keys(newErrors)?.length === 0;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, 5));
    }
  };

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmit = () => {
    if (validateStep(currentStep)) {
      onSubmit(formData);
      handleClose();
    }
  };

  const handleClose = () => {
    setCurrentStep(1);
    setFormData({
      firstName: '', lastName: '', email: '', phone: '', dateOfBirth: '', nationality: '',
      currentAddress: '', city: '', postalCode: '',
      occupation: '', employer: '', monthlyIncome: '',
      propertyId: '', leaseStartDate: '', leaseDuration: '12', monthlyRent: '', securityDeposit: '',
      emergencyContactName: '', emergencyContactPhone: '', emergencyContactRelation: ''
    });
    setErrors({});
    onClose();
  };

  const steps = [
    { number: 1, title: 'Informations personnelles', icon: 'User' },
    { number: 2, title: 'Adresse actuelle', icon: 'MapPin' },
    { number: 3, title: 'Informations professionnelles', icon: 'Briefcase' },
    { number: 4, title: 'Détails du bail', icon: 'FileText' },
    { number: 5, title: 'Contact d\'urgence', icon: 'Phone' }
  ];

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      <div className="fixed inset-0 bg-black/50" onClick={handleClose} />
      <div className={`relative bg-card border border-border rounded-lg w-full max-w-2xl max-h-[90vh] overflow-hidden ${className}`}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border">
          <div>
            <h2 className="text-xl font-semibold text-foreground">
              Ajouter un nouveau locataire
            </h2>
            <p className="text-sm text-muted-foreground mt-1">
              Étape {currentStep} sur {steps?.length}: {steps?.[currentStep - 1]?.title}
            </p>
          </div>
          <Button variant="ghost" size="icon" onClick={handleClose}>
            <Icon name="X" size={20} />
          </Button>
        </div>

        {/* Progress Steps */}
        <div className="px-6 py-4 border-b border-border">
          <div className="flex items-center justify-between">
            {steps?.map((step, index) => (
              <div key={step?.number} className="flex items-center">
                <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                  currentStep >= step?.number
                    ? 'bg-primary border-primary text-primary-foreground'
                    : 'border-border text-muted-foreground'
                }`}>
                  {currentStep > step?.number ? (
                    <Icon name="Check" size={16} />
                  ) : (
                    <Icon name={step?.icon} size={16} />
                  )}
                </div>
                {index < steps?.length - 1 && (
                  <div className={`w-12 h-0.5 mx-2 ${
                    currentStep > step?.number ? 'bg-primary' : 'bg-border'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Form Content */}
        <div className="p-6 overflow-y-auto max-h-96">
          {/* Step 1: Personal Information */}
          {currentStep === 1 && (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Prénom"
                  type="text"
                  required
                  value={formData?.firstName}
                  onChange={(e) => handleInputChange('firstName', e?.target?.value)}
                  error={errors?.firstName}
                />
                <Input
                  label="Nom"
                  type="text"
                  required
                  value={formData?.lastName}
                  onChange={(e) => handleInputChange('lastName', e?.target?.value)}
                  error={errors?.lastName}
                />
              </div>
              <Input
                label="Email"
                type="email"
                required
                value={formData?.email}
                onChange={(e) => handleInputChange('email', e?.target?.value)}
                error={errors?.email}
              />
              <Input
                label="Téléphone"
                type="tel"
                required
                value={formData?.phone}
                onChange={(e) => handleInputChange('phone', e?.target?.value)}
                error={errors?.phone}
              />
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Date de naissance"
                  type="date"
                  required
                  value={formData?.dateOfBirth}
                  onChange={(e) => handleInputChange('dateOfBirth', e?.target?.value)}
                  error={errors?.dateOfBirth}
                />
                <Select
                  label="Nationalité"
                  options={nationalityOptions}
                  value={formData?.nationality}
                  onChange={(value) => handleInputChange('nationality', value)}
                />
              </div>
            </div>
          )}

          {/* Step 2: Address */}
          {currentStep === 2 && (
            <div className="space-y-4">
              <Input
                label="Adresse actuelle"
                type="text"
                required
                value={formData?.currentAddress}
                onChange={(e) => handleInputChange('currentAddress', e?.target?.value)}
                error={errors?.currentAddress}
              />
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Ville"
                  type="text"
                  required
                  value={formData?.city}
                  onChange={(e) => handleInputChange('city', e?.target?.value)}
                  error={errors?.city}
                />
                <Input
                  label="Code postal"
                  type="text"
                  required
                  value={formData?.postalCode}
                  onChange={(e) => handleInputChange('postalCode', e?.target?.value)}
                  error={errors?.postalCode}
                />
              </div>
            </div>
          )}

          {/* Step 3: Professional Information */}
          {currentStep === 3 && (
            <div className="space-y-4">
              <Input
                label="Profession"
                type="text"
                required
                value={formData?.occupation}
                onChange={(e) => handleInputChange('occupation', e?.target?.value)}
                error={errors?.occupation}
              />
              <Input
                label="Employeur"
                type="text"
                value={formData?.employer}
                onChange={(e) => handleInputChange('employer', e?.target?.value)}
              />
              <Input
                label="Revenus mensuels (€)"
                type="number"
                required
                value={formData?.monthlyIncome}
                onChange={(e) => handleInputChange('monthlyIncome', e?.target?.value)}
                error={errors?.monthlyIncome}
              />
            </div>
          )}

          {/* Step 4: Lease Information */}
          {currentStep === 4 && (
            <div className="space-y-4">
              <Select
                label="Propriété"
                options={propertyOptions}
                required
                value={formData?.propertyId}
                onChange={(value) => handleInputChange('propertyId', value)}
                error={errors?.propertyId}
                searchable
              />
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Date de début du bail"
                  type="date"
                  required
                  value={formData?.leaseStartDate}
                  onChange={(e) => handleInputChange('leaseStartDate', e?.target?.value)}
                  error={errors?.leaseStartDate}
                />
                <Select
                  label="Durée du bail"
                  options={leaseDurationOptions}
                  value={formData?.leaseDuration}
                  onChange={(value) => handleInputChange('leaseDuration', value)}
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Loyer mensuel (€)"
                  type="number"
                  required
                  value={formData?.monthlyRent}
                  onChange={(e) => handleInputChange('monthlyRent', e?.target?.value)}
                  error={errors?.monthlyRent}
                />
                <Input
                  label="Dépôt de garantie (€)"
                  type="number"
                  required
                  value={formData?.securityDeposit}
                  onChange={(e) => handleInputChange('securityDeposit', e?.target?.value)}
                  error={errors?.securityDeposit}
                />
              </div>
            </div>
          )}

          {/* Step 5: Emergency Contact */}
          {currentStep === 5 && (
            <div className="space-y-4">
              <Input
                label="Nom du contact d'urgence"
                type="text"
                value={formData?.emergencyContactName}
                onChange={(e) => handleInputChange('emergencyContactName', e?.target?.value)}
              />
              <Input
                label="Téléphone du contact d'urgence"
                type="tel"
                value={formData?.emergencyContactPhone}
                onChange={(e) => handleInputChange('emergencyContactPhone', e?.target?.value)}
              />
              <Select
                label="Relation"
                options={relationOptions}
                value={formData?.emergencyContactRelation}
                onChange={(value) => handleInputChange('emergencyContactRelation', value)}
              />
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-border">
          <Button
            variant="ghost"
            onClick={handlePrevious}
            disabled={currentStep === 1}
            iconName="ChevronLeft"
            iconPosition="left"
          >
            Précédent
          </Button>
          
          <div className="flex items-center gap-2">
            {currentStep < steps?.length ? (
              <Button
                variant="default"
                onClick={handleNext}
                iconName="ChevronRight"
                iconPosition="right"
              >
                Suivant
              </Button>
            ) : (
              <Button
                variant="default"
                onClick={handleSubmit}
                iconName="Check"
                iconPosition="left"
              >
                Créer le locataire
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddTenantModal;