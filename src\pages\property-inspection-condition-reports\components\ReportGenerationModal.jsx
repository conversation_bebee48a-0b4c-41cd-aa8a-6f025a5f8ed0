import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import { Checkbox } from '../../../components/ui/Checkbox';

const ReportGenerationModal = ({ isOpen, onClose, inspection, onGenerate }) => {
  const [reportOptions, setReportOptions] = useState({
    includePhotos: true,
    includeInspectorNotes: true,
    includeTenantSignature: true,
    includePropertyDetails: true,
    includeComparisonPreviousReport: false,
    format: 'pdf',
    language: 'fr',
    template: 'standard'
  });

  const [generating, setGenerating] = useState(false);

  const handleOptionChange = (option, value) => {
    setReportOptions(prev => ({
      ...prev,
      [option]: value
    }));
  };

  const handleGenerate = () => {
    setGenerating(true);
    
    // Simulate report generation
    setTimeout(() => {
      onGenerate({
        inspectionId: inspection?.id,
        options: reportOptions,
        generatedAt: new Date()?.toISOString()
      });
      setGenerating(false);
    }, 2000);
  };

  const getInspectionTypeLabel = (type) => {
    const types = {
      entry: 'État des lieux d\'entrée',
      exit: 'État des lieux de sortie',
      periodic: 'Inspection périodique',
      maintenance: 'Inspection maintenance'
    };
    return types?.[type] || type;
  };

  const templates = [
    { value: 'standard', label: 'Modèle standard', description: 'Format classique avec toutes les sections' },
    { value: 'detailed', label: 'Modèle détaillé', description: 'Version complète avec analyses approfondies' },
    { value: 'summary', label: 'Modèle résumé', description: 'Version condensée pour aperçu rapide' },
    { value: 'legal', label: 'Modèle légal', description: 'Conforme aux exigences légales' }
  ];

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div className="absolute inset-0 bg-black/50" onClick={onClose} />
      {/* Modal */}
      <div className="relative bg-card border border-border rounded-lg shadow-lg w-full max-w-3xl mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border">
          <div>
            <h2 className="text-xl font-semibold text-foreground">
              Génération de rapport
            </h2>
            <p className="text-sm text-muted-foreground mt-1">
              {getInspectionTypeLabel(inspection?.type)} - {inspection?.property?.address}
            </p>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <Icon name="X" size={20} />
          </Button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Inspection Summary */}
          <div className="bg-muted/50 rounded-lg p-4 mb-6">
            <h3 className="text-sm font-medium text-foreground mb-3">
              Résumé de l'inspection
            </h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">Date planifiée:</span>
                <span className="ml-2 text-foreground">
                  {new Date(inspection?.scheduledDate)?.toLocaleDateString('fr-FR')} à {inspection?.scheduledTime}
                </span>
              </div>
              <div>
                <span className="text-muted-foreground">Inspecteur:</span>
                <span className="ml-2 text-foreground">
                  {inspection?.inspector?.name}
                </span>
              </div>
              <div>
                <span className="text-muted-foreground">Statut:</span>
                <span className="ml-2 text-foreground">
                  {inspection?.status}
                </span>
              </div>
              <div>
                <span className="text-muted-foreground">Photos:</span>
                <span className="ml-2 text-foreground">
                  {inspection?.photos?.length || 0} photo(s)
                </span>
              </div>
            </div>
          </div>

          {/* Template Selection */}
          <div className="mb-6">
            <h3 className="text-sm font-medium text-foreground mb-3">
              Modèle de rapport
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {templates?.map(template => (
                <div
                  key={template?.value}
                  className={`border rounded-lg p-3 cursor-pointer transition-colors ${
                    reportOptions?.template === template?.value
                      ? 'border-primary bg-primary/5' :'border-border hover:border-primary/50'
                  }`}
                  onClick={() => handleOptionChange('template', template?.value)}
                >
                  <div className="flex items-start space-x-3">
                    <input
                      type="radio"
                      checked={reportOptions?.template === template?.value}
                      onChange={() => handleOptionChange('template', template?.value)}
                      className="mt-1"
                    />
                    <div>
                      <h4 className="text-sm font-medium text-foreground">
                        {template?.label}
                      </h4>
                      <p className="text-xs text-muted-foreground mt-1">
                        {template?.description}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Content Options */}
          <div className="mb-6">
            <h3 className="text-sm font-medium text-foreground mb-3">
              Contenu du rapport
            </h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <Checkbox
                  checked={reportOptions?.includePhotos}
                  onCheckedChange={(value) => handleOptionChange('includePhotos', value)}
                />
                <div>
                  <label className="text-sm font-medium text-foreground">
                    Inclure les photos
                  </label>
                  <p className="text-xs text-muted-foreground">
                    Toutes les photos documentées seront incluses dans le rapport
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Checkbox
                  checked={reportOptions?.includeInspectorNotes}
                  onCheckedChange={(value) => handleOptionChange('includeInspectorNotes', value)}
                />
                <div>
                  <label className="text-sm font-medium text-foreground">
                    Inclure les notes de l'inspecteur
                  </label>
                  <p className="text-xs text-muted-foreground">
                    Commentaires et observations détaillées
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Checkbox
                  checked={reportOptions?.includePropertyDetails}
                  onCheckedChange={(value) => handleOptionChange('includePropertyDetails', value)}
                />
                <div>
                  <label className="text-sm font-medium text-foreground">
                    Inclure les détails de la propriété
                  </label>
                  <p className="text-xs text-muted-foreground">
                    Adresse, caractéristiques et informations du bien
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Checkbox
                  checked={reportOptions?.includeTenantSignature}
                  onCheckedChange={(value) => handleOptionChange('includeTenantSignature', value)}
                />
                <div>
                  <label className="text-sm font-medium text-foreground">
                    Inclure l'espace de signature du locataire
                  </label>
                  <p className="text-xs text-muted-foreground">
                    Zone dédiée pour la signature et validation
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Checkbox
                  checked={reportOptions?.includeComparisonPreviousReport}
                  onCheckedChange={(value) => handleOptionChange('includeComparisonPreviousReport', value)}
                />
                <div>
                  <label className="text-sm font-medium text-foreground">
                    Inclure la comparaison avec le rapport précédent
                  </label>
                  <p className="text-xs text-muted-foreground">
                    Analyse des changements depuis la dernière inspection
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Format Options */}
          <div className="mb-6">
            <h3 className="text-sm font-medium text-foreground mb-3">
              Format et options
            </h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-xs font-medium text-foreground mb-2">
                  Format de sortie
                </label>
                <select
                  value={reportOptions?.format}
                  onChange={(e) => handleOptionChange('format', e?.target?.value)}
                  className="w-full px-3 py-2 text-sm border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
                >
                  <option value="pdf">PDF</option>
                  <option value="word">Word (.docx)</option>
                  <option value="html">HTML</option>
                </select>
              </div>

              <div>
                <label className="block text-xs font-medium text-foreground mb-2">
                  Langue
                </label>
                <select
                  value={reportOptions?.language}
                  onChange={(e) => handleOptionChange('language', e?.target?.value)}
                  className="w-full px-3 py-2 text-sm border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
                >
                  <option value="fr">Français</option>
                  <option value="en">English</option>
                </select>
              </div>
            </div>
          </div>

          {/* Preview Info */}
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <Icon name="Info" size={20} className="text-blue-600 mt-0.5" />
              <div>
                <h4 className="text-sm font-medium text-blue-900 mb-2">
                  Aperçu de génération
                </h4>
                <ul className="text-xs text-blue-800 space-y-1">
                  <li>• Le rapport sera généré au format {reportOptions?.format?.toUpperCase()}</li>
                  <li>• {reportOptions?.includePhotos ? `${inspection?.photos?.length || 0} photos seront incluses` : 'Aucune photo ne sera incluse'}</li>
                  <li>• Modèle: {templates?.find(t => t?.value === reportOptions?.template)?.label}</li>
                  <li>• Le rapport sera téléchargeable une fois généré</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-end space-x-4 p-6 border-t border-border">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={generating}
          >
            Annuler
          </Button>
          <Button
            variant="default"
            onClick={handleGenerate}
            disabled={generating}
          >
            {generating ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Génération...
              </>
            ) : (
              <>
                <Icon name="FileText" size={16} className="mr-2" />
                Générer le rapport
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ReportGenerationModal;