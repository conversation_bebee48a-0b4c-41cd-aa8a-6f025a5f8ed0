import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet';
import AgencyHeader from '../../components/ui/AgencyHeader';
import PrimaryNavigation from '../../components/ui/PrimaryNavigation';
import OwnerStats from './components/OwnerStats';
import OwnerFilters from './components/OwnerFilters';
import OwnerTable from './components/OwnerTable';
import OwnerActionBar from './components/OwnerActionBar';
import AddOwnerModal from './components/AddOwnerModal';

const PropertyOwnerManagement = () => {
  const [owners, setOwners] = useState([]);
  const [filteredOwners, setFilteredOwners] = useState([]);
  const [selectedOwners, setSelectedOwners] = useState([]);
  const [filters, setFilters] = useState({});
  const [searchTerm, setSearchTerm] = useState('');
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [stats, setStats] = useState({});
  const [properties, setProperties] = useState([]);

  // Mock data initialization
  useEffect(() => {
    const mockOwners = [
      {
        id: 'O001',
        firstName: 'Jean-Pierre',
        lastName: 'Durand',
        email: '<EMAIL>',
        phone: '+33 6 12 34 56 78',
        dateOfBirth: '1965-08-15',
        nationality: 'french',
        address: '15 Avenue Montaigne, 75008 Paris',
        city: 'Paris',
        postalCode: '75008',
        profession: 'Entrepreneur',
        iban: 'FR76 3000 6000 0112 3456 7890 189',
        managementRate: 8.5,
        totalProperties: 3,
        totalPortfolioValue: 2850000,
        paymentPreference: 'virement',
        taxOptimization: true,
        registrationDate: '2020-03-15',
        lastContact: '2024-08-15',
        status: 'active',
        propertyTypes: ['Appartement', 'Bureau'],
        locations: ['Paris', 'Neuilly-sur-Seine'],
        averageRent: 2100,
        totalRentCollected: 189000
      },
      {
        id: 'O002',
        firstName: 'Marie-Claire',
        lastName: 'Moreau',
        email: '<EMAIL>',
        phone: '+33 6 23 45 67 89',
        dateOfBirth: '1972-11-22',
        nationality: 'french',
        address: '22 Rue de la République, 69002 Lyon',
        city: 'Lyon',
        postalCode: '69002',
        profession: 'Médecin',
        iban: 'FR76 3000 6000 0198 7654 3210 987',
        managementRate: 7.0,
        totalProperties: 2,
        totalPortfolioValue: 850000,
        paymentPreference: 'cheque',
        taxOptimization: false,
        registrationDate: '2021-07-10',
        lastContact: '2024-09-01',
        status: 'active',
        propertyTypes: ['Appartement'],
        locations: ['Lyon'],
        averageRent: 1200,
        totalRentCollected: 86400
      },
      {
        id: 'O003',
        firstName: 'Philippe',
        lastName: 'Bernard',
        email: '<EMAIL>',
        phone: '+33 6 34 56 78 90',
        dateOfBirth: '1958-04-08',
        nationality: 'french',
        address: '8 Boulevard Carnot, 21000 Dijon',
        city: 'Dijon',
        postalCode: '21000',
        profession: 'Retraité',
        iban: 'FR76 3000 6000 0156 7890 1234 567',
        managementRate: 9.0,
        totalProperties: 5,
        totalPortfolioValue: 1750000,
        paymentPreference: 'virement',
        taxOptimization: true,
        registrationDate: '2019-12-01',
        lastContact: '2024-07-20',
        status: 'active',
        propertyTypes: ['Maison', 'Appartement'],
        locations: ['Dijon', 'Beaune'],
        averageRent: 950,
        totalRentCollected: 228000
      },
      {
        id: 'O004',
        firstName: 'Sophie',
        lastName: 'Lefebvre',
        email: '<EMAIL>',
        phone: '+33 6 45 67 89 01',
        dateOfBirth: '1980-01-18',
        nationality: 'french',
        address: '12 Cours Mirabeau, 13100 Aix-en-Provence',
        city: 'Aix-en-Provence',
        postalCode: '13100',
        profession: 'Avocate',
        iban: 'FR76 3000 6000 0134 5678 9012 345',
        managementRate: 8.0,
        totalProperties: 1,
        totalPortfolioValue: 450000,
        paymentPreference: 'virement',
        taxOptimization: false,
        registrationDate: '2022-02-28',
        lastContact: '2024-08-30',
        status: 'pending',
        propertyTypes: ['Appartement'],
        locations: ['Aix-en-Provence'],
        averageRent: 1400,
        totalRentCollected: 16800
      },
      {
        id: 'O005',
        firstName: 'Alain',
        lastName: 'Roux',
        email: '<EMAIL>',
        phone: '+33 6 56 78 90 12',
        dateOfBirth: '1963-09-25',
        nationality: 'french',
        address: '25 Place Stanislas, 54000 Nancy',
        city: 'Nancy',
        postalCode: '54000',
        profession: 'Ingénieur',
        iban: 'FR76 3000 6000 0178 9012 3456 789',
        managementRate: 7.5,
        totalProperties: 4,
        totalPortfolioValue: 1200000,
        paymentPreference: 'cheque',
        taxOptimization: true,
        registrationDate: '2020-11-15',
        lastContact: '2024-06-15',
        status: 'inactive',
        propertyTypes: ['Appartement', 'Studio'],
        locations: ['Nancy', 'Metz'],
        averageRent: 800,
        totalRentCollected: 153600
      }
    ];

    const mockProperties = [
      { id: 'P001', address: '25 Avenue des Champs', city: 'Paris', type: 'Appartement', ownerId: 'O001' },
      { id: 'P002', address: '12 Rue de la Paix', city: 'Lyon', type: 'Studio', ownerId: 'O002' },
      { id: 'P003', address: '45 Cours Mirabeau', city: 'Marseille', type: 'Maison', ownerId: 'O003' },
      { id: 'P004', address: '18 Place du Capitole', city: 'Toulouse', type: 'Appartement', ownerId: 'O004' },
      { id: 'P005', address: '9 Avenue de la Liberté', city: 'Nice', type: 'Appartement', ownerId: 'O005' }
    ];

    const mockStats = {
      totalOwners: mockOwners?.length,
      activeOwners: mockOwners?.filter(o => o?.status === 'active')?.length,
      totalProperties: mockOwners?.reduce((sum, o) => sum + o?.totalProperties, 0),
      totalPortfolioValue: mockOwners?.reduce((sum, o) => sum + o?.totalPortfolioValue, 0),
      averageManagementRate: mockOwners?.reduce((sum, o) => sum + o?.managementRate, 0) / mockOwners?.length,
      monthlyRentTotal: mockOwners?.reduce((sum, o) => sum + (o?.averageRent * o?.totalProperties), 0)
    };

    setOwners(mockOwners);
    setFilteredOwners(mockOwners);
    setProperties(mockProperties);
    setStats(mockStats);
  }, []);

  // Filter and search logic
  useEffect(() => {
    let filtered = [...owners];

    // Apply search filter
    if (searchTerm) {
      const searchLower = searchTerm?.toLowerCase();
      filtered = filtered?.filter(owner =>
        owner?.firstName?.toLowerCase()?.includes(searchLower) ||
        owner?.lastName?.toLowerCase()?.includes(searchLower) ||
        owner?.email?.toLowerCase()?.includes(searchLower) ||
        owner?.city?.toLowerCase()?.includes(searchLower) ||
        owner?.profession?.toLowerCase()?.includes(searchLower)
      );
    }

    // Apply filters
    Object.entries(filters)?.forEach(([key, value]) => {
      if (value && value !== 'all' && value !== '') {
        switch (key) {
          case 'status':
            filtered = filtered?.filter(owner => owner?.status === value);
            break;
          case 'city':
            filtered = filtered?.filter(owner => owner?.city?.toLowerCase() === value);
            break;
          case 'paymentPreference':
            filtered = filtered?.filter(owner => owner?.paymentPreference === value);
            break;
          case 'minProperties':
            filtered = filtered?.filter(owner => owner?.totalProperties >= parseInt(value));
            break;
          case 'maxProperties':
            filtered = filtered?.filter(owner => owner?.totalProperties <= parseInt(value));
            break;
          case 'minPortfolioValue':
            filtered = filtered?.filter(owner => owner?.totalPortfolioValue >= parseInt(value));
            break;
          case 'maxPortfolioValue':
            filtered = filtered?.filter(owner => owner?.totalPortfolioValue <= parseInt(value));
            break;
          default:
            break;
        }
      }
    });

    setFilteredOwners(filtered);
  }, [owners, filters, searchTerm]);

  const handleFilterChange = (filterType, value) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: value
    }));
  };

  const handleSearchChange = (term) => {
    setSearchTerm(term);
  };

  const handleClearFilters = () => {
    setFilters({});
    setSearchTerm('');
  };

  const handleOwnerSelect = (ownerId, action) => {
    console.log(`Action ${action} for owner ${ownerId}`);
    if (action === 'add') {
      setIsAddModalOpen(true);
    }
    // Handle other actions like view, edit, message
  };

  const handleBulkAction = (action) => {
    console.log(`Bulk action ${action} for owners:`, selectedOwners);
    // Handle bulk actions
  };

  const handleSelectionChange = (selectedIds) => {
    setSelectedOwners(selectedIds);
  };

  const handleAddOwner = () => {
    setIsAddModalOpen(true);
  };

  const handleExport = (format) => {
    console.log(`Exporting data in ${format} format`);
    // Handle export functionality
  };

  const handleAddOwnerSubmit = (ownerData) => {
    const newOwner = {
      ...ownerData,
      id: `O${String(owners?.length + 1)?.padStart(3, '0')}`,
      registrationDate: new Date()?.toISOString()?.split('T')?.[0],
      status: 'pending',
      totalProperties: 0,
      totalPortfolioValue: 0,
      totalRentCollected: 0
    };

    setOwners(prev => [...prev, newOwner]);
    console.log('New owner added:', newOwner);
  };

  const handleLogout = () => {
    console.log('User logged out');
    // Handle logout logic
  };

  return (
    <div className="min-h-screen bg-background">
      <Helmet>
        <title>Gestion des Bailleurs - PropertyFlow</title>
        <meta name="description" content="Gérez efficacement vos propriétaires bailleurs et leurs portefeuilles immobiliers avec PropertyFlow" />
      </Helmet>
      
      {/* Header */}
      <AgencyHeader
        agencyName="PropertyFlow"
        userName="Marie Dubois"
        userRole="Gestionnaire"
        onLogout={handleLogout}
        notificationCount={3}
      />
      
      {/* Navigation */}
      <PrimaryNavigation />
      
      {/* Main Content */}
      <main className="pt-32 pb-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Page Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-foreground mb-2">
              Gestion des Bailleurs
            </h1>
            <p className="text-muted-foreground">
              Gérez vos propriétaires bailleurs, leurs portefeuilles et relations financières
            </p>
          </div>

          {/* Stats */}
          <OwnerStats stats={stats} className="mb-8" />

          {/* Filters */}
          <OwnerFilters
            onFilterChange={handleFilterChange}
            onSearchChange={handleSearchChange}
            onClearFilters={handleClearFilters}
            activeFilters={filters}
            className="mb-6"
          />

          {/* Action Bar */}
          <OwnerActionBar
            onAddOwner={handleAddOwner}
            onExport={handleExport}
            onBulkAction={handleBulkAction}
            selectedCount={selectedOwners?.length}
            totalCount={filteredOwners?.length}
            className="mb-6"
          />

          {/* Owner Table */}
          <OwnerTable
            owners={filteredOwners}
            onOwnerSelect={handleOwnerSelect}
            onBulkAction={handleBulkAction}
            selectedOwners={selectedOwners}
            onSelectionChange={handleSelectionChange}
          />
        </div>
      </main>
      
      {/* Add Owner Modal */}
      <AddOwnerModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onSubmit={handleAddOwnerSubmit}
        properties={properties}
      />
    </div>
  );
};

export default PropertyOwnerManagement;