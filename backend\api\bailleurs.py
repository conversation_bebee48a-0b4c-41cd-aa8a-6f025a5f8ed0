"""
PropertyFlow Backend - API Bailleurs
Gestion des propriétaires/bailleurs
"""

from flask import Blueprint, request, jsonify, g
from flask_jwt_extended import jwt_required
import logging
from database import bailleur_service
from utils.errors import NotFoundError, ValidationError, AuthorizationError
from utils.middleware import require_agence_access, validate_json, log_activity

logger = logging.getLogger(__name__)

# Blueprint pour les bailleurs
bailleurs_bp = Blueprint('bailleurs', __name__)

@bailleurs_bp.route('', methods=['GET'])
@jwt_required()
@require_agence_access
@log_activity('list_bailleurs')
def get_bailleurs():
    """Récupérer tous les bailleurs de l'agence"""
    try:
        # Paramètres de filtrage
        statut = request.args.get('statut')
        type_personne = request.args.get('type_personne')
        
        filters = {'agence_id': g.agence_id}
        if statut:
            filters['statut'] = statut
        if type_personne:
            filters['type_personne'] = type_personne
        
        bailleurs = bailleur_service.get_all(filters=filters)
        
        return jsonify({
            'success': True,
            'data': bailleurs,
            'total': len(bailleurs)
        }), 200
        
    except Exception as e:
        logger.error(f"Get bailleurs error: {str(e)}")
        raise

@bailleurs_bp.route('/<bailleur_id>', methods=['GET'])
@jwt_required()
@require_agence_access
@log_activity('get_bailleur')
def get_bailleur(bailleur_id):
    """Récupérer un bailleur par ID"""
    try:
        bailleur = bailleur_service.get_by_id(bailleur_id)
        if not bailleur:
            raise NotFoundError("Bailleur not found")
        
        # Vérifier que le bailleur appartient à l'agence
        if bailleur['agence_id'] != g.agence_id:
            raise AuthorizationError("Access denied to this bailleur")
        
        return jsonify({
            'success': True,
            'data': bailleur
        }), 200
        
    except Exception as e:
        logger.error(f"Get bailleur error: {str(e)}")
        raise

@bailleurs_bp.route('', methods=['POST'])
@jwt_required()
@require_agence_access
@validate_json(['type_personne', 'nom', 'adresse'])
@log_activity('create_bailleur')
def create_bailleur():
    """Créer un nouveau bailleur"""
    try:
        data = request.get_json()
        
        # Validation selon le type de personne
        if data['type_personne'] == 'physique':
            required_fields = ['nom', 'prenom']
        else:  # morale
            required_fields = ['raison_sociale']
        
        missing_fields = [field for field in required_fields if not data.get(field)]
        if missing_fields:
            raise ValidationError(f"Missing required fields: {', '.join(missing_fields)}")
        
        # Créer le bailleur
        bailleur_data = {
            'agence_id': g.agence_id,
            'type_personne': data['type_personne'],
            'nom': data['nom'],
            'prenom': data.get('prenom'),
            'raison_sociale': data.get('raison_sociale'),
            'adresse': data['adresse'],
            'telephone': data.get('telephone'),
            'email': data.get('email'),
            'date_naissance': data.get('date_naissance'),
            'lieu_naissance': data.get('lieu_naissance'),
            'nationalite': data.get('nationalite'),
            'profession': data.get('profession'),
            'iban_chiffre': data.get('iban_chiffre'),  # TODO: Chiffrer
            'bic': data.get('bic'),
            'banque': data.get('banque'),
            'numero_fiscal': data.get('numero_fiscal'),
            'regime_fiscal': data.get('regime_fiscal'),
            'taux_gerance': data.get('taux_gerance'),
            'mode_paiement_prefere': data.get('mode_paiement_prefere'),
            'documents': data.get('documents', []),
            'notes': data.get('notes'),
            'statut': 'actif'
        }
        
        new_bailleur = bailleur_service.create(bailleur_data)
        
        logger.info(f"New bailleur created: {new_bailleur['id']}")
        
        return jsonify({
            'success': True,
            'message': 'Bailleur created successfully',
            'data': new_bailleur
        }), 201
        
    except Exception as e:
        logger.error(f"Create bailleur error: {str(e)}")
        raise

@bailleurs_bp.route('/<bailleur_id>', methods=['PUT'])
@jwt_required()
@require_agence_access
@validate_json()
@log_activity('update_bailleur')
def update_bailleur(bailleur_id):
    """Mettre à jour un bailleur"""
    try:
        # Vérifier que le bailleur existe et appartient à l'agence
        bailleur = bailleur_service.get_by_id(bailleur_id)
        if not bailleur:
            raise NotFoundError("Bailleur not found")
        
        if bailleur['agence_id'] != g.agence_id:
            raise AuthorizationError("Access denied to this bailleur")
        
        data = request.get_json()
        
        # Champs modifiables
        allowed_fields = [
            'nom', 'prenom', 'raison_sociale', 'adresse', 'telephone', 'email',
            'date_naissance', 'lieu_naissance', 'nationalite', 'profession',
            'iban_chiffre', 'bic', 'banque', 'numero_fiscal', 'regime_fiscal',
            'taux_gerance', 'mode_paiement_prefere', 'documents', 'notes', 'statut'
        ]
        
        update_data = {k: v for k, v in data.items() if k in allowed_fields}
        
        if not update_data:
            raise ValidationError("No valid fields to update")
        
        # TODO: Chiffrer l'IBAN si modifié
        
        updated_bailleur = bailleur_service.update(bailleur_id, update_data)
        
        logger.info(f"Bailleur updated: {bailleur_id}")
        
        return jsonify({
            'success': True,
            'message': 'Bailleur updated successfully',
            'data': updated_bailleur
        }), 200
        
    except Exception as e:
        logger.error(f"Update bailleur error: {str(e)}")
        raise

@bailleurs_bp.route('/<bailleur_id>', methods=['DELETE'])
@jwt_required()
@require_agence_access
@log_activity('delete_bailleur')
def delete_bailleur(bailleur_id):
    """Supprimer un bailleur (soft delete)"""
    try:
        bailleur = bailleur_service.get_by_id(bailleur_id)
        if not bailleur:
            raise NotFoundError("Bailleur not found")
        
        if bailleur['agence_id'] != g.agence_id:
            raise AuthorizationError("Access denied to this bailleur")
        
        # TODO: Vérifier qu'il n'y a pas de biens actifs
        
        # Soft delete - changer le statut
        bailleur_service.update(bailleur_id, {'statut': 'inactif'})
        
        logger.info(f"Bailleur deleted: {bailleur_id}")
        
        return jsonify({
            'success': True,
            'message': 'Bailleur deleted successfully'
        }), 200
        
    except Exception as e:
        logger.error(f"Delete bailleur error: {str(e)}")
        raise

@bailleurs_bp.route('/<bailleur_id>/biens', methods=['GET'])
@jwt_required()
@require_agence_access
@log_activity('get_bailleur_biens')
def get_bailleur_biens(bailleur_id):
    """Récupérer tous les biens d'un bailleur"""
    try:
        bailleur = bailleur_service.get_by_id(bailleur_id)
        if not bailleur:
            raise NotFoundError("Bailleur not found")
        
        if bailleur['agence_id'] != g.agence_id:
            raise AuthorizationError("Access denied to this bailleur")
        
        # TODO: Utiliser bien_service pour récupérer les biens
        biens = []  # bien_service.get_by_bailleur(bailleur_id)
        
        return jsonify({
            'success': True,
            'data': biens,
            'total': len(biens)
        }), 200
        
    except Exception as e:
        logger.error(f"Get bailleur biens error: {str(e)}")
        raise

@bailleurs_bp.route('/<bailleur_id>/revenus', methods=['GET'])
@jwt_required()
@require_agence_access
@log_activity('get_bailleur_revenus')
def get_bailleur_revenus(bailleur_id):
    """Récupérer les revenus d'un bailleur"""
    try:
        bailleur = bailleur_service.get_by_id(bailleur_id)
        if not bailleur:
            raise NotFoundError("Bailleur not found")
        
        if bailleur['agence_id'] != g.agence_id:
            raise AuthorizationError("Access denied to this bailleur")
        
        # Paramètres de période
        annee = request.args.get('annee', type=int)
        mois = request.args.get('mois', type=int)
        
        # TODO: Calculer les vrais revenus
        revenus = {
            'total_loyers': 0,
            'total_charges': 0,
            'total_depenses': 0,
            'frais_gestion': 0,
            'net_a_reverser': 0,
            'periode': f"{annee}-{mois:02d}" if annee and mois else "all"
        }
        
        return jsonify({
            'success': True,
            'data': revenus
        }), 200
        
    except Exception as e:
        logger.error(f"Get bailleur revenus error: {str(e)}")
        raise
