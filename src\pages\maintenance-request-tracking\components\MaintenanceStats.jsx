import React from 'react';
import Icon from '../../../components/AppIcon';

const MaintenanceStats = ({ stats }) => {
  const statCards = [
    {
      title: 'Demandes ouvertes',
      value: stats?.openRequests,
      icon: 'AlertCircle',
      color: 'text-error',
      bgColor: 'bg-error/10',
      change: stats?.openRequestsChange,
      changeType: stats?.openRequestsChange > 0 ? 'increase' : 'decrease'
    },
    {
      title: 'En cours',
      value: stats?.inProgressRequests,
      icon: 'Clock',
      color: 'text-warning',
      bgColor: 'bg-warning/10',
      change: stats?.inProgressRequestsChange,
      changeType: stats?.inProgressRequestsChange > 0 ? 'increase' : 'decrease'
    },
    {
      title: 'Terminées ce mois',
      value: stats?.completedThisMonth,
      icon: 'CheckCircle',
      color: 'text-success',
      bgColor: 'bg-success/10',
      change: stats?.completedThisMonthChange,
      changeType: stats?.completedThisMonthChange > 0 ? 'increase' : 'decrease'
    },
    {
      title: 'Coût total',
      value: `${stats?.totalCost?.toLocaleString('fr-FR')}€`,
      icon: 'Euro',
      color: 'text-primary',
      bgColor: 'bg-primary/10',
      change: stats?.totalCostChange,
      changeType: stats?.totalCostChange > 0 ? 'increase' : 'decrease'
    }
  ];

  const priorityStats = [
    {
      label: 'Urgent',
      count: stats?.urgentRequests,
      color: 'bg-error',
      percentage: (stats?.urgentRequests / stats?.totalRequests) * 100
    },
    {
      label: 'Élevé',
      count: stats?.highRequests,
      color: 'bg-warning',
      percentage: (stats?.highRequests / stats?.totalRequests) * 100
    },
    {
      label: 'Moyen',
      count: stats?.mediumRequests,
      color: 'bg-accent',
      percentage: (stats?.mediumRequests / stats?.totalRequests) * 100
    },
    {
      label: 'Faible',
      count: stats?.lowRequests,
      color: 'bg-muted-foreground',
      percentage: (stats?.lowRequests / stats?.totalRequests) * 100
    }
  ];

  return (
    <div className="space-y-6">
      {/* Main Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {statCards?.map((stat, index) => (
          <div key={index} className="bg-card border border-border rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <div className={`p-2 rounded-lg ${stat?.bgColor}`}>
                <Icon name={stat?.icon} size={20} className={stat?.color} />
              </div>
              {stat?.change !== undefined && (
                <div className={`flex items-center space-x-1 text-xs ${
                  stat?.changeType === 'increase' ? 'text-success' : 'text-error'
                }`}>
                  <Icon 
                    name={stat?.changeType === 'increase' ? 'TrendingUp' : 'TrendingDown'} 
                    size={12} 
                  />
                  <span>{Math.abs(stat?.change)}%</span>
                </div>
              )}
            </div>
            <div>
              <p className="text-2xl font-bold text-foreground mb-1">{stat?.value}</p>
              <p className="text-sm text-muted-foreground">{stat?.title}</p>
            </div>
          </div>
        ))}
      </div>
      {/* Priority Distribution */}
      <div className="bg-card border border-border rounded-lg p-4">
        <h3 className="font-semibold text-foreground mb-4">Répartition par priorité</h3>
        <div className="space-y-3">
          {priorityStats?.map((priority, index) => (
            <div key={index} className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className={`w-3 h-3 rounded-full ${priority?.color}`}></div>
                <span className="text-sm font-medium text-foreground">{priority?.label}</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-24 bg-muted rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full ${priority?.color}`}
                    style={{ width: `${priority?.percentage}%` }}
                  ></div>
                </div>
                <span className="text-sm text-muted-foreground w-8 text-right">
                  {priority?.count}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>
      {/* Response Time Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="bg-card border border-border rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-3">
            <Icon name="Clock" size={20} className="text-primary" />
            <h3 className="font-semibold text-foreground">Temps de réponse moyen</h3>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Urgent:</span>
              <span className="text-sm font-medium text-foreground">{stats?.avgResponseTime?.urgent}h</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Élevé:</span>
              <span className="text-sm font-medium text-foreground">{stats?.avgResponseTime?.high}h</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Moyen:</span>
              <span className="text-sm font-medium text-foreground">{stats?.avgResponseTime?.medium}h</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Faible:</span>
              <span className="text-sm font-medium text-foreground">{stats?.avgResponseTime?.low}h</span>
            </div>
          </div>
        </div>

        <div className="bg-card border border-border rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-3">
            <Icon name="Users" size={20} className="text-primary" />
            <h3 className="font-semibold text-foreground">Prestataires actifs</h3>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Total:</span>
              <span className="text-sm font-medium text-foreground">{stats?.activeContractors}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Disponibles:</span>
              <span className="text-sm font-medium text-success">{stats?.availableContractors}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Occupés:</span>
              <span className="text-sm font-medium text-warning">{stats?.busyContractors}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Taux satisfaction:</span>
              <span className="text-sm font-medium text-foreground">{stats?.satisfactionRate}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MaintenanceStats;