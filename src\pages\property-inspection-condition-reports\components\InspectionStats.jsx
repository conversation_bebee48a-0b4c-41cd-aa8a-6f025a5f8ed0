import React, { useMemo } from 'react';
import Icon from '../../../components/AppIcon';

const InspectionStats = ({ inspections, className = "" }) => {
  const stats = React.useMemo(() => {
    const total = inspections?.length || 0;
    const scheduled = inspections?.filter(i => i?.status === 'scheduled')?.length || 0;
    const completed = inspections?.filter(i => i?.status === 'completed')?.length || 0;
    const overdue = inspections?.filter(i => i?.status === 'overdue')?.length || 0;
    const inProgress = inspections?.filter(i => i?.status === 'in-progress')?.length || 0;

    const completionRate = total > 0 ? Math.round((completed / total) * 100) : 0;
    
    return {
      total,
      scheduled,
      completed,
      overdue,
      inProgress,
      completionRate
    };
  }, [inspections]);

  const statCards = [
    {
      title: 'Total Inspections',
      value: stats?.total,
      icon: 'ClipboardList',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      change: null
    },
    {
      title: 'Planifiées',
      value: stats?.scheduled,
      icon: 'Calendar',
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
      change: null
    },
    {
      title: 'Terminées',
      value: stats?.completed,
      icon: 'CheckCircle',
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      change: `${stats?.completionRate}%`
    },
    {
      title: 'En retard',
      value: stats?.overdue,
      icon: 'AlertTriangle',
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      change: null
    },
    {
      title: 'En cours',
      value: stats?.inProgress,
      icon: 'Clock',
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      change: null
    }
  ];

  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 ${className}`}>
      {statCards?.map((stat, index) => (
        <div
          key={index}
          className="bg-card border border-border rounded-lg p-4 transition-smooth hover:shadow-md"
        >
          <div className="flex items-center justify-between mb-3">
            <div className={`p-2 rounded-lg ${stat?.bgColor}`}>
              <Icon name={stat?.icon} size={20} className={stat?.color} />
            </div>
            {stat?.change && (
              <span className="text-sm text-muted-foreground">
                {stat?.change}
              </span>
            )}
          </div>
          
          <div>
            <p className="text-2xl font-bold text-foreground mb-1">
              {stat?.value}
            </p>
            <p className="text-sm text-muted-foreground">
              {stat?.title}
            </p>
          </div>
        </div>
      ))}
    </div>
  );
};

export default InspectionStats;