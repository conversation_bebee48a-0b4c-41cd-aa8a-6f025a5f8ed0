import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet';
import AgencyHeader from '../../components/ui/AgencyHeader';
import PrimaryNavigation from '../../components/ui/PrimaryNavigation';
import ContractStats from './components/ContractStats';
import ContractFilters from './components/ContractFilters';
import ContractTable from './components/ContractTable';
import ContractActionBar from './components/ContractActionBar';
import CreateContractModal from './components/CreateContractModal';

const ContractManagementSystem = () => {
  const [contracts, setContracts] = useState([]);
  const [filteredContracts, setFilteredContracts] = useState([]);
  const [selectedContracts, setSelectedContracts] = useState([]);
  const [filters, setFilters] = useState({});
  const [searchTerm, setSearchTerm] = useState('');
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [stats, setStats] = useState({});
  const [properties, setProperties] = useState([]);
  const [owners, setOwners] = useState([]);

  // Mock data initialization
  useEffect(() => {
    const mockContracts = [
      {
        id: 'C001',
        contractNumber: 'BAIL-2024-001',
        type: 'lease',
        status: 'active',
        title: 'Bail d\'habitation - 25 Avenue des Champs',
        property: {
          id: 'P001',
          address: '25 Avenue des Champs',
          city: 'Paris',
          type: 'Appartement'
        },
        owner: {
          id: 'O001',
          name: 'Jean-Pierre Durand',
          email: '<EMAIL>'
        },
        tenant: {
          id: 'T001',
          name: 'Marie Dubois',
          email: '<EMAIL>'
        },
        startDate: '2024-01-01',
        endDate: '2025-12-31',
        renewalDate: '2025-10-31',
        monthlyRent: 1800,
        securityDeposit: 3600,
        charges: 150,
        duration: 24,
        renewalType: 'automatic',
        complianceStatus: 'compliant',
        digitalSignature: true,
        lastModified: '2024-08-15',
        createdBy: 'Marie Gestionnaire',
        documentsCount: 5,
        clauses: ['Clause résiliation', 'Clause réparations', 'Clause assurance']
      },
      {
        id: 'C002',
        contractNumber: 'GESTION-2024-002',
        type: 'management',
        status: 'active',
        title: 'Mandat de gestion - Portfolio Moreau',
        property: {
          id: 'P002',
          address: '12 Rue de la Paix',
          city: 'Lyon',
          type: 'Studio'
        },
        owner: {
          id: 'O002',
          name: 'Marie-Claire Moreau',
          email: '<EMAIL>'
        },
        tenant: null,
        startDate: '2021-07-01',
        endDate: '2024-06-30',
        renewalDate: '2024-04-30',
        monthlyRent: 0,
        managementFee: 7.0,
        duration: 36,
        renewalType: 'manual',
        complianceStatus: 'expiring_soon',
        digitalSignature: true,
        lastModified: '2024-09-01',
        createdBy: 'Pierre Admin',
        documentsCount: 8,
        clauses: ['Clause exclusivité', 'Clause rémunération', 'Clause résiliation']
      },
      {
        id: 'C003',
        contractNumber: 'SERVICE-2024-003',
        type: 'service',
        status: 'pending',
        title: 'Contrat de maintenance - Copropriété Bernard',
        property: {
          id: 'P003',
          address: '45 Cours Mirabeau',
          city: 'Marseille',
          type: 'Maison'
        },
        owner: {
          id: 'O003',
          name: 'Philippe Bernard',
          email: '<EMAIL>'
        },
        serviceProvider: {
          id: 'S001',
          name: 'Maintenance Pro',
          email: '<EMAIL>'
        },
        startDate: '2024-10-01',
        endDate: '2025-09-30',
        renewalDate: '2025-07-31',
        monthlyFee: 450,
        duration: 12,
        renewalType: 'automatic',
        complianceStatus: 'pending_review',
        digitalSignature: false,
        lastModified: '2024-09-15',
        createdBy: 'Sophie Gestionnaire',
        documentsCount: 3,
        clauses: ['Clause intervention', 'Clause urgence', 'Clause facturation']
      },
      {
        id: 'C004',
        contractNumber: 'BAIL-2023-004',
        type: 'lease',
        status: 'expired',
        title: 'Bail commercial - Place du Capitole',
        property: {
          id: 'P004',
          address: '18 Place du Capitole',
          city: 'Toulouse',
          type: 'Bureau'
        },
        owner: {
          id: 'O004',
          name: 'Sophie Lefebvre',
          email: '<EMAIL>'
        },
        tenant: {
          id: 'T004',
          name: 'Thomas Leroy',
          email: '<EMAIL>'
        },
        startDate: '2023-01-01',
        endDate: '2024-08-31',
        renewalDate: '2024-06-30',
        monthlyRent: 2400,
        securityDeposit: 7200,
        charges: 300,
        duration: 20,
        renewalType: 'manual',
        complianceStatus: 'expired',
        digitalSignature: true,
        lastModified: '2024-08-31',
        createdBy: 'Marie Gestionnaire',
        documentsCount: 7,
        clauses: ['Clause indexation', 'Clause destination', 'Clause cession']
      },
      {
        id: 'C005',
        contractNumber: 'BAIL-2024-005',
        type: 'lease',
        status: 'draft',
        title: 'Bail d\'habitation - Avenue de la Liberté',
        property: {
          id: 'P005',
          address: '9 Avenue de la Liberté',
          city: 'Nice',
          type: 'Appartement'
        },
        owner: {
          id: 'O005',
          name: 'Alain Roux',
          email: '<EMAIL>'
        },
        tenant: {
          id: 'T005',
          name: 'Sophie Laurent',
          email: '<EMAIL>'
        },
        startDate: '2024-11-01',
        endDate: '2025-10-31',
        renewalDate: '2025-08-31',
        monthlyRent: 2100,
        securityDeposit: 4200,
        charges: 200,
        duration: 12,
        renewalType: 'automatic',
        complianceStatus: 'draft',
        digitalSignature: false,
        lastModified: '2024-09-20',
        createdBy: 'Pierre Admin',
        documentsCount: 2,
        clauses: ['Clause standard']
      }
    ];

    const mockStats = {
      totalContracts: mockContracts?.length,
      activeContracts: mockContracts?.filter(c => c?.status === 'active')?.length,
      expiringContracts: mockContracts?.filter(c => c?.complianceStatus === 'expiring_soon')?.length,
      pendingContracts: mockContracts?.filter(c => c?.status === 'pending')?.length,
      digitalSignatureRate: (mockContracts?.filter(c => c?.digitalSignature)?.length / mockContracts?.length * 100),
      complianceRate: (mockContracts?.filter(c => c?.complianceStatus === 'compliant')?.length / mockContracts?.length * 100),
      averageContractValue: mockContracts?.reduce((sum, c) => sum + (c?.monthlyRent || c?.monthlyFee || 0), 0) / mockContracts?.length,
      renewalsThisMonth: 2
    };

    const mockProperties = [
      { id: 'P001', address: '25 Avenue des Champs', city: 'Paris', type: 'Appartement' },
      { id: 'P002', address: '12 Rue de la Paix', city: 'Lyon', type: 'Studio' },
      { id: 'P003', address: '45 Cours Mirabeau', city: 'Marseille', type: 'Maison' },
      { id: 'P004', address: '18 Place du Capitole', city: 'Toulouse', type: 'Bureau' },
      { id: 'P005', address: '9 Avenue de la Liberté', city: 'Nice', type: 'Appartement' }
    ];

    const mockOwners = [
      { id: 'O001', name: 'Jean-Pierre Durand', email: '<EMAIL>' },
      { id: 'O002', name: 'Marie-Claire Moreau', email: '<EMAIL>' },
      { id: 'O003', name: 'Philippe Bernard', email: '<EMAIL>' },
      { id: 'O004', name: 'Sophie Lefebvre', email: '<EMAIL>' },
      { id: 'O005', name: 'Alain Roux', email: '<EMAIL>' }
    ];

    setContracts(mockContracts);
    setFilteredContracts(mockContracts);
    setStats(mockStats);
    setProperties(mockProperties);
    setOwners(mockOwners);
  }, []);

  // Filter and search logic
  useEffect(() => {
    let filtered = [...contracts];

    // Apply search filter
    if (searchTerm) {
      const searchLower = searchTerm?.toLowerCase();
      filtered = filtered?.filter(contract =>
        contract?.title?.toLowerCase()?.includes(searchLower) ||
        contract?.contractNumber?.toLowerCase()?.includes(searchLower) ||
        contract?.owner?.name?.toLowerCase()?.includes(searchLower) ||
        contract?.tenant?.name?.toLowerCase()?.includes(searchLower) ||
        contract?.property?.address?.toLowerCase()?.includes(searchLower)
      );
    }

    // Apply filters
    Object.entries(filters)?.forEach(([key, value]) => {
      if (value && value !== 'all' && value !== '') {
        switch (key) {
          case 'type':
            filtered = filtered?.filter(contract => contract?.type === value);
            break;
          case 'status':
            filtered = filtered?.filter(contract => contract?.status === value);
            break;
          case 'complianceStatus':
            filtered = filtered?.filter(contract => contract?.complianceStatus === value);
            break;
          case 'renewalType':
            filtered = filtered?.filter(contract => contract?.renewalType === value);
            break;
          case 'city':
            filtered = filtered?.filter(contract => contract?.property?.city?.toLowerCase() === value);
            break;
          case 'expiringBefore':
            filtered = filtered?.filter(contract => 
              new Date(contract.endDate) <= new Date(value)
            );
            break;
          default:
            break;
        }
      }
    });

    setFilteredContracts(filtered);
  }, [contracts, filters, searchTerm]);

  const handleFilterChange = (filterType, value) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: value
    }));
  };

  const handleSearchChange = (term) => {
    setSearchTerm(term);
  };

  const handleClearFilters = () => {
    setFilters({});
    setSearchTerm('');
  };

  const handleContractSelect = (contractId, action) => {
    console.log(`Action ${action} for contract ${contractId}`);
    if (action === 'create') {
      setIsCreateModalOpen(true);
    }
    // Handle other actions like view, edit, sign, renew
  };

  const handleBulkAction = (action) => {
    console.log(`Bulk action ${action} for contracts:`, selectedContracts);
    // Handle bulk actions
  };

  const handleSelectionChange = (selectedIds) => {
    setSelectedContracts(selectedIds);
  };

  const handleCreateContract = () => {
    setIsCreateModalOpen(true);
  };

  const handleExport = (format) => {
    console.log(`Exporting data in ${format} format`);
    // Handle export functionality
  };

  const handleCreateContractSubmit = (contractData) => {
    const newContract = {
      ...contractData,
      id: `C${String(contracts?.length + 1)?.padStart(3, '0')}`,
      contractNumber: `${contractData?.type?.toUpperCase()}-${new Date()?.getFullYear()}-${String(contracts?.length + 1)?.padStart(3, '0')}`,
      status: 'draft',
      complianceStatus: 'draft',
      digitalSignature: false,
      lastModified: new Date()?.toISOString()?.split('T')?.[0],
      createdBy: 'Current User',
      documentsCount: 1
    };

    setContracts(prev => [...prev, newContract]);
    console.log('New contract created:', newContract);
  };

  const handleLogout = () => {
    console.log('User logged out');
    // Handle logout logic
  };

  return (
    <div className="min-h-screen bg-background">
      <Helmet>
        <title>Gestion des Contrats - PropertyFlow</title>
        <meta name="description" content="Gérez efficacement tous vos contrats de bail, mandats et accords avec PropertyFlow" />
      </Helmet>
      
      {/* Header */}
      <AgencyHeader
        agencyName="PropertyFlow"
        userName="Marie Dubois"
        userRole="Gestionnaire"
        onLogout={handleLogout}
        notificationCount={3}
      />
      
      {/* Navigation */}
      <PrimaryNavigation />
      
      {/* Main Content */}
      <main className="pt-32 pb-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Page Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-foreground mb-2">
              Gestion des Contrats
            </h1>
            <p className="text-muted-foreground">
              Gérez tous vos contrats de bail, mandats de gestion et accords de service
            </p>
          </div>

          {/* Stats */}
          <ContractStats stats={stats} className="mb-8" />

          {/* Filters */}
          <ContractFilters
            onFilterChange={handleFilterChange}
            onSearchChange={handleSearchChange}
            onClearFilters={handleClearFilters}
            activeFilters={filters}
            className="mb-6"
          />

          {/* Action Bar */}
          <ContractActionBar
            onCreateContract={handleCreateContract}
            onExport={handleExport}
            onBulkAction={handleBulkAction}
            selectedCount={selectedContracts?.length}
            totalCount={filteredContracts?.length}
            className="mb-6"
          />

          {/* Contract Table */}
          <ContractTable
            contracts={filteredContracts}
            onContractSelect={handleContractSelect}
            onBulkAction={handleBulkAction}
            selectedContracts={selectedContracts}
            onSelectionChange={handleSelectionChange}
          />
        </div>
      </main>
      
      {/* Create Contract Modal */}
      <CreateContractModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={handleCreateContractSubmit}
        properties={properties}
        owners={owners}
      />
    </div>
  );
};

export default ContractManagementSystem;