"""
PropertyFlow Backend - API Utilisateurs
Gestion des utilisateurs avec RBAC
"""

from flask import Blueprint, request, jsonify, g
from flask_jwt_extended import jwt_required
import bcrypt
import logging
from database import utilisateur_service
from utils.errors import NotFoundError, ValidationError, AuthorizationError, ConflictError
from utils.middleware import require_role, require_agence_access, validate_json, log_activity

logger = logging.getLogger(__name__)

# Blueprint pour les utilisateurs
utilisateurs_bp = Blueprint('utilisateurs', __name__)

@utilisateurs_bp.route('', methods=['GET'])
@jwt_required()
@require_agence_access
@log_activity('list_users')
def get_utilisateurs():
    """Récupérer tous les utilisateurs de l'agence"""
    try:
        utilisateurs = utilisateur_service.get_by_agence(g.agence_id)
        
        # Masquer les mots de passe
        for user in utilisateurs:
            user.pop('mot_de_passe_hash', None)
        
        return jsonify({
            'success': True,
            'data': utilisateurs,
            'total': len(utilisateurs)
        }), 200
        
    except Exception as e:
        logger.error(f"Get users error: {str(e)}")
        raise

@utilisateurs_bp.route('/<user_id>', methods=['GET'])
@jwt_required()
@require_agence_access
@log_activity('get_user')
def get_utilisateur(user_id):
    """Récupérer un utilisateur par ID"""
    try:
        user = utilisateur_service.get_by_id(user_id)
        if not user:
            raise NotFoundError("User not found")
        
        # Vérifier que l'utilisateur appartient à la même agence
        if user['agence_id'] != g.agence_id:
            raise AuthorizationError("Access denied to this user")
        
        # Masquer le mot de passe
        user.pop('mot_de_passe_hash', None)
        
        return jsonify({
            'success': True,
            'data': user
        }), 200
        
    except Exception as e:
        logger.error(f"Get user error: {str(e)}")
        raise

@utilisateurs_bp.route('', methods=['POST'])
@jwt_required()
@require_role('super_admin', 'admin_agence')
@require_agence_access
@validate_json(['email', 'password', 'nom', 'prenom', 'role'])
@log_activity('create_user')
def create_utilisateur():
    """Créer un nouvel utilisateur"""
    try:
        data = request.get_json()
        email = data['email'].lower().strip()
        
        # Vérifier si l'utilisateur existe déjà
        existing_user = utilisateur_service.get_by_email(email)
        if existing_user:
            raise ConflictError("User already exists")
        
        # Vérifier les permissions de rôle
        requested_role = data['role']
        if g.user_role == 'admin_agence' and requested_role in ['super_admin', 'admin_agence']:
            raise AuthorizationError("Cannot create users with admin roles")
        
        # Hasher le mot de passe
        password_hash = bcrypt.hashpw(
            data['password'].encode('utf-8'), 
            bcrypt.gensalt()
        ).decode('utf-8')
        
        # Créer l'utilisateur
        user_data = {
            'email': email,
            'mot_de_passe_hash': password_hash,
            'nom': data['nom'],
            'prenom': data['prenom'],
            'agence_id': g.agence_id,
            'role': requested_role,
            'telephone': data.get('telephone'),
            'permissions': data.get('permissions', {}),
            'statut': 'actif'
        }
        
        new_user = utilisateur_service.create(user_data)
        
        # Masquer le mot de passe
        new_user.pop('mot_de_passe_hash', None)
        
        logger.info(f"New user created: {new_user['id']}")
        
        return jsonify({
            'success': True,
            'message': 'User created successfully',
            'data': new_user
        }), 201
        
    except Exception as e:
        logger.error(f"Create user error: {str(e)}")
        raise

@utilisateurs_bp.route('/<user_id>', methods=['PUT'])
@jwt_required()
@require_agence_access
@validate_json()
@log_activity('update_user')
def update_utilisateur(user_id):
    """Mettre à jour un utilisateur"""
    try:
        # Vérifier que l'utilisateur existe et appartient à l'agence
        user = utilisateur_service.get_by_id(user_id)
        if not user:
            raise NotFoundError("User not found")
        
        if user['agence_id'] != g.agence_id:
            raise AuthorizationError("Access denied to this user")
        
        data = request.get_json()
        
        # Vérifier les permissions
        if g.user_role not in ['super_admin', 'admin_agence']:
            # Les utilisateurs normaux ne peuvent modifier que leurs propres infos
            if user_id != g.current_user_id:
                raise AuthorizationError("Can only update your own profile")
        
        # Champs modifiables selon le rôle
        if g.user_role in ['super_admin', 'admin_agence']:
            allowed_fields = ['nom', 'prenom', 'telephone', 'role', 'permissions', 'statut']
        else:
            allowed_fields = ['nom', 'prenom', 'telephone']
        
        # Vérifier les permissions de rôle
        if 'role' in data:
            if g.user_role == 'admin_agence' and data['role'] in ['super_admin', 'admin_agence']:
                raise AuthorizationError("Cannot assign admin roles")
        
        update_data = {k: v for k, v in data.items() if k in allowed_fields}
        
        if not update_data:
            raise ValidationError("No valid fields to update")
        
        updated_user = utilisateur_service.update(user_id, update_data)
        
        # Masquer le mot de passe
        updated_user.pop('mot_de_passe_hash', None)
        
        logger.info(f"User updated: {user_id}")
        
        return jsonify({
            'success': True,
            'message': 'User updated successfully',
            'data': updated_user
        }), 200
        
    except Exception as e:
        logger.error(f"Update user error: {str(e)}")
        raise

@utilisateurs_bp.route('/<user_id>', methods=['DELETE'])
@jwt_required()
@require_role('super_admin', 'admin_agence')
@require_agence_access
@log_activity('delete_user')
def delete_utilisateur(user_id):
    """Supprimer un utilisateur (soft delete)"""
    try:
        user = utilisateur_service.get_by_id(user_id)
        if not user:
            raise NotFoundError("User not found")
        
        if user['agence_id'] != g.agence_id:
            raise AuthorizationError("Access denied to this user")
        
        # Empêcher la suppression de soi-même
        if user_id == g.current_user_id:
            raise ValidationError("Cannot delete your own account")
        
        # Soft delete - changer le statut
        utilisateur_service.update(user_id, {'statut': 'inactif'})
        
        logger.info(f"User deleted: {user_id}")
        
        return jsonify({
            'success': True,
            'message': 'User deleted successfully'
        }), 200
        
    except Exception as e:
        logger.error(f"Delete user error: {str(e)}")
        raise

@utilisateurs_bp.route('/<user_id>/reset-password', methods=['POST'])
@jwt_required()
@require_role('super_admin', 'admin_agence')
@require_agence_access
@validate_json(['new_password'])
@log_activity('reset_user_password')
def reset_password(user_id):
    """Réinitialiser le mot de passe d'un utilisateur"""
    try:
        user = utilisateur_service.get_by_id(user_id)
        if not user:
            raise NotFoundError("User not found")
        
        if user['agence_id'] != g.agence_id:
            raise AuthorizationError("Access denied to this user")
        
        data = request.get_json()
        
        # Hasher le nouveau mot de passe
        new_password_hash = bcrypt.hashpw(
            data['new_password'].encode('utf-8'), 
            bcrypt.gensalt()
        ).decode('utf-8')
        
        # Mettre à jour
        utilisateur_service.update(user_id, {
            'mot_de_passe_hash': new_password_hash
        })
        
        logger.info(f"Password reset for user {user_id}")
        
        return jsonify({
            'success': True,
            'message': 'Password reset successfully'
        }), 200
        
    except Exception as e:
        logger.error(f"Reset password error: {str(e)}")
        raise
