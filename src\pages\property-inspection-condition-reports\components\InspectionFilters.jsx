import React from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';
import Select from '../../../components/ui/Select';

const InspectionFilters = ({ 
  isOpen, 
  onToggle, 
  filters, 
  onFiltersChange, 
  onClearFilters,
  className = ""
}) => {
  const handleFilterChange = (key, value) => {
    onFiltersChange(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleDateRangeChange = (key, value) => {
    onFiltersChange(prev => ({
      ...prev,
      dateRange: {
        ...prev?.dateRange,
        [key]: value
      }
    }));
  };

  const inspectionTypeOptions = [
    { value: '', label: 'Tous les types' },
    { value: 'entry', label: 'État des lieux d\'entrée' },
    { value: 'exit', label: 'État des lieux de sortie' },
    { value: 'periodic', label: 'Inspection périodique' },
    { value: 'maintenance', label: 'Inspection maintenance' }
  ];

  const statusOptions = [
    { value: '', label: 'Tous les statuts' },
    { value: 'scheduled', label: 'Planifiée' },
    { value: 'in-progress', label: 'En cours' },
    { value: 'completed', label: 'Terminée' },
    { value: 'overdue', label: 'En retard' },
    { value: 'cancelled', label: 'Annulée' }
  ];

  const priorityOptions = [
    { value: '', label: 'Toutes les priorités' },
    { value: 'low', label: 'Basse' },
    { value: 'medium', label: 'Moyenne' },
    { value: 'high', label: 'Haute' }
  ];

  const hasActiveFilters = Object.entries(filters)?.some(([key, value]) => {
    if (key === 'dateRange') {
      return value?.start !== '' || value?.end !== '';
    }
    return value !== '' && value !== false;
  });

  if (!isOpen) return null;

  return (
    <div className={`bg-card border border-border rounded-lg p-6 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-foreground">Filtres</h3>
        <div className="flex items-center space-x-2">
          {hasActiveFilters && (
            <Button
              variant="outline"
              size="sm"
              onClick={onClearFilters}
              iconName="X"
              iconPosition="left"
            >
              Effacer
            </Button>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggle}
          >
            <Icon name="X" size={16} />
          </Button>
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Type d'inspection */}
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            Type d'inspection
          </label>
          <Select
            value={filters?.inspectionType}
            onValueChange={(value) => handleFilterChange('inspectionType', value)}
            options={inspectionTypeOptions}
            placeholder="Sélectionner un type"
          />
        </div>

        {/* Statut */}
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            Statut
          </label>
          <Select
            value={filters?.status}
            onValueChange={(value) => handleFilterChange('status', value)}
            options={statusOptions}
            placeholder="Sélectionner un statut"
          />
        </div>

        {/* Priorité */}
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            Priorité
          </label>
          <Select
            value={filters?.priority}
            onValueChange={(value) => handleFilterChange('priority', value)}
            options={priorityOptions}
            placeholder="Sélectionner une priorité"
          />
        </div>

        {/* Inspecteur */}
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            Inspecteur
          </label>
          <Input
            type="text"
            placeholder="Nom de l'inspecteur"
            value={filters?.inspector}
            onChange={(e) => handleFilterChange('inspector', e?.target?.value)}
          />
        </div>

        {/* Date de début */}
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            Date de début
          </label>
          <Input
            type="date"
            value={filters?.dateRange?.start}
            onChange={(e) => handleDateRangeChange('start', e?.target?.value)}
          />
        </div>

        {/* Date de fin */}
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            Date de fin
          </label>
          <Input
            type="date"
            value={filters?.dateRange?.end}
            onChange={(e) => handleDateRangeChange('end', e?.target?.value)}
          />
        </div>
      </div>
      {/* Filter Summary */}
      {hasActiveFilters && (
        <div className="mt-4 pt-4 border-t border-border">
          <p className="text-sm text-muted-foreground">
            {Object.entries(filters)?.filter(([key, value]) => {
              if (key === 'dateRange') return value?.start || value?.end;
              return value !== '' && value !== false;
            })?.length} filtre(s) actif(s)
          </p>
        </div>
      )}
    </div>
  );
};

export default InspectionFilters;