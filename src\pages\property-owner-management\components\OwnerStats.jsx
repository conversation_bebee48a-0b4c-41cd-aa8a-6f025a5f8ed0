import React from 'react';
import Icon from '../../../components/AppIcon';

const OwnerStats = ({ stats = {}, className = "" }) => {
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    })?.format(amount);
  };

  const formatNumber = (number) => {
    return new Intl.NumberFormat('fr-FR')?.format(number);
  };

  const statsData = [
    {
      title: 'Total Bailleurs',
      value: stats?.totalOwners || 0,
      icon: 'UserCheck',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      change: '+5.2%',
      changeType: 'positive'
    },
    {
      title: 'Bailleurs Actifs',
      value: stats?.activeOwners || 0,
      icon: 'Users',
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      change: '+2.1%',
      changeType: 'positive'
    },
    {
      title: 'Propriétés Gérées',
      value: stats?.totalProperties || 0,
      icon: 'Building2',
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      change: '+8.7%',
      changeType: 'positive'
    },
    {
      title: 'Valeur Portefeuille',
      value: formatCurrency(stats?.totalPortfolioValue || 0),
      icon: 'TrendingUp',
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      change: '+12.3%',
      changeType: 'positive'
    },
    {
      title: 'Taux Gérance Moyen',
      value: `${(stats?.averageManagementRate || 0)?.toFixed(1)}%`,
      icon: 'Percent',
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-50',
      change: '+0.3%',
      changeType: 'positive'
    },
    {
      title: 'Loyers Mensuels',
      value: formatCurrency(stats?.monthlyRentTotal || 0),
      icon: 'Euro',
      color: 'text-emerald-600',
      bgColor: 'bg-emerald-50',
      change: '+6.8%',
      changeType: 'positive'
    }
  ];

  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 ${className}`}>
      {statsData?.map((stat, index) => (
        <div
          key={index}
          className="bg-card rounded-lg p-6 border border-border hover:shadow-md transition-shadow duration-200"
        >
          <div className="flex items-center justify-between mb-4">
            <div className={`w-12 h-12 ${stat?.bgColor} rounded-lg flex items-center justify-center`}>
              <Icon name={stat?.icon} size={24} className={stat?.color} />
            </div>
            <div className={`flex items-center text-xs font-medium ${
              stat?.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
            }`}>
              <Icon 
                name={stat?.changeType === 'positive' ? 'TrendingUp' : 'TrendingDown'} 
                size={12} 
                className="mr-1" 
              />
              {stat?.change}
            </div>
          </div>
          
          <div className="space-y-1">
            <p className="text-2xl font-bold text-foreground">
              {typeof stat?.value === 'string' ? stat?.value : formatNumber(stat?.value)}
            </p>
            <p className="text-sm text-muted-foreground font-medium">
              {stat?.title}
            </p>
          </div>
        </div>
      ))}
    </div>
  );
};

export default OwnerStats;