import React from 'react';
import { Line<PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import Icon from '../../../components/AppIcon';

const RevenueChart = ({ className = "" }) => {
  const monthlyData = [
    { month: 'Jan', revenue: 45000, expenses: 12000, properties: 28 },
    { month: 'Fév', revenue: 48000, expenses: 13500, properties: 29 },
    { month: 'Mar', revenue: 52000, expenses: 14200, properties: 31 },
    { month: 'Avr', revenue: 49000, expenses: 13800, properties: 30 },
    { month: 'Mai', revenue: 55000, expenses: 15000, properties: 33 },
    { month: 'Jun', revenue: 58000, expenses: 15500, properties: 34 },
    { month: 'Jul', revenue: 61000, expenses: 16200, properties: 36 },
    { month: 'Aoû', revenue: 59000, expenses: 15800, properties: 35 },
    { month: 'Sep', revenue: 62000, expenses: 16500, properties: 37 }
  ];

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    })?.format(value);
  };

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload?.length) {
      return (
        <div className="bg-popover border border-border rounded-lg p-3 elevation-2">
          <p className="font-medium text-popover-foreground mb-2">{label} 2025</p>
          {payload?.map((entry, index) => (
            <div key={index} className="flex items-center space-x-2 text-sm">
              <div 
                className="w-3 h-3 rounded-full" 
                style={{ backgroundColor: entry?.color }}
              />
              <span className="text-muted-foreground">{entry?.name}:</span>
              <span className="font-data font-medium text-popover-foreground">
                {entry?.name === 'Propriétés' ? entry?.value : formatCurrency(entry?.value)}
              </span>
            </div>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <div className={`bg-card border border-border rounded-lg ${className}`}>
      <div className="p-6 border-b border-border">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-foreground">Évolution des revenus</h3>
            <p className="text-sm text-muted-foreground">Revenus et dépenses mensuels</p>
          </div>
          <div className="flex items-center space-x-2">
            <button className="flex items-center space-x-1 px-3 py-1 text-xs bg-primary/10 text-primary rounded-full">
              <Icon name="TrendingUp" size={12} />
              <span>+12,5%</span>
            </button>
          </div>
        </div>
      </div>
      
      <div className="p-6">
        <div className="h-80 w-full">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={monthlyData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="var(--color-border)" />
              <XAxis 
                dataKey="month" 
                stroke="var(--color-muted-foreground)"
                fontSize={12}
              />
              <YAxis 
                stroke="var(--color-muted-foreground)"
                fontSize={12}
                tickFormatter={formatCurrency}
              />
              <Tooltip content={<CustomTooltip />} />
              <Line 
                type="monotone" 
                dataKey="revenue" 
                stroke="var(--color-primary)" 
                strokeWidth={3}
                name="Revenus"
                dot={{ fill: 'var(--color-primary)', strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6, stroke: 'var(--color-primary)', strokeWidth: 2 }}
              />
              <Line 
                type="monotone" 
                dataKey="expenses" 
                stroke="var(--color-error)" 
                strokeWidth={2}
                name="Dépenses"
                dot={{ fill: 'var(--color-error)', strokeWidth: 2, r: 3 }}
                strokeDasharray="5 5"
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
        
        <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-4 bg-primary/5 rounded-lg">
            <p className="text-sm text-muted-foreground mb-1">Revenus moyens</p>
            <p className="text-xl font-bold text-primary font-data">54 333 €</p>
          </div>
          <div className="text-center p-4 bg-error/5 rounded-lg">
            <p className="text-sm text-muted-foreground mb-1">Dépenses moyennes</p>
            <p className="text-xl font-bold text-error font-data">14 733 €</p>
          </div>
          <div className="text-center p-4 bg-success/5 rounded-lg">
            <p className="text-sm text-muted-foreground mb-1">Bénéfice moyen</p>
            <p className="text-xl font-bold text-success font-data">39 600 €</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RevenueChart;