import React from 'react';
import Icon from '../../../components/AppIcon';

const PlatformLogo = () => {
  return (
    <div className="text-center mb-8">
      {/* Logo */}
      <div className="flex items-center justify-center mb-4">
        <div className="flex items-center space-x-3">
          <div className="relative">
            <div className="flex items-center justify-center w-12 h-12 bg-primary rounded-xl">
              <Icon name="Building2" size={24} color="white" />
            </div>
            <div className="absolute -top-1 -right-1 w-4 h-4 bg-accent rounded-full flex items-center justify-center">
              <Icon name="Zap" size={10} color="white" />
            </div>
          </div>
          <div className="text-left">
            <h1 className="text-2xl font-bold text-foreground">PropertyFlow</h1>
            <p className="text-sm text-muted-foreground">Gestion Immobilière</p>
          </div>
        </div>
      </div>

      {/* Welcome Message */}
      <div className="space-y-2">
        <h2 className="text-xl font-semibold text-foreground">
          Bienvenue sur votre plateforme
        </h2>
        <p className="text-muted-foreground">
          Connectez-vous à votre espace agence pour gérer votre portefeuille immobilier
        </p>
      </div>

      {/* Features Highlight */}
      <div className="mt-6 grid grid-cols-3 gap-4">
        <div className="text-center">
          <div className="inline-flex items-center justify-center w-8 h-8 bg-primary/10 rounded-lg mb-2">
            <Icon name="Users" size={16} className="text-primary" />
          </div>
          <p className="text-xs text-muted-foreground">Gestion Locataires</p>
        </div>
        <div className="text-center">
          <div className="inline-flex items-center justify-center w-8 h-8 bg-success/10 rounded-lg mb-2">
            <Icon name="TrendingUp" size={16} className="text-success" />
          </div>
          <p className="text-xs text-muted-foreground">Suivi Financier</p>
        </div>
        <div className="text-center">
          <div className="inline-flex items-center justify-center w-8 h-8 bg-accent/10 rounded-lg mb-2">
            <Icon name="Wrench" size={16} className="text-accent" />
          </div>
          <p className="text-xs text-muted-foreground">Maintenance</p>
        </div>
      </div>
    </div>
  );
};

export default PlatformLogo;