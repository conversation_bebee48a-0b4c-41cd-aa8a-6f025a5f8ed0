"""
PropertyFlow Backend - API Locataires
Gestion des locataires
"""

from flask import Blueprint, request, jsonify, g
from flask_jwt_extended import jwt_required
import logging
from database import locataire_service
from utils.errors import NotFoundError, ValidationError, AuthorizationError
from utils.middleware import require_agence_access, validate_json, log_activity

logger = logging.getLogger(__name__)

# Blueprint pour les locataires
locataires_bp = Blueprint('locataires', __name__)

@locataires_bp.route('', methods=['GET'])
@jwt_required()
@require_agence_access
@log_activity('list_locataires')
def get_locataires():
    """Récupérer tous les locataires de l'agence"""
    try:
        # Paramètres de filtrage
        statut = request.args.get('statut')
        
        filters = {'agence_id': g.agence_id}
        if statut:
            filters['statut'] = statut
        
        locataires = locataire_service.get_all(filters=filters)
        
        return jsonify({
            'success': True,
            'data': locataires,
            'total': len(locataires)
        }), 200
        
    except Exception as e:
        logger.error(f"Get locataires error: {str(e)}")
        raise

@locataires_bp.route('/<locataire_id>', methods=['GET'])
@jwt_required()
@require_agence_access
@log_activity('get_locataire')
def get_locataire(locataire_id):
    """Récupérer un locataire par ID"""
    try:
        locataire = locataire_service.get_by_id(locataire_id)
        if not locataire:
            raise NotFoundError("Locataire not found")
        
        # Vérifier que le locataire appartient à l'agence
        if locataire['agence_id'] != g.agence_id:
            raise AuthorizationError("Access denied to this locataire")
        
        return jsonify({
            'success': True,
            'data': locataire
        }), 200
        
    except Exception as e:
        logger.error(f"Get locataire error: {str(e)}")
        raise

@locataires_bp.route('', methods=['POST'])
@jwt_required()
@require_agence_access
@validate_json(['nom', 'prenom', 'telephone'])
@log_activity('create_locataire')
def create_locataire():
    """Créer un nouveau locataire"""
    try:
        data = request.get_json()
        
        # Créer le locataire
        locataire_data = {
            'agence_id': g.agence_id,
            'nom': data['nom'],
            'prenom': data['prenom'],
            'date_naissance': data.get('date_naissance'),
            'lieu_naissance': data.get('lieu_naissance'),
            'nationalite': data.get('nationalite'),
            'profession': data.get('profession'),
            'employeur': data.get('employeur'),
            'revenus_mensuels': data.get('revenus_mensuels'),
            'telephone': data['telephone'],
            'email': data.get('email'),
            'adresse_precedente': data.get('adresse_precedente'),
            'type_piece_identite': data.get('type_piece_identite'),
            'numero_piece_identite': data.get('numero_piece_identite'),
            'date_expiration_piece': data.get('date_expiration_piece'),
            'contact_urgence_nom': data.get('contact_urgence_nom'),
            'contact_urgence_telephone': data.get('contact_urgence_telephone'),
            'ancien_proprietaire': data.get('ancien_proprietaire'),
            'ancien_proprietaire_telephone': data.get('ancien_proprietaire_telephone'),
            'motif_depart': data.get('motif_depart'),
            'documents': data.get('documents', []),
            'notes': data.get('notes'),
            'statut': 'actif'
        }
        
        new_locataire = locataire_service.create(locataire_data)
        
        logger.info(f"New locataire created: {new_locataire['id']}")
        
        return jsonify({
            'success': True,
            'message': 'Locataire created successfully',
            'data': new_locataire
        }), 201
        
    except Exception as e:
        logger.error(f"Create locataire error: {str(e)}")
        raise

@locataires_bp.route('/<locataire_id>', methods=['PUT'])
@jwt_required()
@require_agence_access
@validate_json()
@log_activity('update_locataire')
def update_locataire(locataire_id):
    """Mettre à jour un locataire"""
    try:
        # Vérifier que le locataire existe et appartient à l'agence
        locataire = locataire_service.get_by_id(locataire_id)
        if not locataire:
            raise NotFoundError("Locataire not found")
        
        if locataire['agence_id'] != g.agence_id:
            raise AuthorizationError("Access denied to this locataire")
        
        data = request.get_json()
        
        # Champs modifiables
        allowed_fields = [
            'nom', 'prenom', 'date_naissance', 'lieu_naissance', 'nationalite',
            'profession', 'employeur', 'revenus_mensuels', 'telephone', 'email',
            'adresse_precedente', 'type_piece_identite', 'numero_piece_identite',
            'date_expiration_piece', 'contact_urgence_nom', 'contact_urgence_telephone',
            'ancien_proprietaire', 'ancien_proprietaire_telephone', 'motif_depart',
            'documents', 'notes', 'statut'
        ]
        
        update_data = {k: v for k, v in data.items() if k in allowed_fields}
        
        if not update_data:
            raise ValidationError("No valid fields to update")
        
        updated_locataire = locataire_service.update(locataire_id, update_data)
        
        logger.info(f"Locataire updated: {locataire_id}")
        
        return jsonify({
            'success': True,
            'message': 'Locataire updated successfully',
            'data': updated_locataire
        }), 200
        
    except Exception as e:
        logger.error(f"Update locataire error: {str(e)}")
        raise

@locataires_bp.route('/<locataire_id>', methods=['DELETE'])
@jwt_required()
@require_agence_access
@log_activity('delete_locataire')
def delete_locataire(locataire_id):
    """Supprimer un locataire (soft delete)"""
    try:
        locataire = locataire_service.get_by_id(locataire_id)
        if not locataire:
            raise NotFoundError("Locataire not found")
        
        if locataire['agence_id'] != g.agence_id:
            raise AuthorizationError("Access denied to this locataire")
        
        # TODO: Vérifier qu'il n'y a pas de contrat actif
        
        # Soft delete - changer le statut
        locataire_service.update(locataire_id, {'statut': 'ancien'})
        
        logger.info(f"Locataire deleted: {locataire_id}")
        
        return jsonify({
            'success': True,
            'message': 'Locataire deleted successfully'
        }), 200
        
    except Exception as e:
        logger.error(f"Delete locataire error: {str(e)}")
        raise

@locataires_bp.route('/<locataire_id>/contrats', methods=['GET'])
@jwt_required()
@require_agence_access
@log_activity('get_locataire_contrats')
def get_locataire_contrats(locataire_id):
    """Récupérer tous les contrats d'un locataire"""
    try:
        locataire = locataire_service.get_by_id(locataire_id)
        if not locataire:
            raise NotFoundError("Locataire not found")
        
        if locataire['agence_id'] != g.agence_id:
            raise AuthorizationError("Access denied to this locataire")
        
        # TODO: Utiliser contrat_service pour récupérer les contrats
        contrats = []  # contrat_service.get_by_locataire(locataire_id)
        
        return jsonify({
            'success': True,
            'data': contrats,
            'total': len(contrats)
        }), 200
        
    except Exception as e:
        logger.error(f"Get locataire contrats error: {str(e)}")
        raise

@locataires_bp.route('/<locataire_id>/paiements', methods=['GET'])
@jwt_required()
@require_agence_access
@log_activity('get_locataire_paiements')
def get_locataire_paiements(locataire_id):
    """Récupérer l'historique des paiements d'un locataire"""
    try:
        locataire = locataire_service.get_by_id(locataire_id)
        if not locataire:
            raise NotFoundError("Locataire not found")
        
        if locataire['agence_id'] != g.agence_id:
            raise AuthorizationError("Access denied to this locataire")
        
        # Paramètres de période
        annee = request.args.get('annee', type=int)
        mois = request.args.get('mois', type=int)
        
        # TODO: Utiliser paiement_service pour récupérer les paiements
        paiements = []  # paiement_service.get_by_locataire(locataire_id, annee, mois)
        
        return jsonify({
            'success': True,
            'data': paiements,
            'total': len(paiements)
        }), 200
        
    except Exception as e:
        logger.error(f"Get locataire paiements error: {str(e)}")
        raise

@locataires_bp.route('/search', methods=['GET'])
@jwt_required()
@require_agence_access
@log_activity('search_locataires')
def search_locataires():
    """Rechercher des locataires"""
    try:
        query = request.args.get('q', '')
        
        # TODO: Implémenter la recherche avancée
        filters = {'agence_id': g.agence_id}
        locataires = locataire_service.get_all(filters=filters)
        
        # Filtrage côté application (temporaire)
        if query:
            query_lower = query.lower()
            locataires = [
                l for l in locataires 
                if query_lower in l.get('nom', '').lower() 
                or query_lower in l.get('prenom', '').lower()
                or query_lower in l.get('telephone', '').lower()
                or query_lower in l.get('email', '').lower()
            ]
        
        return jsonify({
            'success': True,
            'data': locataires,
            'total': len(locataires),
            'query': query
        }), 200
        
    except Exception as e:
        logger.error(f"Search locataires error: {str(e)}")
        raise
