import React from 'react';
import Icon from '../../../components/AppIcon';

const ActivityFeed = ({ className = "" }) => {
  const activities = [
    {
      id: 1,
      type: 'payment',
      title: 'Paiement reçu',
      description: '<PERSON> - Appartement 3B, Rue de la Paix',
      amount: '1 250,00 €',
      timestamp: new Date(Date.now() - 15 * 60 * 1000),
      icon: 'CreditCard',
      color: 'success'
    },
    {
      id: 2,
      type: 'maintenance',
      title: 'Nouvelle demande de maintenance',
      description: 'Fuite d\'e<PERSON> - <PERSON>, Studio Voltaire',
      timestamp: new Date(Date.now() - 45 * 60 * 1000),
      icon: 'Wrench',
      color: 'warning'
    },
    {
      id: 3,
      type: 'lease',
      title: 'Bail signé',
      description: '<PERSON> - T2 <PERSON> Haussmann',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
      icon: 'FileText',
      color: 'primary'
    },
    {
      id: 4,
      type: 'inspection',
      title: 'Inspection programmée',
      description: 'État des lieux - <PERSON>, T3 Rue de Rivoli',
      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
      icon: 'Eye',
      color: 'accent'
    },
    {
      id: 5,
      type: 'tenant',
      title: '<PERSON>uveau locataire',
      description: '<PERSON><PERSON> - Dossier validé pour T1 Montmartre',
      timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),
      icon: 'UserPlus',
      color: 'success'
    }
  ];

  const formatTimestamp = (timestamp) => {
    const now = new Date();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 60) return `Il y a ${minutes} min`;
    if (hours < 24) return `Il y a ${hours}h`;
    return `Il y a ${days}j`;
  };

  const getColorClasses = (color) => {
    switch (color) {
      case 'success':
        return 'bg-success/10 text-success';
      case 'warning':
        return 'bg-warning/10 text-warning';
      case 'error':
        return 'bg-error/10 text-error';
      case 'accent':
        return 'bg-accent/10 text-accent';
      default:
        return 'bg-primary/10 text-primary';
    }
  };

  return (
    <div className={`bg-card border border-border rounded-lg ${className}`}>
      <div className="p-6 border-b border-border">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-foreground">Activité récente</h3>
          <button className="text-sm text-primary hover:text-primary/80 transition-smooth">
            Voir tout
          </button>
        </div>
      </div>
      <div className="max-h-96 overflow-y-auto">
        {activities?.map((activity) => (
          <div key={activity?.id} className="p-4 border-b border-border last:border-b-0 hover:bg-muted transition-smooth">
            <div className="flex items-start space-x-3">
              <div className={`p-2 rounded-lg ${getColorClasses(activity?.color)}`}>
                <Icon name={activity?.icon} size={16} />
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-1">
                  <p className="font-medium text-sm text-foreground">{activity?.title}</p>
                  <span className="text-xs text-muted-foreground">
                    {formatTimestamp(activity?.timestamp)}
                  </span>
                </div>
                <p className="text-sm text-muted-foreground mb-1">{activity?.description}</p>
                {activity?.amount && (
                  <p className="text-sm font-data font-medium text-success">{activity?.amount}</p>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ActivityFeed;