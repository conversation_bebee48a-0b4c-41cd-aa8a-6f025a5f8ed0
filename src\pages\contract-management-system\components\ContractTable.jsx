import React, { useState, useMemo } from 'react';
import Button from '../../../components/ui/Button';
import { Checkbox } from '../../../components/ui/Checkbox';
import Icon from '../../../components/AppIcon';

const ContractTable = ({ 
  contracts = [], 
  onContractSelect, 
  selectedContracts = [], 
  onSelectionChange,
  onBulkAction
}) => {
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    })?.format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString)?.toLocaleDateString('fr-FR');
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      active: { label: 'Actif', className: 'bg-green-100 text-green-800 border-green-200' },
      pending: { label: 'En attente', className: 'bg-yellow-100 text-yellow-800 border-yellow-200' },
      draft: { label: 'Brouillon', className: 'bg-gray-100 text-gray-800 border-gray-200' },
      expired: { label: 'Expiré', className: 'bg-red-100 text-red-800 border-red-200' },
      terminated: { label: 'Résilié', className: 'bg-red-100 text-red-800 border-red-200' }
    };

    const config = statusConfig?.[status] || statusConfig?.draft;
    
    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium border ${config?.className}`}>
        {config?.label}
      </span>
    );
  };

  const getComplianceBadge = (complianceStatus) => {
    const complianceConfig = {
      compliant: { label: 'Conforme', className: 'bg-green-100 text-green-800 border-green-200', icon: 'Shield' },
      expiring_soon: { label: 'Expire bientôt', className: 'bg-orange-100 text-orange-800 border-orange-200', icon: 'Clock' },
      pending_review: { label: 'En révision', className: 'bg-blue-100 text-blue-800 border-blue-200', icon: 'Eye' },
      non_compliant: { label: 'Non conforme', className: 'bg-red-100 text-red-800 border-red-200', icon: 'AlertTriangle' },
      draft: { label: 'Brouillon', className: 'bg-gray-100 text-gray-800 border-gray-200', icon: 'Edit' }
    };

    const config = complianceConfig?.[complianceStatus] || complianceConfig?.draft;
    
    return (
      <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${config?.className}`}>
        <Icon name={config?.icon} size={12} />
        {config?.label}
      </div>
    );
  };

  const getContractTypeIcon = (type) => {
    const typeIcons = {
      lease: 'FileText',
      management: 'Briefcase',
      service: 'Settings'
    };
    return typeIcons?.[type] || 'FileText';
  };

  const handleSort = (key) => {
    let direction = 'asc';
    if (sortConfig?.key === key && sortConfig?.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  const handleSelectAll = (checked) => {
    if (checked) {
      onSelectionChange?.(contracts?.map(contract => contract?.id));
    } else {
      onSelectionChange?.([]);
    }
  };

  const handleSelectContract = (contractId, checked) => {
    if (checked) {
      onSelectionChange?.([...selectedContracts, contractId]);
    } else {
      onSelectionChange?.(selectedContracts?.filter(id => id !== contractId));
    }
  };

  const sortedContracts = React.useMemo(() => {
    if (!sortConfig?.key) return contracts;

    return [...contracts]?.sort((a, b) => {
      const aValue = a?.[sortConfig?.key];
      const bValue = b?.[sortConfig?.key];

      if (aValue < bValue) {
        return sortConfig?.direction === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortConfig?.direction === 'asc' ? 1 : -1;
      }
      return 0;
    });
  }, [contracts, sortConfig]);

  const SortHeader = ({ sortKey, children }) => (
    <th
      className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider cursor-pointer hover:bg-muted/50 transition-colors"
      onClick={() => handleSort(sortKey)}
    >
      <div className="flex items-center gap-1">
        {children}
        <Icon
          name={sortConfig?.key === sortKey && sortConfig?.direction === 'desc' ? 'ChevronDown' : 'ChevronUp'}
          size={14}
          className={`transition-opacity ${sortConfig?.key === sortKey ? 'opacity-100' : 'opacity-0'}`}
        />
      </div>
    </th>
  );

  if (contracts?.length === 0) {
    return (
      <div className="bg-card rounded-lg border border-border p-12 text-center">
        <Icon name="FileX" size={48} className="mx-auto text-muted-foreground mb-4" />
        <h3 className="text-lg font-medium text-foreground mb-2">Aucun contrat trouvé</h3>
        <p className="text-muted-foreground mb-6">
          Commencez par créer votre premier contrat dans le système.
        </p>
        <Button iconName="Plus" onClick={() => onContractSelect?.(null, 'create')}>
          Créer un Contrat
        </Button>
      </div>
    );
  }

  return (
    <div className="bg-card rounded-lg border border-border overflow-hidden">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-border">
          <thead className="bg-muted/50">
            <tr>
              <th className="px-6 py-3 w-12">
                <Checkbox
                  checked={selectedContracts?.length === contracts?.length && contracts?.length > 0}
                  indeterminate={selectedContracts?.length > 0 && selectedContracts?.length < contracts?.length}
                  onChange={handleSelectAll}
                />
              </th>
              <SortHeader sortKey="contractNumber">Contrat</SortHeader>
              <SortHeader sortKey="type">Type</SortHeader>
              <SortHeader sortKey="property">Propriété</SortHeader>
              <SortHeader sortKey="owner">Parties</SortHeader>
              <SortHeader sortKey="endDate">Échéance</SortHeader>
              <SortHeader sortKey="monthlyRent">Montant</SortHeader>
              <SortHeader sortKey="status">Statut</SortHeader>
              <SortHeader sortKey="complianceStatus">Conformité</SortHeader>
              <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-border">
            {sortedContracts?.map((contract) => (
              <tr 
                key={contract?.id} 
                className="hover:bg-muted/30 transition-colors"
              >
                <td className="px-6 py-4 whitespace-nowrap">
                  <Checkbox
                    checked={selectedContracts?.includes(contract?.id)}
                    onChange={(checked) => handleSelectContract(contract?.id, checked)}
                  />
                </td>
                
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center mr-3">
                      <Icon name={getContractTypeIcon(contract?.type)} size={16} className="text-primary" />
                    </div>
                    <div>
                      <div className="text-sm font-medium text-foreground">
                        {contract?.contractNumber}
                      </div>
                      <div className="text-xs text-muted-foreground truncate max-w-48">
                        {contract?.title}
                      </div>
                    </div>
                  </div>
                </td>
                
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-foreground">
                    {contract?.type === 'lease' ? 'Bail' : 
                     contract?.type === 'management' ? 'Gestion' : 'Service'}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {contract?.duration} mois
                  </div>
                </td>
                
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-foreground truncate max-w-32">
                    {contract?.property?.address}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {contract?.property?.city} • {contract?.property?.type}
                  </div>
                </td>
                
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-foreground">
                    {contract?.owner?.name}
                  </div>
                  {contract?.tenant && (
                    <div className="text-xs text-muted-foreground">
                      ↔ {contract?.tenant?.name}
                    </div>
                  )}
                </td>
                
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-foreground">
                    {formatDate(contract?.endDate)}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Renouvellement: {formatDate(contract?.renewalDate)}
                  </div>
                </td>
                
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-foreground">
                    {formatCurrency(contract?.monthlyRent || contract?.monthlyFee || 0)}
                  </div>
                  {contract?.charges && (
                    <div className="text-xs text-muted-foreground">
                      + {formatCurrency(contract?.charges)} charges
                    </div>
                  )}
                </td>
                
                <td className="px-6 py-4 whitespace-nowrap">
                  {getStatusBadge(contract?.status)}
                </td>
                
                <td className="px-6 py-4 whitespace-nowrap">
                  {getComplianceBadge(contract?.complianceStatus)}
                </td>
                
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      iconName="Eye"
                      onClick={() => onContractSelect?.(contract?.id, 'view')}
                      title="Voir détails"
                    />
                    <Button
                      variant="ghost"
                      size="sm"
                      iconName="Edit"
                      onClick={() => onContractSelect?.(contract?.id, 'edit')}
                      title="Modifier"
                    />
                    <Button
                      variant="ghost"
                      size="sm"
                      iconName="PenTool"
                      onClick={() => onContractSelect?.(contract?.id, 'sign')}
                      title="Signer"
                    />
                    <Button
                      variant="ghost"
                      size="sm"
                      iconName="RotateCcw"
                      onClick={() => onContractSelect?.(contract?.id, 'renew')}
                      title="Renouveler"
                    />
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default ContractTable;