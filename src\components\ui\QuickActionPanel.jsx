import React, { useState } from 'react';
import { useLocation } from 'react-router-dom';
import Icon from '../AppIcon';
import Button from './Button';

const QuickActionPanel = ({ 
  isOpen = false, 
  onToggle = () => {},
  className = "" 
}) => {
  const location = useLocation();
  const [expandedSection, setExpandedSection] = useState(null);

  // Context-aware actions based on current page
  const getContextualActions = () => {
    const currentPath = location?.pathname;
    
    const commonActions = [
      {
        id: 'add-property',
        label: 'Ajouter une propriété',
        icon: 'Plus',
        description: 'Créer une nouvelle propriété',
        category: 'property',
        priority: 'high'
      },
      {
        id: 'add-tenant',
        label: 'Nouveau locataire',
        icon: 'UserPlus',
        description: 'Enregistrer un nouveau locataire',
        category: 'tenant',
        priority: 'high'
      },
      {
        id: 'record-payment',
        label: 'Enregistrer un paiement',
        icon: 'CreditCard',
        description: 'Saisir un paiement de loyer',
        category: 'finance',
        priority: 'high'
      }
    ];

    const contextualActions = {
      '/agency-dashboard': [
        ...commonActions,
        {
          id: 'generate-report',
          label: 'Générer un rapport',
          icon: 'FileText',
          description: 'Créer un rapport personnalisé',
          category: 'report',
          priority: 'medium'
        }
      ],
      '/property-portfolio-management': [
        {
          id: 'add-property',
          label: 'Nouvelle propriété',
          icon: 'Building2',
          description: 'Ajouter une propriété au portefeuille',
          category: 'property',
          priority: 'high'
        },
        {
          id: 'bulk-update',
          label: 'Mise à jour groupée',
          icon: 'Edit3',
          description: 'Modifier plusieurs propriétés',
          category: 'property',
          priority: 'medium'
        },
        {
          id: 'schedule-inspection',
          label: 'Programmer inspection',
          icon: 'Calendar',
          description: 'Planifier une visite d\'état des lieux',
          category: 'inspection',
          priority: 'medium'
        }
      ],
      '/tenant-management-hub': [
        {
          id: 'add-tenant',
          label: 'Nouveau locataire',
          icon: 'UserPlus',
          description: 'Créer un profil locataire',
          category: 'tenant',
          priority: 'high'
        },
        {
          id: 'create-lease',
          label: 'Créer un bail',
          icon: 'FileText',
          description: 'Générer un nouveau contrat de bail',
          category: 'lease',
          priority: 'high'
        },
        {
          id: 'send-notice',
          label: 'Envoyer un avis',
          icon: 'Mail',
          description: 'Notifier les locataires',
          category: 'communication',
          priority: 'medium'
        }
      ],
      '/maintenance-request-tracking': [
        {
          id: 'create-request',
          label: 'Nouvelle demande',
          icon: 'Wrench',
          description: 'Créer une demande de maintenance',
          category: 'maintenance',
          priority: 'high'
        },
        {
          id: 'assign-contractor',
          label: 'Assigner prestataire',
          icon: 'Users',
          description: 'Affecter un prestataire',
          category: 'maintenance',
          priority: 'medium'
        },
        {
          id: 'schedule-maintenance',
          label: 'Programmer intervention',
          icon: 'Calendar',
          description: 'Planifier une intervention',
          category: 'maintenance',
          priority: 'medium'
        }
      ],
      '/financial-dashboard-reporting': [
        {
          id: 'record-payment',
          label: 'Nouveau paiement',
          icon: 'CreditCard',
          description: 'Enregistrer un paiement',
          category: 'finance',
          priority: 'high'
        },
        {
          id: 'create-invoice',
          label: 'Créer facture',
          icon: 'Receipt',
          description: 'Générer une facture',
          category: 'finance',
          priority: 'high'
        },
        {
          id: 'export-data',
          label: 'Exporter données',
          icon: 'Download',
          description: 'Télécharger les données financières',
          category: 'export',
          priority: 'medium'
        }
      ]
    };

    return contextualActions?.[currentPath] || commonActions;
  };

  const actions = getContextualActions();
  const categories = [...new Set(actions.map(action => action.category))];

  const handleActionClick = (actionId) => {
    console.log(`Executing action: ${actionId}`);
    // Here you would implement the actual action logic
    onToggle(); // Close panel after action
  };

  const toggleSection = (category) => {
    setExpandedSection(expandedSection === category ? null : category);
  };

  const getCategoryIcon = (category) => {
    switch (category) {
      case 'property': return 'Building2';
      case 'tenant': return 'Users';
      case 'finance': return 'CreditCard';
      case 'maintenance': return 'Wrench';
      case 'lease': return 'FileText';
      case 'communication': return 'Mail';
      case 'inspection': return 'Eye';
      case 'report': return 'BarChart3';
      case 'export': return 'Download';
      default: return 'Zap';
    }
  };

  const getCategoryLabel = (category) => {
    switch (category) {
      case 'property': return 'Propriétés';
      case 'tenant': return 'Locataires';
      case 'finance': return 'Finances';
      case 'maintenance': return 'Maintenance';
      case 'lease': return 'Baux';
      case 'communication': return 'Communication';
      case 'inspection': return 'Inspections';
      case 'report': return 'Rapports';
      case 'export': return 'Export';
      default: return 'Actions';
    }
  };

  if (!isOpen) return null;

  return (
    <div className={`fixed right-4 top-32 w-80 bg-card border border-border rounded-lg elevation-3 z-40 animate-slide-in ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <div className="flex items-center space-x-2">
          <Icon name="Zap" size={20} className="text-primary" />
          <h3 className="font-semibold text-foreground">Actions rapides</h3>
        </div>
        <Button
          variant="ghost"
          size="icon"
          onClick={onToggle}
        >
          <Icon name="X" size={16} />
        </Button>
      </div>
      {/* Actions by Category */}
      <div className="max-h-96 overflow-y-auto">
        {categories?.map((category) => {
          const categoryActions = actions?.filter(action => action?.category === category);
          const isExpanded = expandedSection === category || categories?.length === 1;

          return (
            <div key={category} className="border-b border-border last:border-b-0">
              {categories?.length > 1 && (
                <button
                  onClick={() => toggleSection(category)}
                  className="flex items-center justify-between w-full p-3 hover:bg-muted transition-smooth"
                >
                  <div className="flex items-center space-x-2">
                    <Icon name={getCategoryIcon(category)} size={16} className="text-primary" />
                    <span className="font-medium text-sm text-foreground">
                      {getCategoryLabel(category)}
                    </span>
                    <span className="text-xs text-muted-foreground">
                      ({categoryActions?.length})
                    </span>
                  </div>
                  <Icon 
                    name={isExpanded ? "ChevronUp" : "ChevronDown"} 
                    size={16} 
                    className="text-muted-foreground" 
                  />
                </button>
              )}
              {isExpanded && (
                <div className="pb-2">
                  {categoryActions?.map((action) => (
                    <button
                      key={action?.id}
                      onClick={() => handleActionClick(action?.id)}
                      className="flex items-start space-x-3 w-full p-3 hover:bg-muted transition-smooth text-left"
                    >
                      <div className={`p-2 rounded-lg ${
                        action?.priority === 'high' ? 'bg-primary/10 text-primary' :
                        action?.priority === 'medium'? 'bg-accent/10 text-accent' : 'bg-muted text-muted-foreground'
                      }`}>
                        <Icon name={action?.icon} size={16} />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-sm text-foreground">
                          {action?.label}
                        </p>
                        <p className="text-xs text-muted-foreground mt-1">
                          {action?.description}
                        </p>
                      </div>
                      <Icon name="ChevronRight" size={14} className="text-muted-foreground mt-1" />
                    </button>
                  ))}
                </div>
              )}
            </div>
          );
        })}
      </div>
      {/* Footer */}
      <div className="p-3 border-t border-border">
        <Button variant="ghost" size="sm" className="w-full">
          <Icon name="Settings" size={14} className="mr-2" />
          Personnaliser les actions
        </Button>
      </div>
    </div>
  );
};

export default QuickActionPanel;