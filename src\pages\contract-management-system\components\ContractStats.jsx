import React from 'react';
import Icon from '../../../components/AppIcon';

const ContractStats = ({ stats = {}, className = "" }) => {
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    })?.format(amount);
  };

  const formatPercentage = (percentage) => {
    return `${percentage?.toFixed(1)}%`;
  };

  const statsData = [
    {
      title: 'Total Contrats',
      value: stats?.totalContracts || 0,
      icon: 'FileText',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      change: '+3.2%',
      changeType: 'positive'
    },
    {
      title: 'Contrats Actifs',
      value: stats?.activeContracts || 0,
      icon: 'FileCheck',
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      change: '+5.1%',
      changeType: 'positive'
    },
    {
      title: '<PERSON> Renouveler',
      value: stats?.expiringContracts || 0,
      icon: 'Clock',
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      change: '+2 ce mois',
      changeType: 'neutral'
    },
    {
      title: 'En Attente',
      value: stats?.pendingContracts || 0,
      icon: 'AlertCircle',
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
      change: '-1.5%',
      changeType: 'positive'
    },
    {
      title: 'Signature Numérique',
      value: formatPercentage(stats?.digitalSignatureRate || 0),
      icon: 'PenTool',
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      change: '+15.3%',
      changeType: 'positive'
    },
    {
      title: 'Conformité',
      value: formatPercentage(stats?.complianceRate || 0),
      icon: 'Shield',
      color: 'text-emerald-600',
      bgColor: 'bg-emerald-50',
      change: '+2.8%',
      changeType: 'positive'
    }
  ];

  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 ${className}`}>
      {statsData?.map((stat, index) => (
        <div
          key={index}
          className="bg-card rounded-lg p-6 border border-border hover:shadow-md transition-shadow duration-200"
        >
          <div className="flex items-center justify-between mb-4">
            <div className={`w-12 h-12 ${stat?.bgColor} rounded-lg flex items-center justify-center`}>
              <Icon name={stat?.icon} size={24} className={stat?.color} />
            </div>
            <div className={`flex items-center text-xs font-medium ${
              stat?.changeType === 'positive' ? 'text-green-600' : 
              stat?.changeType === 'negative' ? 'text-red-600' : 'text-muted-foreground'
            }`}>
              {stat?.changeType !== 'neutral' && (
                <Icon 
                  name={stat?.changeType === 'positive' ? 'TrendingUp' : 'TrendingDown'} 
                  size={12} 
                  className="mr-1" 
                />
              )}
              {stat?.change}
            </div>
          </div>
          
          <div className="space-y-1">
            <p className="text-2xl font-bold text-foreground">
              {stat?.value}
            </p>
            <p className="text-sm text-muted-foreground font-medium">
              {stat?.title}
            </p>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ContractStats;