"""
PropertyFlow Backend - API Biens
Gestion des biens immobiliers
"""

from flask import Blueprint, request, jsonify, g
from flask_jwt_extended import jwt_required
import logging
from database import bien_service, bailleur_service
from utils.errors import NotFoundError, ValidationError, AuthorizationError
from utils.middleware import require_agence_access, validate_json, log_activity

logger = logging.getLogger(__name__)

# Blueprint pour les biens
biens_bp = Blueprint('biens', __name__)

@biens_bp.route('', methods=['GET'])
@jwt_required()
@require_agence_access
@log_activity('list_biens')
def get_biens():
    """Récupérer tous les biens de l'agence"""
    try:
        # Paramètres de filtrage
        statut = request.args.get('statut')
        type_bien = request.args.get('type_bien')
        bailleur_id = request.args.get('bailleur_id')
        ville = request.args.get('ville')
        
        filters = {'agence_id': g.agence_id}
        if statut:
            filters['statut'] = statut
        if type_bien:
            filters['type_bien'] = type_bien
        if bailleur_id:
            filters['bailleur_id'] = bailleur_id
        if ville:
            filters['ville'] = ville
        
        biens = bien_service.get_all(filters=filters)
        
        return jsonify({
            'success': True,
            'data': biens,
            'total': len(biens)
        }), 200
        
    except Exception as e:
        logger.error(f"Get biens error: {str(e)}")
        raise

@biens_bp.route('/<bien_id>', methods=['GET'])
@jwt_required()
@require_agence_access
@log_activity('get_bien')
def get_bien(bien_id):
    """Récupérer un bien par ID"""
    try:
        bien = bien_service.get_by_id(bien_id)
        if not bien:
            raise NotFoundError("Bien not found")
        
        # Vérifier que le bien appartient à l'agence
        if bien['agence_id'] != g.agence_id:
            raise AuthorizationError("Access denied to this bien")
        
        return jsonify({
            'success': True,
            'data': bien
        }), 200
        
    except Exception as e:
        logger.error(f"Get bien error: {str(e)}")
        raise

@biens_bp.route('', methods=['POST'])
@jwt_required()
@require_agence_access
@validate_json(['bailleur_id', 'type_bien', 'adresse', 'ville', 'loyer_mensuel'])
@log_activity('create_bien')
def create_bien():
    """Créer un nouveau bien"""
    try:
        data = request.get_json()
        
        # Vérifier que le bailleur existe et appartient à l'agence
        bailleur = bailleur_service.get_by_id(data['bailleur_id'])
        if not bailleur:
            raise NotFoundError("Bailleur not found")
        
        if bailleur['agence_id'] != g.agence_id:
            raise AuthorizationError("Bailleur does not belong to your agency")
        
        # Générer une référence unique
        # TODO: Implémenter la génération de référence
        reference = f"BIEN-{data['type_bien'][:3].upper()}-{len(bien_service.get_by_agence(g.agence_id)) + 1:04d}"
        
        # Créer le bien
        bien_data = {
            'agence_id': g.agence_id,
            'bailleur_id': data['bailleur_id'],
            'reference': reference,
            'type_bien': data['type_bien'],
            'adresse': data['adresse'],
            'ville': data['ville'],
            'code_postal': data.get('code_postal'),
            'pays': data.get('pays', 'Sénégal'),
            'surface_habitable': data.get('surface_habitable'),
            'nombre_pieces': data.get('nombre_pieces'),
            'nombre_chambres': data.get('nombre_chambres'),
            'nombre_salles_bain': data.get('nombre_salles_bain'),
            'etage': data.get('etage'),
            'ascenseur': data.get('ascenseur', False),
            'balcon': data.get('balcon', False),
            'parking': data.get('parking', False),
            'jardin': data.get('jardin', False),
            'loyer_mensuel': data['loyer_mensuel'],
            'charges_mensuelles': data.get('charges_mensuelles', 0),
            'depot_garantie': data.get('depot_garantie'),
            'equipements': data.get('equipements', []),
            'photos': data.get('photos', []),
            'documents': data.get('documents', []),
            'statut': 'disponible',
            'date_disponibilite': data.get('date_disponibilite'),
            'description': data.get('description'),
            'notes': data.get('notes')
        }
        
        new_bien = bien_service.create(bien_data)
        
        logger.info(f"New bien created: {new_bien['id']}")
        
        return jsonify({
            'success': True,
            'message': 'Bien created successfully',
            'data': new_bien
        }), 201
        
    except Exception as e:
        logger.error(f"Create bien error: {str(e)}")
        raise

@biens_bp.route('/<bien_id>', methods=['PUT'])
@jwt_required()
@require_agence_access
@validate_json()
@log_activity('update_bien')
def update_bien(bien_id):
    """Mettre à jour un bien"""
    try:
        # Vérifier que le bien existe et appartient à l'agence
        bien = bien_service.get_by_id(bien_id)
        if not bien:
            raise NotFoundError("Bien not found")
        
        if bien['agence_id'] != g.agence_id:
            raise AuthorizationError("Access denied to this bien")
        
        data = request.get_json()
        
        # Champs modifiables
        allowed_fields = [
            'type_bien', 'adresse', 'ville', 'code_postal', 'pays',
            'surface_habitable', 'nombre_pieces', 'nombre_chambres', 'nombre_salles_bain',
            'etage', 'ascenseur', 'balcon', 'parking', 'jardin',
            'loyer_mensuel', 'charges_mensuelles', 'depot_garantie',
            'equipements', 'photos', 'documents', 'statut',
            'date_disponibilite', 'description', 'notes'
        ]
        
        update_data = {k: v for k, v in data.items() if k in allowed_fields}
        
        if not update_data:
            raise ValidationError("No valid fields to update")
        
        # Vérifier le changement de bailleur si spécifié
        if 'bailleur_id' in data:
            bailleur = bailleur_service.get_by_id(data['bailleur_id'])
            if not bailleur or bailleur['agence_id'] != g.agence_id:
                raise ValidationError("Invalid bailleur")
            update_data['bailleur_id'] = data['bailleur_id']
        
        updated_bien = bien_service.update(bien_id, update_data)
        
        logger.info(f"Bien updated: {bien_id}")
        
        return jsonify({
            'success': True,
            'message': 'Bien updated successfully',
            'data': updated_bien
        }), 200
        
    except Exception as e:
        logger.error(f"Update bien error: {str(e)}")
        raise

@biens_bp.route('/<bien_id>', methods=['DELETE'])
@jwt_required()
@require_agence_access
@log_activity('delete_bien')
def delete_bien(bien_id):
    """Supprimer un bien (soft delete)"""
    try:
        bien = bien_service.get_by_id(bien_id)
        if not bien:
            raise NotFoundError("Bien not found")
        
        if bien['agence_id'] != g.agence_id:
            raise AuthorizationError("Access denied to this bien")
        
        # TODO: Vérifier qu'il n'y a pas de contrat actif
        
        # Soft delete - changer le statut
        bien_service.update(bien_id, {'statut': 'indisponible'})
        
        logger.info(f"Bien deleted: {bien_id}")
        
        return jsonify({
            'success': True,
            'message': 'Bien deleted successfully'
        }), 200
        
    except Exception as e:
        logger.error(f"Delete bien error: {str(e)}")
        raise

@biens_bp.route('/<bien_id>/photos', methods=['POST'])
@jwt_required()
@require_agence_access
@log_activity('upload_bien_photos')
def upload_bien_photos(bien_id):
    """Uploader des photos pour un bien"""
    try:
        bien = bien_service.get_by_id(bien_id)
        if not bien:
            raise NotFoundError("Bien not found")
        
        if bien['agence_id'] != g.agence_id:
            raise AuthorizationError("Access denied to this bien")
        
        # TODO: Implémenter l'upload de fichiers
        # Pour l'instant, on accepte les URLs
        data = request.get_json()
        photos = data.get('photos', [])
        
        # Ajouter aux photos existantes
        current_photos = bien.get('photos', [])
        current_photos.extend(photos)
        
        bien_service.update(bien_id, {'photos': current_photos})
        
        return jsonify({
            'success': True,
            'message': 'Photos uploaded successfully',
            'photos': current_photos
        }), 200
        
    except Exception as e:
        logger.error(f"Upload photos error: {str(e)}")
        raise

@biens_bp.route('/search', methods=['GET'])
@jwt_required()
@require_agence_access
@log_activity('search_biens')
def search_biens():
    """Rechercher des biens avec critères avancés"""
    try:
        # Paramètres de recherche
        query = request.args.get('q', '')
        prix_min = request.args.get('prix_min', type=float)
        prix_max = request.args.get('prix_max', type=float)
        surface_min = request.args.get('surface_min', type=float)
        surface_max = request.args.get('surface_max', type=float)
        chambres_min = request.args.get('chambres_min', type=int)
        
        # TODO: Implémenter la recherche avancée
        # Pour l'instant, recherche simple
        filters = {'agence_id': g.agence_id}
        
        if prix_min:
            # TODO: Ajouter filtre prix minimum
            pass
        if prix_max:
            # TODO: Ajouter filtre prix maximum
            pass
        
        biens = bien_service.get_all(filters=filters)
        
        # Filtrage côté application (temporaire)
        if query:
            biens = [b for b in biens if query.lower() in b.get('adresse', '').lower() or query.lower() in b.get('ville', '').lower()]
        
        return jsonify({
            'success': True,
            'data': biens,
            'total': len(biens),
            'query': query
        }), 200
        
    except Exception as e:
        logger.error(f"Search biens error: {str(e)}")
        raise
