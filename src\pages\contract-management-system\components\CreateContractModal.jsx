import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';
import Select from '../../../components/ui/Select';
import { Checkbox } from '../../../components/ui/Checkbox';
import Icon from '../../../components/AppIcon';

const CreateContractModal = ({ isOpen, onClose, onSubmit, properties = [], owners = [] }) => {
  const [formData, setFormData] = useState({
    type: 'lease',
    title: '',
    propertyId: '',
    ownerId: '',
    tenantName: '',
    tenantEmail: '',
    startDate: '',
    endDate: '',
    renewalDate: '',
    monthlyRent: '',
    securityDeposit: '',
    charges: '',
    duration: 12,
    renewalType: 'automatic',
    digitalSignature: true,
    managementFee: '',
    serviceDescription: ''
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const contractTypeOptions = [
    { value: 'lease', label: 'Bail de location' },
    { value: 'management', label: 'Mandat de gestion' },
    { value: 'service', label: 'Contrat de service' }
  ];

  const renewalTypeOptions = [
    { value: 'automatic', label: 'Renouvellement automatique' },
    { value: 'manual', label: 'Renouvellement manuel' }
  ];

  const durationOptions = [
    { value: 6, label: '6 mois' },
    { value: 12, label: '1 an' },
    { value: 24, label: '2 ans' },
    { value: 36, label: '3 ans' },
    { value: 60, label: '5 ans' }
  ];

  const propertyOptions = properties?.map(property => ({
    value: property?.id,
    label: `${property?.address}, ${property?.city} (${property?.type})`
  }));

  const ownerOptions = owners?.map(owner => ({
    value: owner?.id,
    label: `${owner?.name} - ${owner?.email}`
  }));

  const handleInputChange = (field) => (e) => {
    const value = e?.target?.type === 'checkbox' ? e?.target?.checked : e?.target?.value;
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors?.[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }

    // Auto-generate title
    if (field === 'type' || field === 'propertyId') {
      generateTitle({ ...formData, [field]: value });
    }
  };

  const handleSelectChange = (field) => (value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors?.[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }

    // Auto-generate title
    if (field === 'type' || field === 'propertyId') {
      generateTitle({ ...formData, [field]: value });
    }
  };

  const generateTitle = (data) => {
    if (data?.type && data?.propertyId) {
      const property = properties?.find(p => p?.id === data?.propertyId);
      if (property) {
        let typeLabel = '';
        switch (data?.type) {
          case 'lease':
            typeLabel = 'Bail de location';
            break;
          case 'management':
            typeLabel = 'Mandat de gestion';
            break;
          case 'service':
            typeLabel = 'Contrat de service';
            break;
        }
        const title = `${typeLabel} - ${property?.address}`;
        setFormData(prev => ({ ...prev, title }));
      }
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData?.type) newErrors.type = 'Type de contrat requis';
    if (!formData?.title?.trim()) newErrors.title = 'Titre requis';
    if (!formData?.propertyId) newErrors.propertyId = 'Propriété requise';
    if (!formData?.ownerId) newErrors.ownerId = 'Propriétaire requis';
    if (!formData?.startDate) newErrors.startDate = 'Date de début requise';
    if (!formData?.endDate) newErrors.endDate = 'Date de fin requise';

    // Type-specific validations
    if (formData?.type === 'lease') {
      if (!formData?.tenantName?.trim()) newErrors.tenantName = 'Nom du locataire requis';
      if (!formData?.tenantEmail?.trim()) {
        newErrors.tenantEmail = 'Email du locataire requis';
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/?.test(formData?.tenantEmail)) {
        newErrors.tenantEmail = 'Format email invalide';
      }
      if (!formData?.monthlyRent || formData?.monthlyRent <= 0) {
        newErrors.monthlyRent = 'Loyer mensuel requis';
      }
    }

    if (formData?.type === 'management') {
      if (!formData?.managementFee || formData?.managementFee <= 0) {
        newErrors.managementFee = 'Frais de gestion requis';
      }
    }

    if (formData?.type === 'service') {
      if (!formData?.serviceDescription?.trim()) {
        newErrors.serviceDescription = 'Description du service requise';
      }
    }

    // Date validation
    if (formData?.startDate && formData?.endDate) {
      if (new Date(formData?.startDate) >= new Date(formData?.endDate)) {
        newErrors.endDate = 'La date de fin doit être postérieure au début';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors)?.length === 0;
  };

  const handleSubmit = async (e) => {
    e?.preventDefault();
    
    if (!validateForm()) return;
    
    setIsSubmitting(true);
    
    try {
      await onSubmit?.(formData);
      handleClose();
    } catch (error) {
      console.error('Error creating contract:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setFormData({
      type: 'lease',
      title: '',
      propertyId: '',
      ownerId: '',
      tenantName: '',
      tenantEmail: '',
      startDate: '',
      endDate: '',
      renewalDate: '',
      monthlyRent: '',
      securityDeposit: '',
      charges: '',
      duration: 12,
      renewalType: 'automatic',
      digitalSignature: true,
      managementFee: '',
      serviceDescription: ''
    });
    setErrors({});
    onClose?.();
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black/50"
            onClick={handleClose}
          />
          
          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            className="relative bg-card rounded-lg shadow-xl border border-border w-full max-w-3xl max-h-[90vh] overflow-hidden"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-border">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                  <Icon name="FileText" size={18} className="text-primary" />
                </div>
                <div>
                  <h2 className="text-lg font-semibold text-foreground">
                    Nouveau Contrat
                  </h2>
                  <p className="text-sm text-muted-foreground">
                    Créez un nouveau contrat avec signature numérique
                  </p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                iconName="X"
                onClick={handleClose}
              />
            </div>

            {/* Content */}
            <div className="p-6 overflow-y-auto max-h-[70vh]">
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Contract Type */}
                <div className="space-y-4">
                  <h3 className="text-base font-medium text-foreground border-b border-border pb-2">
                    Type de Contrat
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Select
                        label="Type de contrat *"
                        value={formData?.type}
                        onValueChange={handleSelectChange('type')}
                        options={contractTypeOptions}
                        error={errors?.type}
                      />
                    </div>
                    <div>
                      <Input
                        label="Titre du contrat *"
                        value={formData?.title}
                        onChange={handleInputChange('title')}
                        error={errors?.title}
                        placeholder="Sera généré automatiquement"
                      />
                    </div>
                  </div>
                </div>

                {/* Property and Parties */}
                <div className="space-y-4">
                  <h3 className="text-base font-medium text-foreground border-b border-border pb-2">
                    Propriété et Parties
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Select
                        label="Propriété *"
                        value={formData?.propertyId}
                        onValueChange={handleSelectChange('propertyId')}
                        options={propertyOptions}
                        error={errors?.propertyId}
                        placeholder="Sélectionner une propriété"
                      />
                    </div>
                    <div>
                      <Select
                        label="Propriétaire *"
                        value={formData?.ownerId}
                        onValueChange={handleSelectChange('ownerId')}
                        options={ownerOptions}
                        error={errors?.ownerId}
                        placeholder="Sélectionner un propriétaire"
                      />
                    </div>
                  </div>

                  {/* Tenant info for lease contracts */}
                  {formData?.type === 'lease' && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Input
                          label="Nom du locataire *"
                          value={formData?.tenantName}
                          onChange={handleInputChange('tenantName')}
                          error={errors?.tenantName}
                          placeholder="Jean Dupont"
                        />
                      </div>
                      <div>
                        <Input
                          label="Email du locataire *"
                          type="email"
                          value={formData?.tenantEmail}
                          onChange={handleInputChange('tenantEmail')}
                          error={errors?.tenantEmail}
                          placeholder="<EMAIL>"
                        />
                      </div>
                    </div>
                  )}
                </div>

                {/* Contract Terms */}
                <div className="space-y-4">
                  <h3 className="text-base font-medium text-foreground border-b border-border pb-2">
                    Conditions du Contrat
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <Input
                        label="Date de début *"
                        type="date"
                        value={formData?.startDate}
                        onChange={handleInputChange('startDate')}
                        error={errors?.startDate}
                      />
                    </div>
                    <div>
                      <Input
                        label="Date de fin *"
                        type="date"
                        value={formData?.endDate}
                        onChange={handleInputChange('endDate')}
                        error={errors?.endDate}
                      />
                    </div>
                    <div>
                      <Select
                        label="Durée"
                        value={formData?.duration}
                        onValueChange={handleSelectChange('duration')}
                        options={durationOptions}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Input
                        label="Date limite renouvellement"
                        type="date"
                        value={formData?.renewalDate}
                        onChange={handleInputChange('renewalDate')}
                      />
                    </div>
                    <div>
                      <Select
                        label="Type de renouvellement"
                        value={formData?.renewalType}
                        onValueChange={handleSelectChange('renewalType')}
                        options={renewalTypeOptions}
                      />
                    </div>
                  </div>
                </div>

                {/* Financial Terms */}
                <div className="space-y-4">
                  <h3 className="text-base font-medium text-foreground border-b border-border pb-2">
                    Conditions Financières
                  </h3>
                  
                  {formData?.type === 'lease' && (
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <Input
                          label="Loyer mensuel (€) *"
                          type="number"
                          step="0.01"
                          min="0"
                          value={formData?.monthlyRent}
                          onChange={handleInputChange('monthlyRent')}
                          error={errors?.monthlyRent}
                          placeholder="1500"
                        />
                      </div>
                      <div>
                        <Input
                          label="Dépôt de garantie (€)"
                          type="number"
                          step="0.01"
                          min="0"
                          value={formData?.securityDeposit}
                          onChange={handleInputChange('securityDeposit')}
                          placeholder="3000"
                        />
                      </div>
                      <div>
                        <Input
                          label="Charges mensuelles (€)"
                          type="number"
                          step="0.01"
                          min="0"
                          value={formData?.charges}
                          onChange={handleInputChange('charges')}
                          placeholder="150"
                        />
                      </div>
                    </div>
                  )}

                  {formData?.type === 'management' && (
                    <div>
                      <Input
                        label="Frais de gestion (%) *"
                        type="number"
                        step="0.1"
                        min="0"
                        max="100"
                        value={formData?.managementFee}
                        onChange={handleInputChange('managementFee')}
                        error={errors?.managementFee}
                        placeholder="8.0"
                      />
                    </div>
                  )}

                  {formData?.type === 'service' && (
                    <div>
                      <Input
                        label="Description du service *"
                        value={formData?.serviceDescription}
                        onChange={handleInputChange('serviceDescription')}
                        error={errors?.serviceDescription}
                        placeholder="Maintenance, nettoyage, gardiennage..."
                      />
                    </div>
                  )}
                </div>

                {/* Options */}
                <div className="space-y-4">
                  <h3 className="text-base font-medium text-foreground border-b border-border pb-2">
                    Options
                  </h3>
                  
                  <div>
                    <Checkbox
                      checked={formData?.digitalSignature}
                      onChange={handleInputChange('digitalSignature')}
                      label="Signature numérique activée"
                    />
                  </div>
                </div>
              </form>
            </div>

            {/* Footer */}
            <div className="flex items-center justify-end gap-3 p-6 border-t border-border bg-muted/20">
              <Button
                variant="outline"
                onClick={handleClose}
                disabled={isSubmitting}
              >
                Annuler
              </Button>
              <Button
                onClick={handleSubmit}
                loading={isSubmitting}
                iconName="FileText"
              >
                Créer Contrat
              </Button>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};

export default CreateContractModal;