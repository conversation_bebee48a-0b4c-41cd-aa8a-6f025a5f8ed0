"""
Configuration de l'application PropertyFlow
"""

import os
from datetime import timedelta

class Config:
    """Configuration de base"""
    
    # Configuration Flask
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    
    # Configuration JWT
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY') or 'jwt-secret-key-change-in-production'
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=24)
    JWT_REFRESH_TOKEN_EXPIRES = timedelta(days=30)
    JWT_ALGORITHM = 'HS256'
    
    # Configuration Supabase
    SUPABASE_URL = os.environ.get('SUPABASE_URL')
    SUPABASE_KEY = os.environ.get('SUPABASE_KEY')
    SUPABASE_SERVICE_KEY = os.environ.get('SUPABASE_SERVICE_KEY')
    
    # Configuration PostgreSQL (fallback si pas Supabase)
    DATABASE_URL = os.environ.get('DATABASE_URL') or 'postgresql://user:password@localhost/propertyflow'
    
    # Configuration Redis (pour cache et sessions)
    REDIS_URL = os.environ.get('REDIS_URL') or 'redis://localhost:6379/0'
    
    # Configuration CORS
    CORS_ORIGINS = os.environ.get('CORS_ORIGINS', 'http://localhost:3000,http://localhost:5173').split(',')
    
    # Configuration de l'upload de fichiers
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max
    UPLOAD_FOLDER = os.environ.get('UPLOAD_FOLDER') or 'uploads'
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'pdf', 'doc', 'docx'}
    
    # Configuration des services externes
    # Orange Money
    ORANGE_MONEY_API_URL = os.environ.get('ORANGE_MONEY_API_URL')
    ORANGE_MONEY_CLIENT_ID = os.environ.get('ORANGE_MONEY_CLIENT_ID')
    ORANGE_MONEY_CLIENT_SECRET = os.environ.get('ORANGE_MONEY_CLIENT_SECRET')
    
    # Wave
    WAVE_API_URL = os.environ.get('WAVE_API_URL')
    WAVE_API_KEY = os.environ.get('WAVE_API_KEY')
    WAVE_SECRET_KEY = os.environ.get('WAVE_SECRET_KEY')
    
    # Configuration SMS (pour notifications)
    SMS_PROVIDER = os.environ.get('SMS_PROVIDER', 'orange')  # orange, free, etc.
    SMS_API_KEY = os.environ.get('SMS_API_KEY')
    SMS_SENDER_ID = os.environ.get('SMS_SENDER_ID', 'PropertyFlow')
    
    # Configuration Email
    MAIL_SERVER = os.environ.get('MAIL_SERVER', 'smtp.gmail.com')
    MAIL_PORT = int(os.environ.get('MAIL_PORT', 587))
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'true').lower() in ['true', 'on', '1']
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    MAIL_DEFAULT_SENDER = os.environ.get('MAIL_DEFAULT_SENDER')
    
    # Configuration Celery (pour les tâches asynchrones)
    CELERY_BROKER_URL = os.environ.get('CELERY_BROKER_URL') or REDIS_URL
    CELERY_RESULT_BACKEND = os.environ.get('CELERY_RESULT_BACKEND') or REDIS_URL
    
    # Configuration de logging
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
    LOG_FILE = os.environ.get('LOG_FILE')
    
    # Configuration de sécurité
    BCRYPT_LOG_ROUNDS = int(os.environ.get('BCRYPT_LOG_ROUNDS', 12))
    
    # Configuration des documents PDF
    PDF_TEMPLATE_DIR = os.path.join(os.path.dirname(__file__), 'templates', 'pdf')
    
    @staticmethod
    def init_app(app):
        """Initialisation spécifique à l'application"""
        pass

class DevelopmentConfig(Config):
    """Configuration de développement"""
    DEBUG = True
    TESTING = False
    
    # JWT plus court en dev
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=8)
    
    # Logs plus verbeux
    LOG_LEVEL = 'DEBUG'

class TestingConfig(Config):
    """Configuration de test"""
    TESTING = True
    DEBUG = True
    
    # Base de données en mémoire pour les tests
    DATABASE_URL = 'sqlite:///:memory:'
    
    # JWT très court pour les tests
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(minutes=15)
    
    # Désactiver la protection CSRF pour les tests
    WTF_CSRF_ENABLED = False

class ProductionConfig(Config):
    """Configuration de production"""
    DEBUG = False
    TESTING = False
    
    # Sécurité renforcée
    BCRYPT_LOG_ROUNDS = 15
    
    # Logs en fichier
    LOG_FILE = '/var/log/propertyflow/app.log'
    
    @classmethod
    def init_app(cls, app):
        Config.init_app(app)
        
        # Log vers syslog en production
        import logging
        from logging.handlers import SysLogHandler
        syslog_handler = SysLogHandler()
        syslog_handler.setLevel(logging.WARNING)
        app.logger.addHandler(syslog_handler)

# Configuration par défaut selon l'environnement
config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
