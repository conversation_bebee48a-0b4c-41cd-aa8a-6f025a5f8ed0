import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';

const CreateRequestModal = ({ 
  isOpen, 
  onClose, 
  onSubmit, 
  properties = [], 
  tenants = [] 
}) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    priority: 'medium',
    category: 'general',
    propertyId: '',
    tenantId: '',
    estimatedCost: '',
    photos: []
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const categories = [
    { value: 'plumbing', label: 'Plomberie', icon: 'Droplets' },
    { value: 'electrical', label: 'Électricité', icon: 'Zap' },
    { value: 'heating', label: 'Chauffage', icon: 'Thermometer' },
    { value: 'appliances', label: 'Électroménager', icon: 'Microwave' },
    { value: 'security', label: 'Sécurité', icon: 'Shield' },
    { value: 'cleaning', label: 'Nettoyage', icon: 'Sparkles' },
    { value: 'painting', label: 'Peinture', icon: 'Paintbrush' },
    { value: 'general', label: 'Général', icon: 'Wrench' }
  ];

  const priorities = [
    { value: 'urgent', label: 'Urgent', color: 'text-error' },
    { value: 'high', label: 'Élevé', color: 'text-warning' },
    { value: 'medium', label: 'Moyen', color: 'text-accent' },
    { value: 'low', label: 'Faible', color: 'text-muted-foreground' }
  ];

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors?.[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const handlePhotoUpload = (event) => {
    const files = Array.from(event?.target?.files);
    const photoUrls = files?.map(file => URL.createObjectURL(file));
    
    setFormData(prev => ({
      ...prev,
      photos: [...prev?.photos, ...photoUrls]
    }));
  };

  const removePhoto = (index) => {
    setFormData(prev => ({
      ...prev,
      photos: prev?.photos?.filter((_, i) => i !== index)
    }));
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData?.title?.trim()) {
      newErrors.title = 'Le titre est requis';
    }

    if (!formData?.description?.trim()) {
      newErrors.description = 'La description est requise';
    }

    if (!formData?.propertyId) {
      newErrors.propertyId = 'Veuillez sélectionner une propriété';
    }

    if (!formData?.tenantId) {
      newErrors.tenantId = 'Veuillez sélectionner un locataire';
    }

    if (formData?.estimatedCost && isNaN(parseFloat(formData?.estimatedCost))) {
      newErrors.estimatedCost = 'Le coût doit être un nombre valide';
    }

    setErrors(newErrors);
    return Object.keys(newErrors)?.length === 0;
  };

  const handleSubmit = async (e) => {
    e?.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      await onSubmit({
        ...formData,
        estimatedCost: formData?.estimatedCost ? parseFloat(formData?.estimatedCost) : null
      });
      
      // Reset form
      setFormData({
        title: '',
        description: '',
        priority: 'medium',
        category: 'general',
        propertyId: '',
        tenantId: '',
        estimatedCost: '',
        photos: []
      });
      
      onClose();
    } catch (error) {
      console.error('Error creating request:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Overlay */}
      <div className="absolute inset-0 bg-black/50" onClick={onClose} />
      {/* Modal */}
      <div className="relative bg-card border border-border rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto m-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border">
          <div className="flex items-center space-x-2">
            <Icon name="Plus" size={20} className="text-primary" />
            <h2 className="text-lg font-semibold text-foreground">Nouvelle demande de maintenance</h2>
          </div>
          <Button variant="ghost" size="icon" onClick={onClose}>
            <Icon name="X" size={20} />
          </Button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Title */}
          <Input
            label="Titre de la demande"
            type="text"
            placeholder="Ex: Fuite d'eau dans la salle de bain"
            value={formData?.title}
            onChange={(e) => handleInputChange('title', e?.target?.value)}
            error={errors?.title}
            required
          />

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              Description détaillée *
            </label>
            <textarea
              value={formData?.description}
              onChange={(e) => handleInputChange('description', e?.target?.value)}
              placeholder="Décrivez le problème en détail..."
              rows={4}
              className={`w-full p-3 border rounded-lg bg-input text-foreground placeholder:text-muted-foreground ${
                errors?.description ? 'border-error' : 'border-border'
              }`}
            />
            {errors?.description && (
              <p className="text-sm text-error mt-1">{errors?.description}</p>
            )}
          </div>

          {/* Category and Priority */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">Catégorie</label>
              <select
                value={formData?.category}
                onChange={(e) => handleInputChange('category', e?.target?.value)}
                className="w-full p-3 border border-border rounded-lg bg-input text-foreground"
              >
                {categories?.map((category) => (
                  <option key={category?.value} value={category?.value}>
                    {category?.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-foreground mb-2">Priorité</label>
              <select
                value={formData?.priority}
                onChange={(e) => handleInputChange('priority', e?.target?.value)}
                className="w-full p-3 border border-border rounded-lg bg-input text-foreground"
              >
                {priorities?.map((priority) => (
                  <option key={priority?.value} value={priority?.value}>
                    {priority?.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Property and Tenant */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">Propriété *</label>
              <select
                value={formData?.propertyId}
                onChange={(e) => handleInputChange('propertyId', e?.target?.value)}
                className={`w-full p-3 border rounded-lg bg-input text-foreground ${
                  errors?.propertyId ? 'border-error' : 'border-border'
                }`}
              >
                <option value="">Sélectionner une propriété</option>
                {properties?.map((property) => (
                  <option key={property?.id} value={property?.id}>
                    {property?.address} - {property?.unit}
                  </option>
                ))}
              </select>
              {errors?.propertyId && (
                <p className="text-sm text-error mt-1">{errors?.propertyId}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-foreground mb-2">Locataire *</label>
              <select
                value={formData?.tenantId}
                onChange={(e) => handleInputChange('tenantId', e?.target?.value)}
                className={`w-full p-3 border rounded-lg bg-input text-foreground ${
                  errors?.tenantId ? 'border-error' : 'border-border'
                }`}
              >
                <option value="">Sélectionner un locataire</option>
                {tenants?.map((tenant) => (
                  <option key={tenant?.id} value={tenant?.id}>
                    {tenant?.name}
                  </option>
                ))}
              </select>
              {errors?.tenantId && (
                <p className="text-sm text-error mt-1">{errors?.tenantId}</p>
              )}
            </div>
          </div>

          {/* Estimated Cost */}
          <Input
            label="Coût estimé (€)"
            type="number"
            placeholder="0.00"
            value={formData?.estimatedCost}
            onChange={(e) => handleInputChange('estimatedCost', e?.target?.value)}
            error={errors?.estimatedCost}
            min="0"
            step="0.01"
          />

          {/* Photo Upload */}
          <div>
            <label className="block text-sm font-medium text-foreground mb-2">Photos</label>
            <div className="border-2 border-dashed border-border rounded-lg p-4">
              <input
                type="file"
                multiple
                accept="image/*"
                onChange={handlePhotoUpload}
                className="hidden"
                id="photo-upload"
              />
              <label
                htmlFor="photo-upload"
                className="flex flex-col items-center justify-center cursor-pointer"
              >
                <Icon name="Camera" size={32} className="text-muted-foreground mb-2" />
                <p className="text-sm text-muted-foreground text-center">
                  Cliquez pour ajouter des photos ou glissez-déposez
                </p>
              </label>
            </div>

            {/* Photo Preview */}
            {formData?.photos?.length > 0 && (
              <div className="grid grid-cols-3 gap-2 mt-3">
                {formData?.photos?.map((photo, index) => (
                  <div key={index} className="relative">
                    <img
                      src={photo}
                      alt={`Photo ${index + 1}`}
                      className="w-full h-20 object-cover rounded border"
                    />
                    <button
                      type="button"
                      onClick={() => removePhoto(index)}
                      className="absolute -top-2 -right-2 bg-error text-error-foreground rounded-full w-6 h-6 flex items-center justify-center text-xs"
                    >
                      <Icon name="X" size={12} />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex items-center justify-end space-x-3 pt-6 border-t border-border">
            <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
              Annuler
            </Button>
            <Button type="submit" loading={isSubmitting}>
              <Icon name="Plus" size={16} className="mr-2" />
              Créer la demande
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateRequestModal;