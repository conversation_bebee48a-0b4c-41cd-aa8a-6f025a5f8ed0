import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const TenantActionBar = ({ 
  onAddTenant = () => {},
  onExport = () => {},
  onBulkAction = () => {},
  selectedCount = 0,
  totalCount = 0,
  className = ""
}) => {
  const [showExportMenu, setShowExportMenu] = useState(false);
  const [showBulkMenu, setShowBulkMenu] = useState(false);

  const exportOptions = [
    { id: 'excel', label: 'Excel (.xlsx)', icon: 'FileSpreadsheet' },
    { id: 'csv', label: 'CSV (.csv)', icon: 'FileText' },
    { id: 'pdf', label: 'PDF (.pdf)', icon: 'FileText' }
  ];

  const bulkActions = [
    { id: 'message', label: 'Envoyer un message', icon: 'Mail' },
    { id: 'payment_reminder', label: 'Rappel de paiement', icon: 'CreditCard' },
    { id: 'lease_renewal', label: 'Renouvellement de bail', icon: 'FileText' },
    { id: 'export_selected', label: 'Exporter sélection', icon: 'Download' }
  ];

  const handleExport = (format) => {
    onExport(format);
    setShowExportMenu(false);
  };

  const handleBulkAction = (action) => {
    onBulkAction(action);
    setShowBulkMenu(false);
  };

  return (
    <div className={`flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 ${className}`}>
      {/* Left Side - Stats and Selection Info */}
      <div className="flex items-center gap-4">
        <div className="text-sm text-muted-foreground">
          {selectedCount > 0 ? (
            <span className="font-medium text-primary">
              {selectedCount} locataire(s) sélectionné(s)
            </span>
          ) : (
            <span>
              {totalCount} locataire(s) au total
            </span>
          )}
        </div>
      </div>
      {/* Right Side - Actions */}
      <div className="flex items-center gap-2">
        {/* Bulk Actions (shown when items are selected) */}
        {selectedCount > 0 && (
          <div className="relative">
            <Button
              variant="outline"
              onClick={() => setShowBulkMenu(!showBulkMenu)}
              iconName="MoreHorizontal"
              iconPosition="left"
            >
              Actions groupées
            </Button>

            {showBulkMenu && (
              <div className="absolute right-0 top-12 w-56 bg-popover border border-border rounded-lg elevation-3 z-50 animate-slide-in">
                <div className="py-2">
                  {bulkActions?.map((action) => (
                    <button
                      key={action?.id}
                      onClick={() => handleBulkAction(action?.id)}
                      className="flex items-center w-full px-3 py-2 text-sm text-popover-foreground hover:bg-muted transition-smooth"
                    >
                      <Icon name={action?.icon} size={16} className="mr-3" />
                      {action?.label}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Export Menu */}
        <div className="relative">
          <Button
            variant="outline"
            onClick={() => setShowExportMenu(!showExportMenu)}
            iconName="Download"
            iconPosition="left"
          >
            Exporter
          </Button>

          {showExportMenu && (
            <div className="absolute right-0 top-12 w-48 bg-popover border border-border rounded-lg elevation-3 z-50 animate-slide-in">
              <div className="py-2">
                {exportOptions?.map((option) => (
                  <button
                    key={option?.id}
                    onClick={() => handleExport(option?.id)}
                    className="flex items-center w-full px-3 py-2 text-sm text-popover-foreground hover:bg-muted transition-smooth"
                  >
                    <Icon name={option?.icon} size={16} className="mr-3" />
                    {option?.label}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Add Tenant Button */}
        <Button
          variant="default"
          onClick={onAddTenant}
          iconName="Plus"
          iconPosition="left"
        >
          Ajouter un locataire
        </Button>
      </div>
      {/* Click outside to close menus */}
      {(showExportMenu || showBulkMenu) && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => {
            setShowExportMenu(false);
            setShowBulkMenu(false);
          }}
        />
      )}
    </div>
  );
};

export default TenantActionBar;