"""
PropertyFlow Backend - API Paiements
Gestion des paiements de loyer avec logique métier
"""

from flask import Blueprint, request, jsonify, g
from flask_jwt_extended import jwt_required
import logging
from datetime import datetime, timedelta
from database import paiement_service, contrat_service
from utils.errors import NotFoundError, ValidationError, AuthorizationError, BusinessLogicError
from utils.middleware import require_agence_access, validate_json, log_activity

logger = logging.getLogger(__name__)

# Blueprint pour les paiements
paiements_bp = Blueprint('paiements', __name__)

@paiements_bp.route('', methods=['GET'])
@jwt_required()
@require_agence_access
@log_activity('list_paiements')
def get_paiements():
    """Récupérer tous les paiements de l'agence"""
    try:
        # Paramètres de filtrage
        statut = request.args.get('statut')
        contrat_id = request.args.get('contrat_id')
        locataire_id = request.args.get('locataire_id')
        mois_annee = request.args.get('mois_annee')
        mode_paiement = request.args.get('mode_paiement')
        
        filters = {'agence_id': g.agence_id}
        if statut:
            filters['statut'] = statut
        if contrat_id:
            filters['contrat_id'] = contrat_id
        if locataire_id:
            filters['locataire_id'] = locataire_id
        if mois_annee:
            filters['mois_annee'] = mois_annee
        if mode_paiement:
            filters['mode_paiement'] = mode_paiement
        
        paiements = paiement_service.get_all(filters=filters)
        
        return jsonify({
            'success': True,
            'data': paiements,
            'total': len(paiements)
        }), 200
        
    except Exception as e:
        logger.error(f"Get paiements error: {str(e)}")
        raise

@paiements_bp.route('/<paiement_id>', methods=['GET'])
@jwt_required()
@require_agence_access
@log_activity('get_paiement')
def get_paiement(paiement_id):
    """Récupérer un paiement par ID"""
    try:
        paiement = paiement_service.get_by_id(paiement_id)
        if not paiement:
            raise NotFoundError("Paiement not found")
        
        # Vérifier que le paiement appartient à l'agence
        if paiement['agence_id'] != g.agence_id:
            raise AuthorizationError("Access denied to this paiement")
        
        return jsonify({
            'success': True,
            'data': paiement
        }), 200
        
    except Exception as e:
        logger.error(f"Get paiement error: {str(e)}")
        raise

@paiements_bp.route('', methods=['POST'])
@jwt_required()
@require_agence_access
@validate_json(['contrat_id', 'periode_debut', 'periode_fin', 'montant_loyer', 'date_echeance'])
@log_activity('create_paiement')
def create_paiement():
    """Créer un nouveau paiement (échéance)"""
    try:
        data = request.get_json()
        
        # Vérifier que le contrat existe et appartient à l'agence
        contrat = contrat_service.get_by_id(data['contrat_id'])
        if not contrat:
            raise NotFoundError("Contrat not found")
        
        if contrat['agence_id'] != g.agence_id:
            raise AuthorizationError("Contrat does not belong to your agency")
        
        if contrat['statut'] != 'actif':
            raise BusinessLogicError("Contract is not active")
        
        # Calculer le mois_annee
        periode_debut = datetime.fromisoformat(data['periode_debut'].replace('Z', '+00:00'))
        mois_annee = periode_debut.strftime('%Y-%m')
        
        # Vérifier qu'il n'y a pas déjà un paiement pour cette période
        existing_paiement = paiement_service.get_all(filters={
            'contrat_id': data['contrat_id'],
            'mois_annee': mois_annee
        })
        
        if existing_paiement:
            raise BusinessLogicError("Payment already exists for this period")
        
        # Calculer les montants
        montant_loyer = data['montant_loyer']
        montant_charges = data.get('montant_charges', 0)
        montant_total = montant_loyer + montant_charges
        
        # Créer le paiement
        paiement_data = {
            'agence_id': g.agence_id,
            'contrat_id': data['contrat_id'],
            'locataire_id': contrat['locataire_id'],
            'periode_debut': data['periode_debut'],
            'periode_fin': data['periode_fin'],
            'mois_annee': mois_annee,
            'montant_loyer': montant_loyer,
            'montant_charges': montant_charges,
            'montant_total': montant_total,
            'montant_paye': 0,
            'mode_paiement': data.get('mode_paiement'),
            'reference_transaction': data.get('reference_transaction'),
            'date_echeance': data['date_echeance'],
            'reconcilie': False,
            'statut': 'en_attente',
            'retard_jours': 0,
            'notes': data.get('notes')
        }
        
        new_paiement = paiement_service.create(paiement_data)
        
        logger.info(f"New paiement created: {new_paiement['id']}")
        
        return jsonify({
            'success': True,
            'message': 'Paiement created successfully',
            'data': new_paiement
        }), 201
        
    except Exception as e:
        logger.error(f"Create paiement error: {str(e)}")
        raise

@paiements_bp.route('/<paiement_id>/record-payment', methods=['POST'])
@jwt_required()
@require_agence_access
@validate_json(['montant_paye', 'mode_paiement', 'date_paiement'])
@log_activity('record_payment')
def record_payment(paiement_id):
    """Enregistrer un paiement reçu"""
    try:
        paiement = paiement_service.get_by_id(paiement_id)
        if not paiement:
            raise NotFoundError("Paiement not found")
        
        if paiement['agence_id'] != g.agence_id:
            raise AuthorizationError("Access denied to this paiement")
        
        data = request.get_json()
        montant_paye = data['montant_paye']
        
        # Calculer le nouveau montant total payé
        nouveau_montant_paye = paiement['montant_paye'] + montant_paye
        
        if nouveau_montant_paye > paiement['montant_total']:
            raise BusinessLogicError("Payment amount exceeds total due")
        
        # Déterminer le nouveau statut
        if nouveau_montant_paye >= paiement['montant_total']:
            nouveau_statut = 'paye'
        elif nouveau_montant_paye > 0:
            nouveau_statut = 'partiel'
        else:
            nouveau_statut = 'en_attente'
        
        # Calculer les jours de retard
        date_echeance = datetime.fromisoformat(paiement['date_echeance'].replace('Z', '+00:00'))
        date_paiement = datetime.fromisoformat(data['date_paiement'].replace('Z', '+00:00'))
        retard_jours = max(0, (date_paiement - date_echeance).days)
        
        # Générer un numéro de quittance si paiement complet
        numero_quittance = None
        if nouveau_statut == 'paye':
            # TODO: Utiliser la fonction SQL generate_receipt_number
            numero_quittance = f"QUI-{datetime.now().strftime('%Y%m')}-{paiement_id[:8]}"
        
        # Mettre à jour le paiement
        update_data = {
            'montant_paye': nouveau_montant_paye,
            'mode_paiement': data['mode_paiement'],
            'reference_transaction': data.get('reference_transaction'),
            'date_paiement': data['date_paiement'],
            'date_encaissement': data.get('date_encaissement'),
            'statut': nouveau_statut,
            'retard_jours': retard_jours,
            'numero_quittance': numero_quittance,
            'reconcilie': data.get('reconcilie', False),
            'reference_bancaire': data.get('reference_bancaire'),
            'notes': data.get('notes')
        }
        
        updated_paiement = paiement_service.update(paiement_id, update_data)
        
        logger.info(f"Payment recorded: {paiement_id} - Amount: {montant_paye}")
        
        return jsonify({
            'success': True,
            'message': 'Payment recorded successfully',
            'data': updated_paiement
        }), 200
        
    except Exception as e:
        logger.error(f"Record payment error: {str(e)}")
        raise

@paiements_bp.route('/<paiement_id>/generate-receipt', methods=['POST'])
@jwt_required()
@require_agence_access
@log_activity('generate_receipt')
def generate_receipt(paiement_id):
    """Générer une quittance de loyer"""
    try:
        paiement = paiement_service.get_by_id(paiement_id)
        if not paiement:
            raise NotFoundError("Paiement not found")
        
        if paiement['agence_id'] != g.agence_id:
            raise AuthorizationError("Access denied to this paiement")
        
        if paiement['statut'] != 'paye':
            raise BusinessLogicError("Can only generate receipt for paid payments")
        
        # TODO: Implémenter la génération PDF avec ReportLab
        quittance_url = f"/documents/quittances/{paiement_id}.pdf"
        
        # Mettre à jour l'URL de la quittance
        paiement_service.update(paiement_id, {
            'quittance_pdf_url': quittance_url,
            'date_generation_quittance': datetime.utcnow().isoformat()
        })
        
        return jsonify({
            'success': True,
            'message': 'Receipt generated successfully',
            'quittance_url': quittance_url
        }), 200
        
    except Exception as e:
        logger.error(f"Generate receipt error: {str(e)}")
        raise

@paiements_bp.route('/generate-monthly', methods=['POST'])
@jwt_required()
@require_agence_access
@validate_json(['mois_annee'])
@log_activity('generate_monthly_payments')
def generate_monthly_payments(mois_annee=None):
    """Générer les échéances mensuelles pour tous les contrats actifs"""
    try:
        data = request.get_json()
        mois_annee = data['mois_annee']
        
        # Récupérer tous les contrats actifs de l'agence
        contrats_actifs = contrat_service.get_active_contracts(g.agence_id)
        
        created_count = 0
        errors = []
        
        for contrat in contrats_actifs:
            try:
                # Vérifier qu'il n'y a pas déjà un paiement pour cette période
                existing_paiement = paiement_service.get_all(filters={
                    'contrat_id': contrat['id'],
                    'mois_annee': mois_annee
                })
                
                if existing_paiement:
                    continue
                
                # Calculer les dates de période
                annee, mois = map(int, mois_annee.split('-'))
                periode_debut = datetime(annee, mois, 1)
                
                # Dernier jour du mois
                if mois == 12:
                    periode_fin = datetime(annee + 1, 1, 1) - timedelta(days=1)
                else:
                    periode_fin = datetime(annee, mois + 1, 1) - timedelta(days=1)
                
                # Date d'échéance (généralement le 5 du mois)
                date_echeance = datetime(annee, mois, 5)
                
                # Créer le paiement
                paiement_data = {
                    'agence_id': g.agence_id,
                    'contrat_id': contrat['id'],
                    'locataire_id': contrat['locataire_id'],
                    'periode_debut': periode_debut.isoformat(),
                    'periode_fin': periode_fin.isoformat(),
                    'mois_annee': mois_annee,
                    'montant_loyer': contrat['loyer_mensuel'],
                    'montant_charges': contrat['charges_mensuelles'],
                    'montant_total': contrat['loyer_mensuel'] + contrat['charges_mensuelles'],
                    'montant_paye': 0,
                    'date_echeance': date_echeance.isoformat(),
                    'statut': 'en_attente',
                    'retard_jours': 0
                }
                
                paiement_service.create(paiement_data)
                created_count += 1
                
            except Exception as e:
                errors.append(f"Contract {contrat['numero_contrat']}: {str(e)}")
        
        logger.info(f"Generated {created_count} monthly payments for {mois_annee}")
        
        return jsonify({
            'success': True,
            'message': f'Generated {created_count} monthly payments',
            'created_count': created_count,
            'errors': errors
        }), 200
        
    except Exception as e:
        logger.error(f"Generate monthly payments error: {str(e)}")
        raise

@paiements_bp.route('/overdue', methods=['GET'])
@jwt_required()
@require_agence_access
@log_activity('get_overdue_payments')
def get_overdue_payments():
    """Récupérer les paiements en retard"""
    try:
        # Calculer les jours de retard pour tous les paiements en attente
        today = datetime.now().date()
        
        paiements = paiement_service.get_all(filters={
            'agence_id': g.agence_id,
            'statut': ['en_attente', 'partiel']
        })
        
        overdue_payments = []
        for paiement in paiements:
            date_echeance = datetime.fromisoformat(paiement['date_echeance'].replace('Z', '+00:00')).date()
            if date_echeance < today:
                retard_jours = (today - date_echeance).days
                paiement['retard_jours'] = retard_jours
                
                # Mettre à jour le statut si nécessaire
                if retard_jours > 0 and paiement['statut'] == 'en_attente':
                    paiement_service.update(paiement['id'], {
                        'statut': 'en_retard',
                        'retard_jours': retard_jours
                    })
                    paiement['statut'] = 'en_retard'
                
                overdue_payments.append(paiement)
        
        # Trier par nombre de jours de retard (décroissant)
        overdue_payments.sort(key=lambda x: x['retard_jours'], reverse=True)
        
        return jsonify({
            'success': True,
            'data': overdue_payments,
            'total': len(overdue_payments)
        }), 200
        
    except Exception as e:
        logger.error(f"Get overdue payments error: {str(e)}")
        raise
