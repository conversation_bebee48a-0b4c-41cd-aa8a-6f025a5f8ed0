import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const FinancialToolbar = ({ 
  onDateRangeChange = () => {},
  onExport = () => {},
  onGenerateReport = () => {},
  className = ""
}) => {
  const [dateRange, setDateRange] = useState('12m');
  const [showExportMenu, setShowExportMenu] = useState(false);
  const [showReportMenu, setShowReportMenu] = useState(false);

  const dateRangeOptions = [
    { value: '1m', label: '1 mois' },
    { value: '3m', label: '3 mois' },
    { value: '6m', label: '6 mois' },
    { value: '12m', label: '12 mois' },
    { value: '24m', label: '2 ans' },
    { value: 'custom', label: 'Personnalisé' }
  ];

  const exportOptions = [
    { value: 'pdf', label: 'PDF', icon: 'FileText' },
    { value: 'excel', label: 'Excel', icon: 'Table' },
    { value: 'csv', label: 'CSV', icon: 'Database' }
  ];

  const reportTypes = [
    { value: 'monthly', label: 'Rapport mensuel', icon: 'Calendar' },
    { value: 'quarterly', label: 'Rapport trimestriel', icon: 'BarChart3' },
    { value: 'annual', label: 'Rapport annuel', icon: 'TrendingUp' },
    { value: 'tax', label: 'Documents fiscaux', icon: 'Receipt' }
  ];

  const handleDateRangeChange = (value) => {
    setDateRange(value);
    onDateRangeChange(value);
  };

  const handleExport = (format) => {
    onExport(format);
    setShowExportMenu(false);
  };

  const handleGenerateReport = (type) => {
    onGenerateReport(type);
    setShowReportMenu(false);
  };

  return (
    <div className={`bg-card border border-border rounded-lg p-4 mb-6 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {/* Date Range Selector */}
          <div className="flex items-center space-x-2">
            <Icon name="Calendar" size={16} className="text-muted-foreground" />
            <span className="text-sm font-medium text-foreground">Période:</span>
            <div className="flex bg-muted rounded-lg p-1">
              {dateRangeOptions?.map((option) => (
                <button
                  key={option?.value}
                  onClick={() => handleDateRangeChange(option?.value)}
                  className={`px-3 py-1 text-xs font-medium rounded transition-smooth ${
                    dateRange === option?.value
                      ? 'bg-background text-foreground shadow-sm'
                      : 'text-muted-foreground hover:text-foreground'
                  }`}
                >
                  {option?.label}
                </button>
              ))}
            </div>
          </div>

          {/* Quick Filters */}
          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="sm">
              <Icon name="Filter" size={14} className="mr-2" />
              Filtres
            </Button>
            <Button variant="ghost" size="sm">
              <Icon name="Search" size={14} className="mr-2" />
              Rechercher
            </Button>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {/* Export Menu */}
          <div className="relative">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setShowExportMenu(!showExportMenu);
                setShowReportMenu(false);
              }}
            >
              <Icon name="Download" size={14} className="mr-2" />
              Exporter
              <Icon name="ChevronDown" size={14} className="ml-2" />
            </Button>

            {showExportMenu && (
              <div className="absolute right-0 top-10 w-48 bg-popover border border-border rounded-lg elevation-3 z-50 animate-slide-in">
                <div className="p-2">
                  {exportOptions?.map((option) => (
                    <button
                      key={option?.value}
                      onClick={() => handleExport(option?.value)}
                      className="flex items-center w-full px-3 py-2 text-sm text-popover-foreground hover:bg-muted rounded transition-smooth"
                    >
                      <Icon name={option?.icon} size={16} className="mr-3" />
                      {option?.label}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Report Generation Menu */}
          <div className="relative">
            <Button
              variant="default"
              size="sm"
              onClick={() => {
                setShowReportMenu(!showReportMenu);
                setShowExportMenu(false);
              }}
            >
              <Icon name="FileText" size={14} className="mr-2" />
              Générer rapport
              <Icon name="ChevronDown" size={14} className="ml-2" />
            </Button>

            {showReportMenu && (
              <div className="absolute right-0 top-10 w-56 bg-popover border border-border rounded-lg elevation-3 z-50 animate-slide-in">
                <div className="p-2">
                  {reportTypes?.map((report) => (
                    <button
                      key={report?.value}
                      onClick={() => handleGenerateReport(report?.value)}
                      className="flex items-center w-full px-3 py-2 text-sm text-popover-foreground hover:bg-muted rounded transition-smooth"
                    >
                      <Icon name={report?.icon} size={16} className="mr-3" />
                      {report?.label}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Refresh Button */}
          <Button variant="ghost" size="sm">
            <Icon name="RefreshCw" size={14} />
          </Button>
        </div>
      </div>
      {/* Click outside to close menus */}
      {(showExportMenu || showReportMenu) && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => {
            setShowExportMenu(false);
            setShowReportMenu(false);
          }}
        />
      )}
    </div>
  );
};

export default FinancialToolbar;