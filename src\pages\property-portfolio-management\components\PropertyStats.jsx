import React from 'react';
import Icon from '../../../components/AppIcon';

const PropertyStats = ({ properties = [], className = "" }) => {
  const calculateStats = () => {
    const total = properties?.length;
    const occupied = properties?.filter(p => p?.status === 'occupied')?.length;
    const vacant = properties?.filter(p => p?.status === 'vacant')?.length;
    const maintenance = properties?.filter(p => p?.status === 'maintenance')?.length;
    
    const totalRent = properties?.filter(p => p?.status === 'occupied')?.reduce((sum, p) => sum + p?.monthlyRent, 0);
    
    const occupancyRate = total > 0 ? (occupied / total) * 100 : 0;
    
    return {
      total,
      occupied,
      vacant,
      maintenance,
      totalRent,
      occupancyRate
    };
  };

  const stats = calculateStats();

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    })?.format(amount);
  };

  const statCards = [
    {
      label: 'Total propriétés',
      value: stats?.total,
      icon: 'Building2',
      color: 'text-primary bg-primary/10'
    },
    {
      label: 'Propriétés occupées',
      value: stats?.occupied,
      icon: 'CheckCircle',
      color: 'text-success bg-success/10'
    },
    {
      label: 'Propriétés vacantes',
      value: stats?.vacant,
      icon: 'Circle',
      color: 'text-warning bg-warning/10'
    },
    {
      label: 'En maintenance',
      value: stats?.maintenance,
      icon: 'Wrench',
      color: 'text-error bg-error/10'
    },
    {
      label: 'Revenus mensuels',
      value: formatCurrency(stats?.totalRent),
      icon: 'TrendingUp',
      color: 'text-accent bg-accent/10'
    },
    {
      label: 'Taux d\'occupation',
      value: `${stats?.occupancyRate?.toFixed(1)}%`,
      icon: 'Percent',
      color: 'text-secondary bg-secondary/10'
    }
  ];

  return (
    <div className={`grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 ${className}`}>
      {statCards?.map((stat, index) => (
        <div key={index} className="bg-card border border-border rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${stat?.color}`}>
              <Icon name={stat?.icon} size={20} />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm text-muted-foreground truncate">{stat?.label}</p>
              <p className="text-lg font-semibold text-foreground">{stat?.value}</p>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default PropertyStats;