import React, { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import Icon from '../AppIcon';
import Button from './Button';

const PrimaryNavigation = ({ className = "" }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const navigationItems = [
    {
      label: 'Tableau de Bord',
      path: '/agency-dashboard',
      icon: 'LayoutDashboard',
      tooltip: 'Vue d\'ensemble du portefeuille'
    },
    {
      label: 'Propriétés',
      path: '/property-portfolio-management',
      icon: 'Building2',
      tooltip: 'Gestion du portefeuille immobilier'
    },
    {
      label: 'Bailleurs',
      path: '/property-owner-management',
      icon: 'UserCheck',
      tooltip: 'Gestion des propriétaires bailleurs'
    },
    {
      label: 'Contrats',
      path: '/contract-management-system',
      icon: 'FileText',
      tooltip: 'Gestion des contrats et baux'
    },
    {
      label: 'Locataires',
      path: '/tenant-management-hub',
      icon: 'Users',
      tooltip: 'Gestion des locataires et baux'
    },
    {
      label: 'Inspections',
      path: '/property-inspection-condition-reports',
      icon: 'ClipboardList',
      tooltip: 'États des lieux et inspections'
    },
    {
      label: 'Maintenance',
      path: '/maintenance-request-tracking',
      icon: 'Wrench',
      tooltip: 'Suivi des demandes de maintenance'
    },
    {
      label: 'Finances',
      path: '/financial-dashboard-reporting',
      icon: 'TrendingUp',
      tooltip: 'Analyses financières et rapports'
    }
  ];

  const handleNavigation = (path) => {
    navigate(path);
    setIsMobileMenuOpen(false);
  };

  const isActive = (path) => {
    return location?.pathname === path;
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <>
      {/* Desktop Navigation */}
      <nav className={`hidden md:block fixed top-16 left-0 right-0 z-40 bg-card border-b border-border ${className}`}>
        <div className="px-6">
          <div className="flex items-center space-x-1">
            {navigationItems?.map((item) => (
              <button
                key={item?.path}
                onClick={() => handleNavigation(item?.path)}
                className={`flex items-center space-x-2 px-4 py-3 text-sm font-medium transition-smooth relative group ${
                  isActive(item?.path)
                    ? 'text-primary bg-primary/5 border-b-2 border-primary' :'text-muted-foreground hover:text-foreground hover:bg-muted'
                }`}
                title={item?.tooltip}
              >
                <Icon name={item?.icon} size={18} />
                <span>{item?.label}</span>
                
                {/* Tooltip */}
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-popover text-popover-foreground text-xs rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap">
                  {item?.tooltip}
                </div>
              </button>
            ))}
          </div>
        </div>
      </nav>
      {/* Mobile Navigation */}
      <div className="md:hidden fixed top-16 left-0 right-0 z-40 bg-card border-b border-border">
        <div className="px-4 py-2">
          <Button
            variant="ghost"
            onClick={toggleMobileMenu}
            className="flex items-center justify-between w-full"
          >
            <div className="flex items-center space-x-2">
              <Icon name="Menu" size={20} />
              <span className="font-medium">Menu</span>
            </div>
            <Icon 
              name={isMobileMenuOpen ? "ChevronUp" : "ChevronDown"} 
              size={16} 
              className="text-muted-foreground" 
            />
          </Button>
        </div>

        {/* Mobile Menu Dropdown */}
        {isMobileMenuOpen && (
          <div className="bg-card border-t border-border animate-slide-in">
            <div className="py-2">
              {navigationItems?.map((item) => (
                <button
                  key={item?.path}
                  onClick={() => handleNavigation(item?.path)}
                  className={`flex items-center space-x-3 w-full px-4 py-3 text-sm font-medium transition-smooth ${
                    isActive(item?.path)
                      ? 'text-primary bg-primary/5 border-r-2 border-primary' :'text-muted-foreground hover:text-foreground hover:bg-muted'
                  }`}
                >
                  <Icon name={item?.icon} size={18} />
                  <div className="flex-1 text-left">
                    <div>{item?.label}</div>
                    <div className="text-xs text-muted-foreground mt-1">
                      {item?.tooltip}
                    </div>
                  </div>
                  {isActive(item?.path) && (
                    <Icon name="Check" size={16} className="text-primary" />
                  )}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div 
          className="fixed inset-0 z-30 md:hidden" 
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}
    </>
  );
};

export default PrimaryNavigation;