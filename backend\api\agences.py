"""
PropertyFlow Backend - API Agences
Gestion des agences immobilières (tenants)
"""

from flask import Blueprint, request, jsonify, g
from flask_jwt_extended import jwt_required
import logging
from database import agence_service
from utils.errors import NotFoundError, ValidationError, AuthorizationError
from utils.middleware import require_role, validate_json, log_activity

logger = logging.getLogger(__name__)

# Blueprint pour les agences
agences_bp = Blueprint('agences', __name__)

@agences_bp.route('', methods=['GET'])
@jwt_required()
@require_role('super_admin')
@log_activity('list_agencies')
def get_agences():
    """Récupérer toutes les agences (super_admin seulement)"""
    try:
        agences = agence_service.get_all()
        
        return jsonify({
            'success': True,
            'data': agences,
            'total': len(agences)
        }), 200
        
    except Exception as e:
        logger.error(f"Get agencies error: {str(e)}")
        raise

@agences_bp.route('/<agence_id>', methods=['GET'])
@jwt_required()
@log_activity('get_agency')
def get_agence(agence_id):
    """Récupérer une agence par ID"""
    try:
        # Vérifier les permissions
        if g.user_role != 'super_admin' and g.agence_id != agence_id:
            raise AuthorizationError("Access denied to this agency")
        
        agence = agence_service.get_by_id(agence_id)
        if not agence:
            raise NotFoundError("Agency not found")
        
        return jsonify({
            'success': True,
            'data': agence
        }), 200
        
    except Exception as e:
        logger.error(f"Get agency error: {str(e)}")
        raise

@agences_bp.route('', methods=['POST'])
@jwt_required()
@require_role('super_admin')
@validate_json(['nom', 'email'])
@log_activity('create_agency')
def create_agence():
    """Créer une nouvelle agence"""
    try:
        data = request.get_json()
        
        # Vérifier si l'email existe déjà
        existing_agence = agence_service.get_by_email(data['email'])
        if existing_agence:
            raise ValidationError("Email already exists")
        
        # Créer l'agence
        agence_data = {
            'nom': data['nom'],
            'email': data['email'].lower().strip(),
            'adresse': data.get('adresse'),
            'telephone': data.get('telephone'),
            'siret': data.get('siret'),
            'taux_gerance_defaut': data.get('taux_gerance_defaut', 8.00),
            'modes_paiement': data.get('modes_paiement', ["especes", "virement", "orange_money", "wave"]),
            'logo_url': data.get('logo_url'),
            'parametres': data.get('parametres', {}),
            'statut': 'actif'
        }
        
        new_agence = agence_service.create(agence_data)
        
        logger.info(f"New agency created: {new_agence['id']}")
        
        return jsonify({
            'success': True,
            'message': 'Agency created successfully',
            'data': new_agence
        }), 201
        
    except Exception as e:
        logger.error(f"Create agency error: {str(e)}")
        raise

@agences_bp.route('/<agence_id>', methods=['PUT'])
@jwt_required()
@validate_json()
@log_activity('update_agency')
def update_agence(agence_id):
    """Mettre à jour une agence"""
    try:
        # Vérifier les permissions
        if g.user_role not in ['super_admin', 'admin_agence'] or (g.user_role == 'admin_agence' and g.agence_id != agence_id):
            raise AuthorizationError("Insufficient permissions")
        
        # Vérifier que l'agence existe
        agence = agence_service.get_by_id(agence_id)
        if not agence:
            raise NotFoundError("Agency not found")
        
        data = request.get_json()
        
        # Champs modifiables
        allowed_fields = [
            'nom', 'adresse', 'telephone', 'siret', 'taux_gerance_defaut',
            'modes_paiement', 'logo_url', 'parametres'
        ]
        
        # Super admin peut modifier le statut et l'email
        if g.user_role == 'super_admin':
            allowed_fields.extend(['email', 'statut'])
        
        update_data = {k: v for k, v in data.items() if k in allowed_fields}
        
        if not update_data:
            raise ValidationError("No valid fields to update")
        
        # Vérifier l'unicité de l'email si modifié
        if 'email' in update_data:
            existing_agence = agence_service.get_by_email(update_data['email'])
            if existing_agence and existing_agence['id'] != agence_id:
                raise ValidationError("Email already exists")
        
        updated_agence = agence_service.update(agence_id, update_data)
        
        logger.info(f"Agency updated: {agence_id}")
        
        return jsonify({
            'success': True,
            'message': 'Agency updated successfully',
            'data': updated_agence
        }), 200
        
    except Exception as e:
        logger.error(f"Update agency error: {str(e)}")
        raise

@agences_bp.route('/<agence_id>', methods=['DELETE'])
@jwt_required()
@require_role('super_admin')
@log_activity('delete_agency')
def delete_agence(agence_id):
    """Supprimer une agence (soft delete)"""
    try:
        agence = agence_service.get_by_id(agence_id)
        if not agence:
            raise NotFoundError("Agency not found")
        
        # Soft delete - changer le statut
        agence_service.update(agence_id, {'statut': 'inactif'})
        
        logger.info(f"Agency deleted: {agence_id}")
        
        return jsonify({
            'success': True,
            'message': 'Agency deleted successfully'
        }), 200
        
    except Exception as e:
        logger.error(f"Delete agency error: {str(e)}")
        raise

@agences_bp.route('/<agence_id>/stats', methods=['GET'])
@jwt_required()
@log_activity('get_agency_stats')
def get_agence_stats(agence_id):
    """Récupérer les statistiques d'une agence"""
    try:
        # Vérifier les permissions
        if g.user_role != 'super_admin' and g.agence_id != agence_id:
            raise AuthorizationError("Access denied to this agency")
        
        agence = agence_service.get_by_id(agence_id)
        if not agence:
            raise NotFoundError("Agency not found")
        
        # TODO: Implémenter les vraies statistiques
        stats = {
            'total_biens': 0,
            'biens_occupes': 0,
            'biens_disponibles': 0,
            'total_locataires': 0,
            'total_bailleurs': 0,
            'revenus_mois_courant': 0,
            'revenus_annuels': 0,
            'taux_occupation': 0
        }
        
        return jsonify({
            'success': True,
            'data': stats
        }), 200
        
    except Exception as e:
        logger.error(f"Get agency stats error: {str(e)}")
        raise
