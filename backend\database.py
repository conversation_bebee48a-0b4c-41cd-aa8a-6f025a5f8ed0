"""
Configuration et gestion de la base de données Supabase
"""

import os
from supabase import create_client, Client
from typing import Optional, Dict, Any, List
import logging

logger = logging.getLogger(__name__)

# Client Supabase global
supabase: Optional[Client] = None

def init_db(app):
    """Initialiser la connexion Supabase"""
    global supabase
    
    supabase_url = app.config.get('SUPABASE_URL')
    supabase_key = app.config.get('SUPABASE_KEY')
    
    if not supabase_url or not supabase_key:
        logger.error("SUPABASE_URL et SUPABASE_KEY doivent être configurés")
        raise ValueError("Configuration Supabase manquante")
    
    try:
        supabase = create_client(supabase_url, supabase_key)
        logger.info("Connexion Supabase initialisée avec succès")
    except Exception as e:
        logger.error(f"Erreur lors de l'initialisation Supabase: {e}")
        raise

def get_supabase_client() -> Client:
    """Obtenir le client Supabase"""
    if supabase is None:
        raise RuntimeError("Supabase n'est pas initialisé")
    return supabase

class DatabaseService:
    """Service de base pour les opérations de base de données"""
    
    def __init__(self, table_name: str):
        self.table_name = table_name
        self.client = get_supabase_client()
    
    def create(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Créer un nouvel enregistrement"""
        try:
            result = self.client.table(self.table_name).insert(data).execute()
            if result.data:
                return result.data[0]
            else:
                raise Exception("Aucune donnée retournée après insertion")
        except Exception as e:
            logger.error(f"Erreur lors de la création dans {self.table_name}: {e}")
            raise
    
    def get_by_id(self, record_id: str) -> Optional[Dict[str, Any]]:
        """Récupérer un enregistrement par ID"""
        try:
            result = self.client.table(self.table_name).select("*").eq("id", record_id).execute()
            return result.data[0] if result.data else None
        except Exception as e:
            logger.error(f"Erreur lors de la récupération par ID dans {self.table_name}: {e}")
            raise
    
    def get_all(self, filters: Optional[Dict[str, Any]] = None, 
                limit: Optional[int] = None, 
                offset: Optional[int] = None,
                order_by: Optional[str] = None) -> List[Dict[str, Any]]:
        """Récupérer tous les enregistrements avec filtres optionnels"""
        try:
            query = self.client.table(self.table_name).select("*")
            
            # Appliquer les filtres
            if filters:
                for key, value in filters.items():
                    if isinstance(value, list):
                        query = query.in_(key, value)
                    else:
                        query = query.eq(key, value)
            
            # Tri
            if order_by:
                if order_by.startswith('-'):
                    query = query.order(order_by[1:], desc=True)
                else:
                    query = query.order(order_by)
            
            # Pagination
            if limit:
                query = query.limit(limit)
            if offset:
                query = query.offset(offset)
            
            result = query.execute()
            return result.data or []
        except Exception as e:
            logger.error(f"Erreur lors de la récupération dans {self.table_name}: {e}")
            raise
    
    def update(self, record_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Mettre à jour un enregistrement"""
        try:
            result = self.client.table(self.table_name).update(data).eq("id", record_id).execute()
            if result.data:
                return result.data[0]
            else:
                raise Exception("Aucune donnée retournée après mise à jour")
        except Exception as e:
            logger.error(f"Erreur lors de la mise à jour dans {self.table_name}: {e}")
            raise
    
    def delete(self, record_id: str) -> bool:
        """Supprimer un enregistrement"""
        try:
            result = self.client.table(self.table_name).delete().eq("id", record_id).execute()
            return True
        except Exception as e:
            logger.error(f"Erreur lors de la suppression dans {self.table_name}: {e}")
            raise
    
    def count(self, filters: Optional[Dict[str, Any]] = None) -> int:
        """Compter les enregistrements"""
        try:
            query = self.client.table(self.table_name).select("*", count="exact")
            
            if filters:
                for key, value in filters.items():
                    if isinstance(value, list):
                        query = query.in_(key, value)
                    else:
                        query = query.eq(key, value)
            
            result = query.execute()
            return result.count or 0
        except Exception as e:
            logger.error(f"Erreur lors du comptage dans {self.table_name}: {e}")
            raise

class AgenceService(DatabaseService):
    """Service pour la gestion des agences"""
    
    def __init__(self):
        super().__init__("agences")
    
    def get_by_email(self, email: str) -> Optional[Dict[str, Any]]:
        """Récupérer une agence par email"""
        try:
            result = self.client.table(self.table_name).select("*").eq("email", email).execute()
            return result.data[0] if result.data else None
        except Exception as e:
            logger.error(f"Erreur lors de la récupération par email: {e}")
            raise

class UtilisateurService(DatabaseService):
    """Service pour la gestion des utilisateurs"""
    
    def __init__(self):
        super().__init__("utilisateurs")
    
    def get_by_email(self, email: str) -> Optional[Dict[str, Any]]:
        """Récupérer un utilisateur par email"""
        try:
            result = self.client.table(self.table_name).select("*").eq("email", email).execute()
            return result.data[0] if result.data else None
        except Exception as e:
            logger.error(f"Erreur lors de la récupération par email: {e}")
            raise
    
    def get_by_agence(self, agence_id: str) -> List[Dict[str, Any]]:
        """Récupérer tous les utilisateurs d'une agence"""
        return self.get_all(filters={"agence_id": agence_id})

class BailleurService(DatabaseService):
    """Service pour la gestion des bailleurs"""
    
    def __init__(self):
        super().__init__("bailleurs")
    
    def get_by_agence(self, agence_id: str) -> List[Dict[str, Any]]:
        """Récupérer tous les bailleurs d'une agence"""
        return self.get_all(filters={"agence_id": agence_id})

class BienService(DatabaseService):
    """Service pour la gestion des biens"""
    
    def __init__(self):
        super().__init__("biens")
    
    def get_by_agence(self, agence_id: str) -> List[Dict[str, Any]]:
        """Récupérer tous les biens d'une agence"""
        return self.get_all(filters={"agence_id": agence_id})
    
    def get_by_bailleur(self, bailleur_id: str) -> List[Dict[str, Any]]:
        """Récupérer tous les biens d'un bailleur"""
        return self.get_all(filters={"bailleur_id": bailleur_id})
    
    def search_by_location(self, agence_id: str, ville: str = None, code_postal: str = None) -> List[Dict[str, Any]]:
        """Rechercher des biens par localisation"""
        filters = {"agence_id": agence_id}
        if ville:
            filters["ville"] = ville
        if code_postal:
            filters["code_postal"] = code_postal
        return self.get_all(filters=filters)

class LocataireService(DatabaseService):
    """Service pour la gestion des locataires"""
    
    def __init__(self):
        super().__init__("locataires")
    
    def get_by_agence(self, agence_id: str) -> List[Dict[str, Any]]:
        """Récupérer tous les locataires d'une agence"""
        return self.get_all(filters={"agence_id": agence_id})

class ContratService(DatabaseService):
    """Service pour la gestion des contrats"""
    
    def __init__(self):
        super().__init__("contrats")
    
    def get_by_agence(self, agence_id: str) -> List[Dict[str, Any]]:
        """Récupérer tous les contrats d'une agence"""
        return self.get_all(filters={"agence_id": agence_id})
    
    def get_active_contracts(self, agence_id: str) -> List[Dict[str, Any]]:
        """Récupérer tous les contrats actifs d'une agence"""
        return self.get_all(filters={"agence_id": agence_id, "statut": "actif"})
    
    def get_by_bien(self, bien_id: str) -> List[Dict[str, Any]]:
        """Récupérer tous les contrats d'un bien"""
        return self.get_all(filters={"bien_id": bien_id})

class PaiementService(DatabaseService):
    """Service pour la gestion des paiements"""
    
    def __init__(self):
        super().__init__("paiements")
    
    def get_by_agence(self, agence_id: str) -> List[Dict[str, Any]]:
        """Récupérer tous les paiements d'une agence"""
        return self.get_all(filters={"agence_id": agence_id})
    
    def get_by_contrat(self, contrat_id: str) -> List[Dict[str, Any]]:
        """Récupérer tous les paiements d'un contrat"""
        return self.get_all(filters={"contrat_id": contrat_id})
    
    def get_unpaid(self, agence_id: str) -> List[Dict[str, Any]]:
        """Récupérer tous les paiements impayés d'une agence"""
        return self.get_all(filters={"agence_id": agence_id, "statut": ["en_retard", "impaye"]})

class DepenseService(DatabaseService):
    """Service pour la gestion des dépenses"""
    
    def __init__(self):
        super().__init__("depenses")
    
    def get_by_agence(self, agence_id: str) -> List[Dict[str, Any]]:
        """Récupérer toutes les dépenses d'une agence"""
        return self.get_all(filters={"agence_id": agence_id})
    
    def get_by_bien(self, bien_id: str) -> List[Dict[str, Any]]:
        """Récupérer toutes les dépenses d'un bien"""
        return self.get_all(filters={"bien_id": bien_id})

class ReversementService(DatabaseService):
    """Service pour la gestion des reversements"""
    
    def __init__(self):
        super().__init__("reversements")
    
    def get_by_agence(self, agence_id: str) -> List[Dict[str, Any]]:
        """Récupérer tous les reversements d'une agence"""
        return self.get_all(filters={"agence_id": agence_id})
    
    def get_by_bailleur(self, bailleur_id: str) -> List[Dict[str, Any]]:
        """Récupérer tous les reversements d'un bailleur"""
        return self.get_all(filters={"bailleur_id": bailleur_id})

# Instances des services
agence_service = AgenceService()
utilisateur_service = UtilisateurService()
bailleur_service = BailleurService()
bien_service = BienService()
locataire_service = LocataireService()
contrat_service = ContratService()
paiement_service = PaiementService()
depense_service = DepenseService()
reversement_service = ReversementService()
