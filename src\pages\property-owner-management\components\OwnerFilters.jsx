import React from 'react';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';
import Select from '../../../components/ui/Select';
import Icon from '../../../components/AppIcon';

const OwnerFilters = ({ 
  onFilterChange, 
  onSearchChange, 
  onClearFilters, 
  activeFilters = {}, 
  className = "" 
}) => {
  const handleFilterChange = (filterType) => (value) => {
    onFilterChange?.(filterType, value);
  };

  const handleSearchChange = (e) => {
    onSearchChange?.(e?.target?.value);
  };

  const hasActiveFilters = Object.values(activeFilters)?.some(value => value && value !== 'all');

  const statusOptions = [
    { value: 'all', label: 'Tous les statuts' },
    { value: 'active', label: 'Actif' },
    { value: 'pending', label: 'En attente' },
    { value: 'inactive', label: 'Inactif' }
  ];

  const cityOptions = [
    { value: 'all', label: 'Toutes les villes' },
    { value: 'paris', label: 'Paris' },
    { value: 'lyon', label: 'Lyon' },
    { value: 'marseille', label: 'Marseille' },
    { value: 'toulouse', label: 'Toulouse' },
    { value: 'nice', label: 'Nice' },
    { value: 'nantes', label: 'Nantes' }
  ];

  const paymentPreferenceOptions = [
    { value: 'all', label: 'Tous les modes' },
    { value: 'virement', label: 'Virement' },
    { value: 'cheque', label: 'Chèque' },
    { value: 'especes', label: 'Espèces' }
  ];

  return (
    <div className={`bg-card rounded-lg p-6 border border-border ${className}`}>
      <div className="flex flex-col lg:flex-row lg:items-center gap-4">
        {/* Search Input */}
        <div className="flex-1 min-w-0">
          <div className="relative">
            <Icon 
              name="Search" 
              size={20} 
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" 
            />
            <Input
              type="text"
              placeholder="Rechercher par nom, email, ville, profession..."
              onChange={handleSearchChange}
              className="pl-10 pr-4"
            />
          </div>
        </div>

        {/* Filters */}
        <div className="flex flex-wrap lg:flex-nowrap items-center gap-3">
          <Select
            value={activeFilters?.status || 'all'}
            onValueChange={handleFilterChange('status')}
            options={statusOptions}
            placeholder="Statut"
            className="min-w-[140px]"
          />

          <Select
            value={activeFilters?.city || 'all'}
            onValueChange={handleFilterChange('city')}
            options={cityOptions}
            placeholder="Ville"
            className="min-w-[140px]"
          />

          <Select
            value={activeFilters?.paymentPreference || 'all'}
            onValueChange={handleFilterChange('paymentPreference')}
            options={paymentPreferenceOptions}
            placeholder="Paiement"
            className="min-w-[140px]"
          />

          {/* Advanced Filters */}
          <div className="flex items-center gap-2">
            <Input
              type="number"
              placeholder="Min propriétés"
              value={activeFilters?.minProperties || ''}
              onChange={(e) => handleFilterChange('minProperties')(e?.target?.value)}
              className="w-32"
            />
            <span className="text-muted-foreground">à</span>
            <Input
              type="number"
              placeholder="Max propriétés"
              value={activeFilters?.maxProperties || ''}
              onChange={(e) => handleFilterChange('maxProperties')(e?.target?.value)}
              className="w-32"
            />
          </div>

          {/* Clear Filters */}
          {hasActiveFilters && (
            <Button
              variant="outline"
              onClick={onClearFilters}
              className="whitespace-nowrap"
              iconName="X"
            >
              Effacer
            </Button>
          )}
        </div>
      </div>
      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="mt-4 pt-4 border-t border-border">
          <div className="flex flex-wrap items-center gap-2">
            <span className="text-sm font-medium text-muted-foreground mr-2">
              Filtres actifs:
            </span>
            {Object.entries(activeFilters)?.map(([key, value]) => {
              if (!value || value === 'all') return null;
              
              let displayValue = value;
              if (key === 'status') {
                const option = statusOptions?.find(opt => opt?.value === value);
                displayValue = option?.label || value;
              } else if (key === 'city') {
                const option = cityOptions?.find(opt => opt?.value === value);
                displayValue = option?.label || value;
              } else if (key === 'paymentPreference') {
                const option = paymentPreferenceOptions?.find(opt => opt?.value === value);
                displayValue = option?.label || value;
              }

              return (
                <span
                  key={key}
                  className="inline-flex items-center gap-1 px-2 py-1 bg-primary/10 text-primary rounded-md text-xs font-medium"
                >
                  {displayValue}
                  <button
                    onClick={() => handleFilterChange(key)('all')}
                    className="hover:bg-primary/20 rounded-sm p-0.5"
                  >
                    <Icon name="X" size={10} />
                  </button>
                </span>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

export default OwnerFilters;