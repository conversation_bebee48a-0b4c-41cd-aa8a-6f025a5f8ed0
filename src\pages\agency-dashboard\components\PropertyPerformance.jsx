import React, { useState } from 'react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON><PERSON>, Pie, Cell } from 'recharts';

import Button from '../../../components/ui/Button';

const PropertyPerformance = ({ className = "" }) => {
  const [viewType, setViewType] = useState('occupancy');

  const occupancyData = [
    { district: '1er arr.', occupied: 12, vacant: 2, total: 14, rate: 85.7 },
    { district: '7ème arr.', occupied: 8, vacant: 1, total: 9, rate: 88.9 },
    { district: '16ème arr.', occupied: 6, vacant: 2, total: 8, rate: 75.0 },
    { district: '8ème arr.', occupied: 5, vacant: 0, total: 5, rate: 100.0 },
    { district: 'Neuilly', occupied: 4, vacant: 1, total: 5, rate: 80.0 }
  ];

  const revenueData = [
    { district: '1er arr.', revenue: 28500, avgRent: 2375 },
    { district: '7ème arr.', revenue: 22400, avgRent: 2800 },
    { district: '16ème arr.', revenue: 15600, avgRent: 2600 },
    { district: '8ème arr.', revenue: 16000, avgRent: 3200 },
    { district: 'Neuilly', revenue: 13200, avgRent: 3300 }
  ];

  const propertyTypes = [
    { name: 'Studio', value: 12, color: 'var(--color-primary)' },
    { name: 'T1', value: 8, color: 'var(--color-accent)' },
    { name: 'T2', value: 15, color: 'var(--color-success)' },
    { name: 'T3', value: 6, color: 'var(--color-warning)' },
    { name: 'T4+', value: 3, color: 'var(--color-error)' }
  ];

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    })?.format(value);
  };

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload?.length) {
      return (
        <div className="bg-popover border border-border rounded-lg p-3 elevation-2">
          <p className="font-medium text-popover-foreground mb-2">{label}</p>
          {payload?.map((entry, index) => (
            <div key={index} className="flex items-center space-x-2 text-sm">
              <div 
                className="w-3 h-3 rounded-full" 
                style={{ backgroundColor: entry?.color }}
              />
              <span className="text-muted-foreground">{entry?.name}:</span>
              <span className="font-data font-medium text-popover-foreground">
                {entry?.name?.includes('Taux') ? `${entry?.value}%` : 
                 entry?.name?.includes('€') || entry?.name?.includes('Revenus') ? formatCurrency(entry?.value) :
                 entry?.value}
              </span>
            </div>
          ))}
        </div>
      );
    }
    return null;
  };

  const PieTooltip = ({ active, payload }) => {
    if (active && payload && payload?.length) {
      const data = payload?.[0];
      return (
        <div className="bg-popover border border-border rounded-lg p-3 elevation-2">
          <p className="font-medium text-popover-foreground">{data?.name}</p>
          <p className="text-sm text-muted-foreground">
            {data?.value} propriétés ({((data?.value / propertyTypes?.reduce((sum, item) => sum + item?.value, 0)) * 100)?.toFixed(1)}%)
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <div className={`bg-card border border-border rounded-lg ${className}`}>
      <div className="p-6 border-b border-border">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-foreground">Performance des propriétés</h3>
            <p className="text-sm text-muted-foreground">Analyse par arrondissement</p>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant={viewType === 'occupancy' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewType('occupancy')}
            >
              Occupation
            </Button>
            <Button
              variant={viewType === 'revenue' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewType('revenue')}
            >
              Revenus
            </Button>
            <Button
              variant={viewType === 'types' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewType('types')}
            >
              Types
            </Button>
          </div>
        </div>
      </div>
      <div className="p-6">
        {viewType === 'occupancy' && (
          <div className="h-80 w-full">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={occupancyData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="var(--color-border)" />
                <XAxis 
                  dataKey="district" 
                  stroke="var(--color-muted-foreground)"
                  fontSize={12}
                />
                <YAxis 
                  stroke="var(--color-muted-foreground)"
                  fontSize={12}
                />
                <Tooltip content={<CustomTooltip />} />
                <Bar 
                  dataKey="occupied" 
                  fill="var(--color-success)" 
                  name="Occupées"
                  radius={[4, 4, 0, 0]}
                />
                <Bar 
                  dataKey="vacant" 
                  fill="var(--color-error)" 
                  name="Vacantes"
                  radius={[4, 4, 0, 0]}
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
        )}

        {viewType === 'revenue' && (
          <div className="h-80 w-full">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={revenueData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="var(--color-border)" />
                <XAxis 
                  dataKey="district" 
                  stroke="var(--color-muted-foreground)"
                  fontSize={12}
                />
                <YAxis 
                  stroke="var(--color-muted-foreground)"
                  fontSize={12}
                  tickFormatter={formatCurrency}
                />
                <Tooltip content={<CustomTooltip />} />
                <Bar 
                  dataKey="revenue" 
                  fill="var(--color-primary)" 
                  name="Revenus mensuels"
                  radius={[4, 4, 0, 0]}
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
        )}

        {viewType === 'types' && (
          <div className="flex items-center justify-center">
            <div className="h-80 w-80">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={propertyTypes}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={120}
                    paddingAngle={5}
                    dataKey="value"
                  >
                    {propertyTypes?.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry?.color} />
                    ))}
                  </Pie>
                  <Tooltip content={<PieTooltip />} />
                </PieChart>
              </ResponsiveContainer>
            </div>
            <div className="ml-8 space-y-3">
              {propertyTypes?.map((type, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <div 
                    className="w-4 h-4 rounded-full" 
                    style={{ backgroundColor: type?.color }}
                  />
                  <span className="text-sm text-foreground font-medium">{type?.name}</span>
                  <span className="text-sm text-muted-foreground">({type?.value})</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Summary Stats */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="text-center p-3 bg-muted rounded-lg">
            <p className="text-xs text-muted-foreground mb-1">Taux d'occupation</p>
            <p className="text-lg font-bold text-success">87,8%</p>
          </div>
          <div className="text-center p-3 bg-muted rounded-lg">
            <p className="text-xs text-muted-foreground mb-1">Loyer moyen</p>
            <p className="text-lg font-bold text-primary font-data">2 654 €</p>
          </div>
          <div className="text-center p-3 bg-muted rounded-lg">
            <p className="text-xs text-muted-foreground mb-1">Revenus totaux</p>
            <p className="text-lg font-bold text-accent font-data">95 700 €</p>
          </div>
          <div className="text-center p-3 bg-muted rounded-lg">
            <p className="text-xs text-muted-foreground mb-1">Propriétés</p>
            <p className="text-lg font-bold text-foreground">44</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PropertyPerformance;