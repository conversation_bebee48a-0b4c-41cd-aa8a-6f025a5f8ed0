import React from "react";
import { BrowserRouter, Routes as RouterRoutes, Route } from "react-router-dom";
import ScrollToTop from "components/ScrollToTop";
import ErrorBoundary from "components/ErrorBoundary";
import NotFound from "pages/NotFound";
import FinancialDashboardReporting from './pages/financial-dashboard-reporting';
import AgencyDashboard from './pages/agency-dashboard';
import PropertyPortfolioManagement from './pages/property-portfolio-management';
import PropertyOwnerManagement from './pages/property-owner-management';
import ContractManagementSystem from './pages/contract-management-system';
import MaintenanceRequestTracking from './pages/maintenance-request-tracking';
import TenantManagementHub from './pages/tenant-management-hub';
import AgencyLogin from './pages/agency-login';
import PropertyInspectionConditionReports from './pages/property-inspection-condition-reports';
import TestModern from './pages/test-modern';

const Routes = () => {
  return (
    <BrowserRouter>
      <ErrorBoundary>
      <ScrollToTop />
      <RouterRoutes>
        {/* Define your route here */}
        <Route path="/" element={<AgencyDashboard />} />
        <Route path="/financial-dashboard-reporting" element={<FinancialDashboardReporting />} />
        <Route path="/agency-dashboard" element={<AgencyDashboard />} />
        <Route path="/property-portfolio-management" element={<PropertyPortfolioManagement />} />
        <Route path="/property-owner-management" element={<PropertyOwnerManagement />} />
        <Route path="/contract-management-system" element={<ContractManagementSystem />} />
        <Route path="/property-inspection-condition-reports" element={<PropertyInspectionConditionReports />} />
        <Route path="/maintenance-request-tracking" element={<MaintenanceRequestTracking />} />
        <Route path="/tenant-management-hub" element={<TenantManagementHub />} />
        <Route path="/agency-login" element={<AgencyLogin />} />
        <Route path="*" element={<NotFound />} />
      </RouterRoutes>
      </ErrorBoundary>
    </BrowserRouter>
  );
};

export default Routes;