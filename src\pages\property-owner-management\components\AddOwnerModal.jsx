import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';
import Select from '../../../components/ui/Select';
import { Checkbox } from '../../../components/ui/Checkbox';
import Icon from '../../../components/AppIcon';

const AddOwnerModal = ({ isOpen, onClose, onSubmit, properties = [] }) => {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    dateOfBirth: '',
    nationality: 'french',
    address: '',
    city: '',
    postalCode: '',
    profession: '',
    iban: '',
    managementRate: 8.0,
    paymentPreference: 'virement',
    taxOptimization: false
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const nationalityOptions = [
    { value: 'french', label: 'Française' },
    { value: 'european', label: 'Européenne' },
    { value: 'other', label: 'Autre' }
  ];

  const paymentPreferenceOptions = [
    { value: 'virement', label: 'Virement bancaire' },
    { value: 'cheque', label: 'Chèque' },
    { value: 'especes', label: 'Espèces' }
  ];

  const handleInputChange = (field) => (e) => {
    const value = e?.target?.type === 'checkbox' ? e?.target?.checked : e?.target?.value;
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors?.[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleSelectChange = (field) => (value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors?.[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData?.firstName?.trim()) newErrors.firstName = 'Prénom requis';
    if (!formData?.lastName?.trim()) newErrors.lastName = 'Nom requis';
    if (!formData?.email?.trim()) {
      newErrors.email = 'Email requis';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/?.test(formData?.email)) {
      newErrors.email = 'Format email invalide';
    }
    if (!formData?.phone?.trim()) newErrors.phone = 'Téléphone requis';
    if (!formData?.address?.trim()) newErrors.address = 'Adresse requise';
    if (!formData?.city?.trim()) newErrors.city = 'Ville requise';
    if (!formData?.postalCode?.trim()) newErrors.postalCode = 'Code postal requis';
    if (!formData?.profession?.trim()) newErrors.profession = 'Profession requise';
    if (!formData?.iban?.trim()) newErrors.iban = 'IBAN requis';

    setErrors(newErrors);
    return Object.keys(newErrors)?.length === 0;
  };

  const handleSubmit = async (e) => {
    e?.preventDefault();
    
    if (!validateForm()) return;
    
    setIsSubmitting(true);
    
    try {
      await onSubmit?.(formData);
      handleClose();
    } catch (error) {
      console.error('Error adding owner:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setFormData({
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      dateOfBirth: '',
      nationality: 'french',
      address: '',
      city: '',
      postalCode: '',
      profession: '',
      iban: '',
      managementRate: 8.0,
      paymentPreference: 'virement',
      taxOptimization: false
    });
    setErrors({});
    onClose?.();
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black/50"
            onClick={handleClose}
          />
          
          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            className="relative bg-card rounded-lg shadow-xl border border-border w-full max-w-2xl max-h-[90vh] overflow-hidden"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-border">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                  <Icon name="UserPlus" size={18} className="text-primary" />
                </div>
                <div>
                  <h2 className="text-lg font-semibold text-foreground">
                    Nouveau Bailleur
                  </h2>
                  <p className="text-sm text-muted-foreground">
                    Ajoutez un nouveau propriétaire bailleur
                  </p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                iconName="X"
                onClick={handleClose}
              />
            </div>

            {/* Content */}
            <div className="p-6 overflow-y-auto max-h-[70vh]">
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Personal Info */}
                <div className="space-y-4">
                  <h3 className="text-base font-medium text-foreground border-b border-border pb-2">
                    Informations Personnelles
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Input
                        label="Prénom *"
                        value={formData?.firstName}
                        onChange={handleInputChange('firstName')}
                        error={errors?.firstName}
                        placeholder="Jean-Pierre"
                      />
                    </div>
                    <div>
                      <Input
                        label="Nom *"
                        value={formData?.lastName}
                        onChange={handleInputChange('lastName')}
                        error={errors?.lastName}
                        placeholder="Durand"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Input
                        label="Email *"
                        type="email"
                        value={formData?.email}
                        onChange={handleInputChange('email')}
                        error={errors?.email}
                        placeholder="<EMAIL>"
                      />
                    </div>
                    <div>
                      <Input
                        label="Téléphone *"
                        value={formData?.phone}
                        onChange={handleInputChange('phone')}
                        error={errors?.phone}
                        placeholder="+33 6 12 34 56 78"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Input
                        label="Date de naissance"
                        type="date"
                        value={formData?.dateOfBirth}
                        onChange={handleInputChange('dateOfBirth')}
                      />
                    </div>
                    <div>
                      <Select
                        label="Nationalité"
                        value={formData?.nationality}
                        onValueChange={handleSelectChange('nationality')}
                        options={nationalityOptions}
                      />
                    </div>
                  </div>

                  <div>
                    <Input
                      label="Profession *"
                      value={formData?.profession}
                      onChange={handleInputChange('profession')}
                      error={errors?.profession}
                      placeholder="Entrepreneur, Médecin, etc."
                    />
                  </div>
                </div>

                {/* Address Info */}
                <div className="space-y-4">
                  <h3 className="text-base font-medium text-foreground border-b border-border pb-2">
                    Adresse
                  </h3>
                  
                  <div>
                    <Input
                      label="Adresse *"
                      value={formData?.address}
                      onChange={handleInputChange('address')}
                      error={errors?.address}
                      placeholder="15 Avenue Montaigne"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Input
                        label="Ville *"
                        value={formData?.city}
                        onChange={handleInputChange('city')}
                        error={errors?.city}
                        placeholder="Paris"
                      />
                    </div>
                    <div>
                      <Input
                        label="Code postal *"
                        value={formData?.postalCode}
                        onChange={handleInputChange('postalCode')}
                        error={errors?.postalCode}
                        placeholder="75008"
                      />
                    </div>
                  </div>
                </div>

                {/* Financial Info */}
                <div className="space-y-4">
                  <h3 className="text-base font-medium text-foreground border-b border-border pb-2">
                    Informations Financières
                  </h3>
                  
                  <div>
                    <Input
                      label="IBAN *"
                      value={formData?.iban}
                      onChange={handleInputChange('iban')}
                      error={errors?.iban}
                      placeholder="FR76 3000 6000 0112 3456 7890 189"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Input
                        label="Taux de gérance (%)"
                        type="number"
                        step="0.1"
                        min="0"
                        max="100"
                        value={formData?.managementRate}
                        onChange={handleInputChange('managementRate')}
                        placeholder="8.0"
                      />
                    </div>
                    <div>
                      <Select
                        label="Mode de paiement"
                        value={formData?.paymentPreference}
                        onValueChange={handleSelectChange('paymentPreference')}
                        options={paymentPreferenceOptions}
                      />
                    </div>
                  </div>

                  <div>
                    <Checkbox
                      checked={formData?.taxOptimization}
                      onChange={handleInputChange('taxOptimization')}
                      label="Optimisation fiscale souhaitée"
                    />
                  </div>
                </div>
              </form>
            </div>

            {/* Footer */}
            <div className="flex items-center justify-end gap-3 p-6 border-t border-border bg-muted/20">
              <Button
                variant="outline"
                onClick={handleClose}
                disabled={isSubmitting}
              >
                Annuler
              </Button>
              <Button
                onClick={handleSubmit}
                loading={isSubmitting}
                iconName="Plus"
              >
                Ajouter Bailleur
              </Button>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};

export default AddOwnerModal;