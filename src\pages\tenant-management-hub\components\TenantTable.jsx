import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';


const TenantTable = ({ 
  tenants = [],
  onTenantSelect = () => {},
  onBulkAction = () => {},
  selectedTenants = [],
  onSelectionChange = () => {},
  className = ""
}) => {
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });

  const getLeaseStatusBadge = (status) => {
    const statusConfig = {
      active: { color: 'bg-success/10 text-success', label: 'Actif' },
      expired: { color: 'bg-error/10 text-error', label: 'Expiré' },
      expiring_soon: { color: 'bg-warning/10 text-warning', label: 'Expire bientôt' },
      terminated: { color: 'bg-muted text-muted-foreground', label: 'Résilié' },
      pending: { color: 'bg-accent/10 text-accent', label: 'En attente' }
    };

    const config = statusConfig?.[status] || statusConfig?.pending;
    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${config?.color}`}>
        {config?.label}
      </span>
    );
  };

  const getPaymentStatusIcon = (status) => {
    const statusConfig = {
      current: { icon: 'CheckCircle', color: 'text-success' },
      late: { icon: 'AlertCircle', color: 'text-error' },
      partial: { icon: 'Clock', color: 'text-warning' },
      pending: { icon: 'Clock', color: 'text-accent' }
    };

    const config = statusConfig?.[status] || statusConfig?.pending;
    return <Icon name={config?.icon} size={16} className={config?.color} />;
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    })?.format(amount);
  };

  const formatDate = (date) => {
    return new Date(date)?.toLocaleDateString('fr-FR');
  };

  const handleSort = (key) => {
    let direction = 'asc';
    if (sortConfig?.key === key && sortConfig?.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  const handleSelectAll = (checked) => {
    if (checked) {
      onSelectionChange(tenants?.map(tenant => tenant?.id));
    } else {
      onSelectionChange([]);
    }
  };

  const handleSelectTenant = (tenantId, checked) => {
    if (checked) {
      onSelectionChange([...selectedTenants, tenantId]);
    } else {
      onSelectionChange(selectedTenants?.filter(id => id !== tenantId));
    }
  };

  const isAllSelected = tenants?.length > 0 && selectedTenants?.length === tenants?.length;
  const isPartiallySelected = selectedTenants?.length > 0 && selectedTenants?.length < tenants?.length;

  const getSortIcon = (key) => {
    if (sortConfig?.key !== key) return 'ArrowUpDown';
    return sortConfig?.direction === 'asc' ? 'ArrowUp' : 'ArrowDown';
  };

  return (
    <div className={`bg-card border border-border rounded-lg overflow-hidden ${className}`}>
      {/* Table Header with Bulk Actions */}
      {selectedTenants?.length > 0 && (
        <div className="bg-primary/5 border-b border-border p-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-primary">
              {selectedTenants?.length} locataire(s) sélectionné(s)
            </span>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onBulkAction('message')}
                iconName="Mail"
                iconPosition="left"
              >
                Envoyer message
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onBulkAction('export')}
                iconName="Download"
                iconPosition="left"
              >
                Exporter
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onSelectionChange([])}
                iconName="X"
              >
                Annuler
              </Button>
            </div>
          </div>
        </div>
      )}
      {/* Desktop Table */}
      <div className="hidden lg:block overflow-x-auto">
        <table className="w-full">
          <thead className="bg-muted/50 border-b border-border">
            <tr>
              <th className="w-12 p-4">
                <input
                  type="checkbox"
                  checked={isAllSelected}
                  ref={input => {
                    if (input) input.indeterminate = isPartiallySelected;
                  }}
                  onChange={(e) => handleSelectAll(e?.target?.checked)}
                  className="rounded border-border"
                />
              </th>
              <th className="text-left p-4 font-medium text-foreground">
                <button
                  onClick={() => handleSort('name')}
                  className="flex items-center gap-2 hover:text-primary transition-smooth"
                >
                  Locataire
                  <Icon name={getSortIcon('name')} size={14} />
                </button>
              </th>
              <th className="text-left p-4 font-medium text-foreground">
                <button
                  onClick={() => handleSort('property')}
                  className="flex items-center gap-2 hover:text-primary transition-smooth"
                >
                  Propriété
                  <Icon name={getSortIcon('property')} size={14} />
                </button>
              </th>
              <th className="text-left p-4 font-medium text-foreground">Statut du bail</th>
              <th className="text-left p-4 font-medium text-foreground">
                <button
                  onClick={() => handleSort('rent')}
                  className="flex items-center gap-2 hover:text-primary transition-smooth"
                >
                  Loyer
                  <Icon name={getSortIcon('rent')} size={14} />
                </button>
              </th>
              <th className="text-left p-4 font-medium text-foreground">Paiement</th>
              <th className="text-left p-4 font-medium text-foreground">Contact</th>
              <th className="text-right p-4 font-medium text-foreground">Actions</th>
            </tr>
          </thead>
          <tbody>
            {tenants?.map((tenant) => (
              <tr
                key={tenant?.id}
                className="border-b border-border hover:bg-muted/30 transition-smooth"
              >
                <td className="p-4">
                  <input
                    type="checkbox"
                    checked={selectedTenants?.includes(tenant?.id)}
                    onChange={(e) => handleSelectTenant(tenant?.id, e?.target?.checked)}
                    className="rounded border-border"
                  />
                </td>
                <td className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-secondary rounded-full flex items-center justify-center">
                      <span className="text-sm font-medium text-secondary-foreground">
                        {tenant?.firstName?.[0]}{tenant?.lastName?.[0]}
                      </span>
                    </div>
                    <div>
                      <p className="font-medium text-foreground">
                        {tenant?.firstName} {tenant?.lastName}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        ID: {tenant?.id}
                      </p>
                    </div>
                  </div>
                </td>
                <td className="p-4">
                  <div>
                    <p className="font-medium text-foreground">{tenant?.property?.address}</p>
                    <p className="text-sm text-muted-foreground">
                      {tenant?.property?.city} • {tenant?.property?.type}
                    </p>
                  </div>
                </td>
                <td className="p-4">
                  {getLeaseStatusBadge(tenant?.leaseStatus)}
                  {tenant?.leaseExpiryDate && (
                    <p className="text-xs text-muted-foreground mt-1">
                      Expire: {formatDate(tenant?.leaseExpiryDate)}
                    </p>
                  )}
                </td>
                <td className="p-4">
                  <p className="font-data font-medium text-foreground">
                    {formatCurrency(tenant?.monthlyRent)}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Dépôt: {formatCurrency(tenant?.securityDeposit)}
                  </p>
                </td>
                <td className="p-4">
                  <div className="flex items-center gap-2">
                    {getPaymentStatusIcon(tenant?.paymentStatus)}
                    <span className="text-sm text-muted-foreground">
                      {tenant?.paymentStatus === 'current' ? 'À jour' :
                       tenant?.paymentStatus === 'late' ? 'En retard' :
                       tenant?.paymentStatus === 'partial' ? 'Partiel' : 'En attente'}
                    </span>
                  </div>
                </td>
                <td className="p-4">
                  <div>
                    <p className="text-sm text-foreground">{tenant?.email}</p>
                    <p className="text-sm text-muted-foreground">{tenant?.phone}</p>
                  </div>
                </td>
                <td className="p-4">
                  <div className="flex items-center justify-end gap-1">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => onTenantSelect(tenant?.id, 'view')}
                      title="Voir le profil"
                    >
                      <Icon name="Eye" size={16} />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => onTenantSelect(tenant?.id, 'edit')}
                      title="Modifier"
                    >
                      <Icon name="Edit" size={16} />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => onTenantSelect(tenant?.id, 'message')}
                      title="Envoyer message"
                    >
                      <Icon name="Mail" size={16} />
                    </Button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      {/* Mobile Cards */}
      <div className="lg:hidden">
        {tenants?.map((tenant) => (
          <div
            key={tenant?.id}
            className="p-4 border-b border-border last:border-b-0"
          >
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center gap-3">
                <input
                  type="checkbox"
                  checked={selectedTenants?.includes(tenant?.id)}
                  onChange={(e) => handleSelectTenant(tenant?.id, e?.target?.checked)}
                  className="rounded border-border"
                />
                <div className="w-12 h-12 bg-secondary rounded-full flex items-center justify-center">
                  <span className="text-sm font-medium text-secondary-foreground">
                    {tenant?.firstName?.[0]}{tenant?.lastName?.[0]}
                  </span>
                </div>
                <div>
                  <p className="font-medium text-foreground">
                    {tenant?.firstName} {tenant?.lastName}
                  </p>
                  <p className="text-sm text-muted-foreground">ID: {tenant?.id}</p>
                </div>
              </div>
              {getLeaseStatusBadge(tenant?.leaseStatus)}
            </div>

            <div className="space-y-2 mb-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Propriété:</span>
                <span className="text-sm font-medium text-foreground">
                  {tenant?.property?.address}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Loyer:</span>
                <span className="text-sm font-data font-medium text-foreground">
                  {formatCurrency(tenant?.monthlyRent)}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Paiement:</span>
                <div className="flex items-center gap-1">
                  {getPaymentStatusIcon(tenant?.paymentStatus)}
                  <span className="text-sm text-muted-foreground">
                    {tenant?.paymentStatus === 'current' ? 'À jour' :
                     tenant?.paymentStatus === 'late' ? 'En retard' :
                     tenant?.paymentStatus === 'partial' ? 'Partiel' : 'En attente'}
                  </span>
                </div>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="text-sm text-muted-foreground">
                <p>{tenant?.email}</p>
                <p>{tenant?.phone}</p>
              </div>
              <div className="flex items-center gap-1">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => onTenantSelect(tenant?.id, 'view')}
                >
                  <Icon name="Eye" size={16} />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => onTenantSelect(tenant?.id, 'edit')}
                >
                  <Icon name="Edit" size={16} />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => onTenantSelect(tenant?.id, 'message')}
                >
                  <Icon name="Mail" size={16} />
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>
      {/* Empty State */}
      {tenants?.length === 0 && (
        <div className="p-12 text-center">
          <Icon name="Users" size={48} className="text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium text-foreground mb-2">
            Aucun locataire trouvé
          </h3>
          <p className="text-muted-foreground mb-4">
            Commencez par ajouter votre premier locataire au système.
          </p>
          <Button
            variant="default"
            onClick={() => onTenantSelect(null, 'add')}
            iconName="Plus"
            iconPosition="left"
          >
            Ajouter un locataire
          </Button>
        </div>
      )}
    </div>
  );
};

export default TenantTable;