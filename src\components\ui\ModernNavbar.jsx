import React, { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import Icon from '../AppIcon';
import Button from './Button';

const ModernNavbar = ({ className = "" }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const navigationItems = [
    {
      label: 'Dashboard',
      path: '/test-modern',
      icon: 'LayoutDashboard',
    },
    {
      label: 'Properties',
      path: '/test-modern/properties',
      icon: 'Building2',
    },
    {
      label: 'Clients',
      path: '/test-modern/clients',
      icon: 'Users',
    },
  ];

  const handleNavigation = (path) => {
    navigate(path);
    setIsMobileMenuOpen(false);
  };

  const isActive = (path) => {
    return location?.pathname === path;
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <>
      {/* Desktop Navigation */}
      <nav className={`fixed top-0 left-0 right-0 z-40 bg-card border-b border-border ${className}`}>
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            {/* Logo */}
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-primary rounded-md flex items-center justify-center">
                <Icon name="Building" size={18} className="text-primary-foreground" />
              </div>
              <span className="text-lg font-semibold">ModernFlow</span>
            </div>
            
            {/* Desktop Menu */}
            <div className="hidden md:flex items-center space-x-1">
              {navigationItems?.map((item) => (
                <Button
                  key={item?.path}
                  variant={isActive(item?.path) ? "default" : "ghost"}
                  onClick={() => handleNavigation(item?.path)}
                  className="flex items-center space-x-2"
                >
                  <Icon name={item?.icon} size={18} />
                  <span>{item?.label}</span>
                </Button>
              ))}
            </div>
            
            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              onClick={toggleMobileMenu}
              className="md:hidden"
            >
              <Icon name={isMobileMenuOpen ? "X" : "Menu"} size={20} />
            </Button>
          </div>
        </div>
        
        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden border-t border-border bg-card">
            <div className="p-4 space-y-2">
              {navigationItems?.map((item) => (
                <Button
                  key={item?.path}
                  variant={isActive(item?.path) ? "default" : "ghost"}
                  onClick={() => handleNavigation(item?.path)}
                  className="flex items-center space-x-2 w-full justify-start"
                >
                  <Icon name={item?.icon} size={18} />
                  <span>{item?.label}</span>
                </Button>
              ))}
            </div>
          </div>
        )}
      </nav>
      
      {/* Content Spacer */}
      <div className="h-16" />
    </>
  );
};

export default ModernNavbar;