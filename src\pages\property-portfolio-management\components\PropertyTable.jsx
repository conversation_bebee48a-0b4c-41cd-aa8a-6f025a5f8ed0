import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import { Checkbox } from '../../../components/ui/Checkbox';
import PropertyStatusBadge from './PropertyStatusBadge';
import PropertyActions from './PropertyActions';

const PropertyTable = ({ 
  properties = [], 
  selectedProperties = [], 
  onSelectionChange = () => {},
  onSort = () => {},
  sortConfig = {},
  onPropertyAction = () => {},
  className = "" 
}) => {
  const [hoveredRow, setHoveredRow] = useState(null);

  const handleSelectAll = (checked) => {
    if (checked) {
      onSelectionChange(properties?.map(p => p?.id));
    } else {
      onSelectionChange([]);
    }
  };

  const handleSelectProperty = (propertyId, checked) => {
    if (checked) {
      onSelectionChange([...selectedProperties, propertyId]);
    } else {
      onSelectionChange(selectedProperties?.filter(id => id !== propertyId));
    }
  };

  const getSortIcon = (column) => {
    if (sortConfig?.key !== column) return 'ArrowUpDown';
    return sortConfig?.direction === 'asc' ? 'ArrowUp' : 'ArrowDown';
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    })?.format(amount);
  };

  const formatDate = (date) => {
    return new Intl.DateTimeFormat('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    })?.format(new Date(date));
  };

  const getPropertyTypeLabel = (type) => {
    const types = {
      apartment: 'Appartement',
      house: 'Maison',
      studio: 'Studio',
      duplex: 'Duplex',
      loft: 'Loft',
      commercial: 'Commercial'
    };
    return types?.[type] || type;
  };

  const isAllSelected = properties?.length > 0 && selectedProperties?.length === properties?.length;
  const isIndeterminate = selectedProperties?.length > 0 && selectedProperties?.length < properties?.length;

  return (
    <div className={`bg-card border border-border rounded-lg overflow-hidden ${className}`}>
      {/* Table Header */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-muted/50 border-b border-border">
            <tr>
              <th className="w-12 p-4">
                <Checkbox
                  checked={isAllSelected}
                  indeterminate={isIndeterminate}
                  onChange={(e) => handleSelectAll(e?.target?.checked)}
                />
              </th>
              
              <th className="text-left p-4 font-medium text-foreground">
                <button
                  onClick={() => onSort('address')}
                  className="flex items-center space-x-2 hover:text-primary transition-smooth"
                >
                  <span>Adresse</span>
                  <Icon name={getSortIcon('address')} size={14} />
                </button>
              </th>
              
              <th className="text-left p-4 font-medium text-foreground">
                <button
                  onClick={() => onSort('type')}
                  className="flex items-center space-x-2 hover:text-primary transition-smooth"
                >
                  <span>Type</span>
                  <Icon name={getSortIcon('type')} size={14} />
                </button>
              </th>
              
              <th className="text-left p-4 font-medium text-foreground">
                <button
                  onClick={() => onSort('status')}
                  className="flex items-center space-x-2 hover:text-primary transition-smooth"
                >
                  <span>Statut</span>
                  <Icon name={getSortIcon('status')} size={14} />
                </button>
              </th>
              
              <th className="text-left p-4 font-medium text-foreground">
                Locataire actuel
              </th>
              
              <th className="text-left p-4 font-medium text-foreground">
                <button
                  onClick={() => onSort('rent')}
                  className="flex items-center space-x-2 hover:text-primary transition-smooth"
                >
                  <span>Loyer mensuel</span>
                  <Icon name={getSortIcon('rent')} size={14} />
                </button>
              </th>
              
              <th className="text-left p-4 font-medium text-foreground">
                <button
                  onClick={() => onSort('lastInspection')}
                  className="flex items-center space-x-2 hover:text-primary transition-smooth"
                >
                  <span>Dernière inspection</span>
                  <Icon name={getSortIcon('lastInspection')} size={14} />
                </button>
              </th>
              
              <th className="w-24 p-4 font-medium text-foreground">
                Actions
              </th>
            </tr>
          </thead>
          
          <tbody>
            {properties?.map((property) => (
              <tr
                key={property?.id}
                className={`border-b border-border hover:bg-muted/30 transition-smooth ${
                  selectedProperties?.includes(property?.id) ? 'bg-primary/5' : ''
                }`}
                onMouseEnter={() => setHoveredRow(property?.id)}
                onMouseLeave={() => setHoveredRow(null)}
              >
                <td className="p-4">
                  <Checkbox
                    checked={selectedProperties?.includes(property?.id)}
                    onChange={(e) => handleSelectProperty(property?.id, e?.target?.checked)}
                  />
                </td>
                
                <td className="p-4">
                  <div className="flex items-start space-x-3">
                    <div className="w-12 h-12 bg-muted rounded-lg flex items-center justify-center">
                      <Icon name="Building2" size={20} className="text-muted-foreground" />
                    </div>
                    <div>
                      <p className="font-medium text-foreground">{property?.address}</p>
                      <p className="text-sm text-muted-foreground">{property?.city}, {property?.postalCode}</p>
                      <p className="text-xs text-muted-foreground">{property?.rooms} pièces • {property?.surface}m²</p>
                    </div>
                  </div>
                </td>
                
                <td className="p-4">
                  <span className="text-sm text-foreground">
                    {getPropertyTypeLabel(property?.type)}
                  </span>
                </td>
                
                <td className="p-4">
                  <PropertyStatusBadge status={property?.status} />
                </td>
                
                <td className="p-4">
                  {property?.currentTenant ? (
                    <div>
                      <p className="text-sm font-medium text-foreground">{property?.currentTenant?.name}</p>
                      <p className="text-xs text-muted-foreground">
                        Bail jusqu'au {formatDate(property?.currentTenant?.leaseEnd)}
                      </p>
                    </div>
                  ) : (
                    <span className="text-sm text-muted-foreground">Aucun locataire</span>
                  )}
                </td>
                
                <td className="p-4">
                  <div>
                    <p className="font-data font-medium text-foreground">
                      {formatCurrency(property?.monthlyRent)}
                    </p>
                    {property?.charges && (
                      <p className="text-xs text-muted-foreground">
                        + {formatCurrency(property?.charges)} charges
                      </p>
                    )}
                  </div>
                </td>
                
                <td className="p-4">
                  {property?.lastInspection ? (
                    <div>
                      <p className="text-sm text-foreground">{formatDate(property?.lastInspection?.date)}</p>
                      <p className="text-xs text-muted-foreground">{property?.lastInspection?.type}</p>
                    </div>
                  ) : (
                    <span className="text-sm text-muted-foreground">Aucune inspection</span>
                  )}
                </td>
                
                <td className="p-4">
                  <PropertyActions
                    property={property}
                    onAction={onPropertyAction}
                    isVisible={hoveredRow === property?.id}
                  />
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      {/* Empty State */}
      {properties?.length === 0 && (
        <div className="p-12 text-center">
          <Icon name="Building2" size={48} className="text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium text-foreground mb-2">Aucune propriété trouvée</h3>
          <p className="text-muted-foreground mb-6">
            Aucune propriété ne correspond aux critères de recherche actuels.
          </p>
          <Button variant="outline">
            <Icon name="Plus" size={16} className="mr-2" />
            Ajouter une propriété
          </Button>
        </div>
      )}
    </div>
  );
};

export default PropertyTable;