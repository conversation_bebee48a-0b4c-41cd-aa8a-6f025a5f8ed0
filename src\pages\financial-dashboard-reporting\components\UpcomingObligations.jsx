import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const UpcomingObligations = ({ obligations, title = "Obligations à venir" }) => {
  const [filter, setFilter] = useState('all');

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    })?.format(amount);
  };

  const formatDate = (date) => {
    return new Intl.DateTimeFormat('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    })?.format(new Date(date));
  };

  const getDaysUntilDue = (dueDate) => {
    const today = new Date();
    const due = new Date(dueDate);
    const diffTime = due - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const getUrgencyColor = (daysUntil) => {
    if (daysUntil < 0) return 'text-error bg-error/10 border-error/20';
    if (daysUntil <= 3) return 'text-warning bg-warning/10 border-warning/20';
    if (daysUntil <= 7) return 'text-accent bg-accent/10 border-accent/20';
    return 'text-muted-foreground bg-muted border-border';
  };

  const getObligationIcon = (type) => {
    switch (type) {
      case 'rent_due': return 'Home';
      case 'tax_payment': return 'Receipt';
      case 'insurance': return 'Shield';
      case 'maintenance': return 'Wrench';
      case 'lease_renewal': return 'FileText';
      case 'inspection': return 'Eye';
      default: return 'Calendar';
    }
  };

  const getObligationLabel = (type) => {
    switch (type) {
      case 'rent_due': return 'Loyer attendu';
      case 'tax_payment': return 'Paiement taxe';
      case 'insurance': return 'Assurance';
      case 'maintenance': return 'Maintenance';
      case 'lease_renewal': return 'Renouvellement bail';
      case 'inspection': return 'Inspection';
      default: return 'Obligation';
    }
  };

  const getUrgencyLabel = (daysUntil) => {
    if (daysUntil < 0) return `En retard de ${Math.abs(daysUntil)} jour${Math.abs(daysUntil) > 1 ? 's' : ''}`;
    if (daysUntil === 0) return 'Aujourd\'hui';
    if (daysUntil === 1) return 'Demain';
    return `Dans ${daysUntil} jours`;
  };

  const filterOptions = [
    { value: 'all', label: 'Toutes' },
    { value: 'overdue', label: 'En retard' },
    { value: 'urgent', label: 'Urgent (≤3j)' },
    { value: 'upcoming', label: 'À venir' }
  ];

  const filteredObligations = obligations?.filter(obligation => {
    const daysUntil = getDaysUntilDue(obligation?.dueDate);
    
    if (filter === 'all') return true;
    if (filter === 'overdue') return daysUntil < 0;
    if (filter === 'urgent') return daysUntil >= 0 && daysUntil <= 3;
    if (filter === 'upcoming') return daysUntil > 3;
    return true;
  });

  const sortedObligations = [...filteredObligations]?.sort((a, b) => {
    return new Date(a.dueDate) - new Date(b.dueDate);
  });

  return (
    <div className="bg-card border border-border rounded-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-foreground">{title}</h3>
        <div className="flex bg-muted rounded-lg p-1">
          {filterOptions?.map((option) => (
            <button
              key={option?.value}
              onClick={() => setFilter(option?.value)}
              className={`px-3 py-1 text-xs font-medium rounded transition-smooth ${
                filter === option?.value
                  ? 'bg-background text-foreground shadow-sm'
                  : 'text-muted-foreground hover:text-foreground'
              }`}
            >
              {option?.label}
            </button>
          ))}
        </div>
      </div>
      <div className="space-y-3 max-h-96 overflow-y-auto">
        {sortedObligations?.length === 0 ? (
          <div className="text-center py-8">
            <Icon name="Calendar" size={32} className="text-muted-foreground mx-auto mb-2" />
            <p className="text-muted-foreground">Aucune obligation trouvée</p>
          </div>
        ) : (
          sortedObligations?.map((obligation) => {
            const daysUntil = getDaysUntilDue(obligation?.dueDate);
            
            return (
              <div
                key={obligation?.id}
                className={`p-4 rounded-lg border transition-smooth hover:elevation-1 ${getUrgencyColor(daysUntil)}`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3">
                    <div className="p-2 rounded-lg bg-background/50">
                      <Icon name={getObligationIcon(obligation?.type)} size={16} />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <p className="font-medium text-foreground">{getObligationLabel(obligation?.type)}</p>
                        <span className="text-xs px-2 py-1 bg-background/50 rounded-full font-medium">
                          {getUrgencyLabel(daysUntil)}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">{obligation?.description}</p>
                      <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                        <Icon name="MapPin" size={12} />
                        <span>{obligation?.property}</span>
                        {obligation?.tenant && (
                          <>
                            <span>•</span>
                            <span>{obligation?.tenant}</span>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    {obligation?.amount && (
                      <p className="font-bold text-foreground mb-1">
                        {formatCurrency(obligation?.amount)}
                      </p>
                    )}
                    <p className="text-xs text-muted-foreground">
                      {formatDate(obligation?.dueDate)}
                    </p>
                  </div>
                </div>
                {obligation?.actions && obligation?.actions?.length > 0 && (
                  <div className="flex items-center space-x-2 mt-3 pt-3 border-t border-background/20">
                    {obligation?.actions?.map((action, index) => (
                      <Button key={index} variant="ghost" size="sm" className="text-xs">
                        <Icon name={action?.icon} size={12} className="mr-1" />
                        {action?.label}
                      </Button>
                    ))}
                  </div>
                )}
              </div>
            );
          })
        )}
      </div>
      {sortedObligations?.length > 0 && (
        <div className="flex items-center justify-between mt-4 pt-4 border-t border-border">
          <p className="text-sm text-muted-foreground">
            {sortedObligations?.length} obligation{sortedObligations?.length > 1 ? 's' : ''}
          </p>
          <Button variant="ghost" size="sm">
            <Icon name="Calendar" size={14} className="mr-2" />
            Voir calendrier
          </Button>
        </div>
      )}
    </div>
  );
};

export default UpcomingObligations;