import React, { useState } from 'react';
import { PieChart, Pie, Cell, ResponsiveContainer, Tooltip, BarChart, Bar, XAxis, YAxis, CartesianGrid } from 'recharts';
import Icon from '../../../components/AppIcon';

const ExpenseBreakdown = ({ data, totalExpenses }) => {
  const [viewType, setViewType] = useState('pie');

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0
    })?.format(value);
  };

  const formatPercentage = (value, total) => {
    return `${((value / total) * 100)?.toFixed(1)}%`;
  };

  const COLORS = [
    'var(--color-primary)',
    'var(--color-accent)',
    'var(--color-success)',
    'var(--color-warning)',
    'var(--color-error)',
    'var(--color-secondary)'
  ];

  const CustomTooltip = ({ active, payload }) => {
    if (active && payload && payload?.length) {
      const data = payload?.[0]?.payload;
      return (
        <div className="bg-popover border border-border rounded-lg p-3 elevation-2">
          <p className="font-medium text-popover-foreground mb-1">{data?.category}</p>
          <p className="text-sm text-muted-foreground">
            {formatCurrency(data?.amount)} ({formatPercentage(data?.amount, totalExpenses)})
          </p>
        </div>
      );
    }
    return null;
  };

  const getCategoryIcon = (category) => {
    switch (category?.toLowerCase()) {
      case 'maintenance': return 'Wrench';
      case 'assurance': return 'Shield';
      case 'taxes': return 'Receipt';
      case 'gestion': return 'Settings';
      case 'marketing': return 'Megaphone';
      case 'autres': return 'MoreHorizontal';
      default: return 'DollarSign';
    }
  };

  return (
    <div className="bg-card border border-border rounded-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-foreground">Répartition des dépenses</h3>
        <div className="flex bg-muted rounded-lg p-1">
          <button
            onClick={() => setViewType('pie')}
            className={`p-2 rounded transition-smooth ${
              viewType === 'pie' ?'bg-background text-foreground shadow-sm' :'text-muted-foreground hover:text-foreground'
            }`}
          >
            <Icon name="PieChart" size={16} />
          </button>
          <button
            onClick={() => setViewType('bar')}
            className={`p-2 rounded transition-smooth ${
              viewType === 'bar' ?'bg-background text-foreground shadow-sm' :'text-muted-foreground hover:text-foreground'
            }`}
          >
            <Icon name="BarChart3" size={16} />
          </button>
        </div>
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Chart */}
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            {viewType === 'pie' ? (
              <PieChart>
                <Pie
                  data={data}
                  cx="50%"
                  cy="50%"
                  innerRadius={40}
                  outerRadius={100}
                  paddingAngle={2}
                  dataKey="amount"
                >
                  {data?.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS?.[index % COLORS?.length]} />
                  ))}
                </Pie>
                <Tooltip content={<CustomTooltip />} />
              </PieChart>
            ) : (
              <BarChart data={data} layout="horizontal">
                <CartesianGrid strokeDasharray="3 3" stroke="var(--color-border)" />
                <XAxis 
                  type="number" 
                  stroke="var(--color-muted-foreground)"
                  fontSize={12}
                  tickFormatter={formatCurrency}
                />
                <YAxis 
                  type="category" 
                  dataKey="category" 
                  stroke="var(--color-muted-foreground)"
                  fontSize={12}
                  width={80}
                />
                <Tooltip content={<CustomTooltip />} />
                <Bar 
                  dataKey="amount" 
                  fill="var(--color-primary)" 
                  radius={[0, 4, 4, 0]}
                />
              </BarChart>
            )}
          </ResponsiveContainer>
        </div>

        {/* Legend & Details */}
        <div className="space-y-3">
          <div className="flex items-center justify-between mb-4">
            <h4 className="font-medium text-foreground">Détail par catégorie</h4>
            <p className="text-sm text-muted-foreground">
              Total: {formatCurrency(totalExpenses)}
            </p>
          </div>
          
          {data?.map((item, index) => (
            <div key={item?.category} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
              <div className="flex items-center space-x-3">
                <div 
                  className="w-4 h-4 rounded-full flex-shrink-0"
                  style={{ backgroundColor: COLORS?.[index % COLORS?.length] }}
                />
                <div className="flex items-center space-x-2">
                  <Icon name={getCategoryIcon(item?.category)} size={16} className="text-muted-foreground" />
                  <span className="font-medium text-foreground">{item?.category}</span>
                </div>
              </div>
              <div className="text-right">
                <p className="font-medium text-foreground">{formatCurrency(item?.amount)}</p>
                <p className="text-xs text-muted-foreground">
                  {formatPercentage(item?.amount, totalExpenses)}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ExpenseBreakdown;