"""
PropertyFlow Backend - Application principale Flask
Multi-tenant avec authentification JWT et intégration Supabase
"""

import os
from flask import Flask, request, jsonify
from flask_cors import CORS
from flask_jwt_extended import JWTManager, jwt_required, get_jwt_identity, get_jwt
from datetime import datetime, timedelta
import logging
from dotenv import load_dotenv

# Imports locaux
from config import Config
from database import init_db, get_supabase_client
from auth import auth_bp
from api.agences import agences_bp
from api.utilisateurs import utilisateurs_bp
from api.bailleurs import bailleurs_bp
from api.biens import biens_bp
from api.locataires import locataires_bp
from api.contrats import contrats_bp
from api.paiements import paiements_bp
from api.depenses import depenses_bp
from api.reversements import reversements_bp
from api.etats_lieux import etats_lieux_bp
from api.notifications import notifications_bp
from api.dashboard import dashboard_bp
from utils.errors import register_error_handlers
from utils.middleware import setup_middleware

# Charger les variables d'environnement
load_dotenv()

def create_app(config_class=Config):
    """Factory pour créer l'application Flask"""
    app = Flask(__name__)
    app.config.from_object(config_class)
    
    # Configuration du logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s %(levelname)s %(name)s %(message)s'
    )
    
    # Extensions
    CORS(app, origins=app.config['CORS_ORIGINS'])
    jwt = JWTManager(app)
    
    # Initialiser la base de données
    init_db(app)
    
    # Configuration JWT
    @jwt.token_in_blocklist_loader
    def check_if_token_revoked(jwt_header, jwt_payload):
        """Vérifier si le token JWT est révoqué"""
        # TODO: Implémenter la vérification avec Redis/DB
        return False
    
    @jwt.expired_token_loader
    def expired_token_callback(jwt_header, jwt_payload):
        return jsonify(message='Token has expired'), 401
    
    @jwt.invalid_token_loader
    def invalid_token_callback(error):
        return jsonify(message='Invalid token'), 401
    
    @jwt.unauthorized_loader
    def missing_token_callback(error):
        return jsonify(message='Authorization token is required'), 401
    
    # Middleware
    setup_middleware(app)
    
    # Gestionnaires d'erreurs
    register_error_handlers(app)
    
    # Routes de base
    @app.route('/health')
    def health_check():
        """Endpoint de santé"""
        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.utcnow().isoformat(),
            'version': '1.0.0'
        })
    
    @app.route('/api/v1/info')
    @jwt_required()
    def api_info():
        """Informations sur l'API"""
        current_user = get_jwt_identity()
        claims = get_jwt()
        
        return jsonify({
            'api_version': '1.0.0',
            'user_id': current_user,
            'agence_id': claims.get('agence_id'),
            'role': claims.get('role'),
            'endpoints': {
                'auth': '/api/v1/auth',
                'agences': '/api/v1/agences',
                'utilisateurs': '/api/v1/utilisateurs',
                'bailleurs': '/api/v1/bailleurs',
                'biens': '/api/v1/biens',
                'locataires': '/api/v1/locataires',
                'contrats': '/api/v1/contrats',
                'paiements': '/api/v1/paiements',
                'depenses': '/api/v1/depenses',
                'reversements': '/api/v1/reversements',
                'etats_lieux': '/api/v1/etats-lieux',
                'notifications': '/api/v1/notifications',
                'dashboard': '/api/v1/dashboard'
            }
        })
    
    # Enregistrement des blueprints
    app.register_blueprint(auth_bp, url_prefix='/api/v1/auth')
    app.register_blueprint(agences_bp, url_prefix='/api/v1/agences')
    app.register_blueprint(utilisateurs_bp, url_prefix='/api/v1/utilisateurs')
    app.register_blueprint(bailleurs_bp, url_prefix='/api/v1/bailleurs')
    app.register_blueprint(biens_bp, url_prefix='/api/v1/biens')
    app.register_blueprint(locataires_bp, url_prefix='/api/v1/locataires')
    app.register_blueprint(contrats_bp, url_prefix='/api/v1/contrats')
    app.register_blueprint(paiements_bp, url_prefix='/api/v1/paiements')
    app.register_blueprint(depenses_bp, url_prefix='/api/v1/depenses')
    app.register_blueprint(reversements_bp, url_prefix='/api/v1/reversements')
    app.register_blueprint(etats_lieux_bp, url_prefix='/api/v1/etats-lieux')
    app.register_blueprint(notifications_bp, url_prefix='/api/v1/notifications')
    app.register_blueprint(dashboard_bp, url_prefix='/api/v1/dashboard')
    
    return app

# Créer l'application
app = create_app()

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 5000))
    debug = os.environ.get('FLASK_ENV') == 'development'
    
    app.run(
        host='0.0.0.0',
        port=port,
        debug=debug
    )
