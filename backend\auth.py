"""
PropertyFlow Backend - Module d'authentification
Gestion JWT, login, register et RBAC multi-tenant
"""

from flask import Blueprint, request, jsonify, g
from flask_jwt_extended import (
    create_access_token, create_refresh_token, 
    jwt_required, get_jwt_identity, get_jwt
)
import bcrypt
import logging
from datetime import datetime, timedelta
from database import utilisateur_service, agence_service
from utils.errors import AuthenticationError, ValidationError, ConflictError, NotFoundError
from utils.middleware import validate_json, log_activity

logger = logging.getLogger(__name__)

# Blueprint pour l'authentification
auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/login', methods=['POST'])
@validate_json(['email', 'password'])
@log_activity('login_attempt')
def login():
    """Connexion utilisateur avec JWT"""
    try:
        data = request.get_json()
        email = data['email'].lower().strip()
        password = data['password']
        
        # Récupérer l'utilisateur
        user = utilisateur_service.get_by_email(email)
        if not user:
            raise AuthenticationError("Invalid credentials")
        
        # Vérifier le mot de passe
        if not bcrypt.checkpw(password.encode('utf-8'), user['mot_de_passe_hash'].encode('utf-8')):
            raise AuthenticationError("Invalid credentials")
        
        # Vérifier le statut
        if user['statut'] != 'actif':
            raise AuthenticationError("Account is not active")
        
        # Récupérer les informations de l'agence
        agence = agence_service.get_by_id(user['agence_id'])
        if not agence or agence['statut'] != 'actif':
            raise AuthenticationError("Agency is not active")
        
        # Créer les claims JWT
        additional_claims = {
            'agence_id': user['agence_id'],
            'role': user['role'],
            'permissions': user.get('permissions', {}),
            'agence_nom': agence['nom']
        }
        
        # Générer les tokens
        access_token = create_access_token(
            identity=user['id'],
            additional_claims=additional_claims
        )
        refresh_token = create_refresh_token(
            identity=user['id'],
            additional_claims=additional_claims
        )
        
        # Mettre à jour la dernière connexion
        utilisateur_service.update(user['id'], {
            'derniere_connexion': datetime.utcnow().isoformat()
        })
        
        logger.info(f"User {user['id']} logged in successfully")
        
        return jsonify({
            'success': True,
            'message': 'Login successful',
            'access_token': access_token,
            'refresh_token': refresh_token,
            'user': {
                'id': user['id'],
                'email': user['email'],
                'nom': user['nom'],
                'prenom': user['prenom'],
                'role': user['role'],
                'agence_id': user['agence_id'],
                'agence_nom': agence['nom']
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Login error: {str(e)}")
        raise

@auth_bp.route('/register', methods=['POST'])
@validate_json(['email', 'password', 'nom', 'prenom', 'agence_id'])
@log_activity('register_attempt')
def register():
    """Inscription d'un nouvel utilisateur"""
    try:
        data = request.get_json()
        email = data['email'].lower().strip()
        
        # Vérifier si l'utilisateur existe déjà
        existing_user = utilisateur_service.get_by_email(email)
        if existing_user:
            raise ConflictError("User already exists")
        
        # Vérifier que l'agence existe
        agence = agence_service.get_by_id(data['agence_id'])
        if not agence:
            raise NotFoundError("Agency not found")
        
        # Hasher le mot de passe
        password_hash = bcrypt.hashpw(
            data['password'].encode('utf-8'), 
            bcrypt.gensalt()
        ).decode('utf-8')
        
        # Créer l'utilisateur
        user_data = {
            'email': email,
            'mot_de_passe_hash': password_hash,
            'nom': data['nom'],
            'prenom': data['prenom'],
            'agence_id': data['agence_id'],
            'role': data.get('role', 'lecture'),  # Rôle par défaut
            'telephone': data.get('telephone'),
            'statut': 'actif'
        }
        
        new_user = utilisateur_service.create(user_data)
        
        logger.info(f"New user registered: {new_user['id']}")
        
        return jsonify({
            'success': True,
            'message': 'User registered successfully',
            'user': {
                'id': new_user['id'],
                'email': new_user['email'],
                'nom': new_user['nom'],
                'prenom': new_user['prenom'],
                'role': new_user['role']
            }
        }), 201
        
    except Exception as e:
        logger.error(f"Registration error: {str(e)}")
        raise

@auth_bp.route('/refresh', methods=['POST'])
@jwt_required(refresh=True)
def refresh():
    """Rafraîchir le token d'accès"""
    try:
        current_user_id = get_jwt_identity()
        claims = get_jwt()
        
        # Créer un nouveau token d'accès
        new_access_token = create_access_token(
            identity=current_user_id,
            additional_claims={
                'agence_id': claims.get('agence_id'),
                'role': claims.get('role'),
                'permissions': claims.get('permissions', {}),
                'agence_nom': claims.get('agence_nom')
            }
        )
        
        return jsonify({
            'success': True,
            'access_token': new_access_token
        }), 200
        
    except Exception as e:
        logger.error(f"Token refresh error: {str(e)}")
        raise

@auth_bp.route('/me', methods=['GET'])
@jwt_required()
def get_current_user():
    """Récupérer les informations de l'utilisateur connecté"""
    try:
        current_user_id = get_jwt_identity()
        claims = get_jwt()
        
        user = utilisateur_service.get_by_id(current_user_id)
        if not user:
            raise NotFoundError("User not found")
        
        return jsonify({
            'success': True,
            'user': {
                'id': user['id'],
                'email': user['email'],
                'nom': user['nom'],
                'prenom': user['prenom'],
                'role': user['role'],
                'agence_id': user['agence_id'],
                'agence_nom': claims.get('agence_nom'),
                'permissions': user.get('permissions', {}),
                'derniere_connexion': user.get('derniere_connexion')
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Get current user error: {str(e)}")
        raise

@auth_bp.route('/logout', methods=['POST'])
@jwt_required()
@log_activity('logout')
def logout():
    """Déconnexion (côté client principalement)"""
    try:
        current_user_id = get_jwt_identity()
        logger.info(f"User {current_user_id} logged out")
        
        # TODO: Ajouter le token à une blacklist Redis
        
        return jsonify({
            'success': True,
            'message': 'Logged out successfully'
        }), 200
        
    except Exception as e:
        logger.error(f"Logout error: {str(e)}")
        raise

@auth_bp.route('/change-password', methods=['POST'])
@jwt_required()
@validate_json(['current_password', 'new_password'])
@log_activity('change_password')
def change_password():
    """Changer le mot de passe"""
    try:
        current_user_id = get_jwt_identity()
        data = request.get_json()
        
        user = utilisateur_service.get_by_id(current_user_id)
        if not user:
            raise NotFoundError("User not found")
        
        # Vérifier le mot de passe actuel
        if not bcrypt.checkpw(
            data['current_password'].encode('utf-8'), 
            user['mot_de_passe_hash'].encode('utf-8')
        ):
            raise AuthenticationError("Current password is incorrect")
        
        # Hasher le nouveau mot de passe
        new_password_hash = bcrypt.hashpw(
            data['new_password'].encode('utf-8'), 
            bcrypt.gensalt()
        ).decode('utf-8')
        
        # Mettre à jour
        utilisateur_service.update(current_user_id, {
            'mot_de_passe_hash': new_password_hash
        })
        
        logger.info(f"Password changed for user {current_user_id}")
        
        return jsonify({
            'success': True,
            'message': 'Password changed successfully'
        }), 200
        
    except Exception as e:
        logger.error(f"Change password error: {str(e)}")
        raise
