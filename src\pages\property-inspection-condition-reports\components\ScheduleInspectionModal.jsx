import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';
import Select from '../../../components/ui/Select';

const ScheduleInspectionModal = ({ isOpen, onClose, inspection, onSave }) => {
  const [formData, setFormData] = useState({
    propertyId: '',
    type: '',
    scheduledDate: '',
    scheduledTime: '',
    inspectorId: '',
    tenantId: '',
    priority: 'medium',
    notes: ''
  });

  const [errors, setErrors] = useState({});

  // Mock data
  const properties = [
    { value: 'P001', label: '15 Rue de la Paix, Paris 75001' },
    { value: 'P002', label: '42 Avenue des Champs, Lyon 69001' },
    { value: 'P003', label: '8 Boulevard Saint-Michel, Paris 75005' }
  ];

  const inspectors = [
    { value: 'U001', label: '<PERSON>' },
    { value: 'U002', label: '<PERSON>' },
    { value: 'U003', label: '<PERSON>' }
  ];

  const inspectionTypes = [
    { value: 'entry', label: 'État des lieux d\'entrée' },
    { value: 'exit', label: 'État des lieux de sortie' },
    { value: 'periodic', label: 'Inspection périodique' },
    { value: 'maintenance', label: 'Inspection maintenance' }
  ];

  const priorities = [
    { value: 'low', label: 'Basse' },
    { value: 'medium', label: 'Moyenne' },
    { value: 'high', label: 'Haute' }
  ];

  useEffect(() => {
    if (inspection) {
      setFormData({
        propertyId: inspection?.property?.id || '',
        type: inspection?.type || '',
        scheduledDate: inspection?.scheduledDate || '',
        scheduledTime: inspection?.scheduledTime || '',
        inspectorId: inspection?.inspector?.id || '',
        tenantId: inspection?.tenant?.id || '',
        priority: inspection?.priority || 'medium',
        notes: inspection?.notes || ''
      });
    } else {
      setFormData({
        propertyId: '',
        type: '',
        scheduledDate: '',
        scheduledTime: '',
        inspectorId: '',
        tenantId: '',
        priority: 'medium',
        notes: ''
      });
    }
    setErrors({});
  }, [inspection]);

  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors?.[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData?.propertyId) {
      newErrors.propertyId = 'Veuillez sélectionner une propriété';
    }
    if (!formData?.type) {
      newErrors.type = 'Veuillez sélectionner un type d\'inspection';
    }
    if (!formData?.scheduledDate) {
      newErrors.scheduledDate = 'Veuillez sélectionner une date';
    }
    if (!formData?.scheduledTime) {
      newErrors.scheduledTime = 'Veuillez sélectionner une heure';
    }
    if (!formData?.inspectorId) {
      newErrors.inspectorId = 'Veuillez sélectionner un inspecteur';
    }

    // Validate date is not in the past
    if (formData?.scheduledDate) {
      const selectedDate = new Date(formData.scheduledDate);
      const today = new Date();
      today?.setHours(0, 0, 0, 0);
      
      if (selectedDate < today) {
        newErrors.scheduledDate = 'La date ne peut pas être dans le passé';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors)?.length === 0;
  };

  const handleSubmit = (e) => {
    e?.preventDefault();
    
    if (validateForm()) {
      onSave({
        id: inspection?.id || `INS${Date.now()}`,
        ...formData,
        status: inspection?.status || 'scheduled',
        createdAt: inspection?.createdAt || new Date()?.toISOString(),
        updatedAt: new Date()?.toISOString()
      });
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div className="absolute inset-0 bg-black/50" onClick={onClose} />
      {/* Modal */}
      <div className="relative bg-card border border-border rounded-lg shadow-lg w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border">
          <h2 className="text-xl font-semibold text-foreground">
            {inspection ? 'Modifier l\'inspection' : 'Planifier une inspection'}
          </h2>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <Icon name="X" size={20} />
          </Button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Propriété */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-foreground mb-2">
                Propriété *
              </label>
              <Select
                value={formData?.propertyId}
                onValueChange={(value) => handleChange('propertyId', value)}
                options={properties}
                placeholder="Sélectionner une propriété"
                error={errors?.propertyId}
              />
            </div>

            {/* Type d'inspection */}
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Type d'inspection *
              </label>
              <Select
                value={formData?.type}
                onValueChange={(value) => handleChange('type', value)}
                options={inspectionTypes}
                placeholder="Sélectionner un type"
                error={errors?.type}
              />
            </div>

            {/* Priorité */}
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Priorité
              </label>
              <Select
                value={formData?.priority}
                onValueChange={(value) => handleChange('priority', value)}
                options={priorities}
              />
            </div>

            {/* Date */}
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Date planifiée *
              </label>
              <Input
                type="date"
                value={formData?.scheduledDate}
                onChange={(e) => handleChange('scheduledDate', e?.target?.value)}
                error={errors?.scheduledDate}
              />
            </div>

            {/* Heure */}
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Heure planifiée *
              </label>
              <Input
                type="time"
                value={formData?.scheduledTime}
                onChange={(e) => handleChange('scheduledTime', e?.target?.value)}
                error={errors?.scheduledTime}
              />
            </div>

            {/* Inspecteur */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-foreground mb-2">
                Inspecteur *
              </label>
              <Select
                value={formData?.inspectorId}
                onValueChange={(value) => handleChange('inspectorId', value)}
                options={inspectors}
                placeholder="Sélectionner un inspecteur"
                error={errors?.inspectorId}
              />
            </div>

            {/* Notes */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-foreground mb-2">
                Notes additionnelles
              </label>
              <textarea
                className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent resize-none"
                rows={3}
                placeholder="Notes ou instructions spéciales pour cette inspection..."
                value={formData?.notes}
                onChange={(e) => handleChange('notes', e?.target?.value)}
              />
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center justify-end space-x-4 mt-6 pt-4 border-t border-border">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
            >
              Annuler
            </Button>
            <Button
              type="submit"
              variant="default"
            >
              {inspection ? 'Mettre à jour' : 'Planifier'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ScheduleInspectionModal;