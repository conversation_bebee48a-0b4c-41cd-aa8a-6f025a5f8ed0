import React, { useState, useRef } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';


const PhotoUploadModal = ({ isOpen, onClose, inspection, onUpload }) => {
  const [uploadedPhotos, setUploadedPhotos] = useState(inspection?.photos || []);
  const [dragActive, setDragActive] = useState(false);
  const [uploading, setUploading] = useState(false);
  const fileInputRef = useRef(null);

  const roomOptions = [
    'Entrée',
    'Salon',
    'Cuisine',
    'Chambre 1',
    'Chambre 2',
    'Chambre 3',
    'Salle de bain',
    'WC',
    'Balcon',
    'Terrasse',
    'Cave',
    'Parking',
    'Extérieur',
    'Autre'
  ];

  const handleDrag = (e) => {
    e?.preventDefault();
    e?.stopPropagation();
    if (e?.type === "dragenter" || e?.type === "dragover") {
      setDragActive(true);
    } else if (e?.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e?.preventDefault();
    e?.stopPropagation();
    setDragActive(false);
    
    if (e?.dataTransfer?.files && e?.dataTransfer?.files?.[0]) {
      handleFiles(e?.dataTransfer?.files);
    }
  };

  const handleChange = (e) => {
    e?.preventDefault();
    if (e?.target?.files && e?.target?.files?.[0]) {
      handleFiles(e?.target?.files);
    }
  };

  const handleFiles = (files) => {
    setUploading(true);
    const fileArray = Array.from(files);
    
    fileArray?.forEach((file, index) => {
      if (file?.type?.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const newPhoto = {
            id: `photo_${Date.now()}_${index}`,
            file: file,
            url: e?.target?.result,
            room: '',
            description: '',
            timestamp: new Date()?.toISOString()
          };
          
          setUploadedPhotos(prev => [...prev, newPhoto]);
        };
        reader?.readAsDataURL(file);
      }
    });
    
    setTimeout(() => setUploading(false), 1000);
  };

  const updatePhoto = (photoId, field, value) => {
    setUploadedPhotos(prev => 
      prev?.map(photo => 
        photo?.id === photoId 
          ? { ...photo, [field]: value }
          : photo
      )
    );
  };

  const removePhoto = (photoId) => {
    setUploadedPhotos(prev => prev?.filter(photo => photo?.id !== photoId));
  };

  const handleSave = () => {
    onUpload(uploadedPhotos);
  };

  const openFileDialog = () => {
    fileInputRef?.current?.click();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div className="absolute inset-0 bg-black/50" onClick={onClose} />
      {/* Modal */}
      <div className="relative bg-card border border-border rounded-lg shadow-lg w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border">
          <div>
            <h2 className="text-xl font-semibold text-foreground">
              Documentation photographique
            </h2>
            <p className="text-sm text-muted-foreground mt-1">
              {inspection?.property?.address} - {inspection?.type}
            </p>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <Icon name="X" size={20} />
          </Button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Upload Zone */}
          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
              dragActive 
                ? 'border-primary bg-primary/5' :'border-border hover:border-primary/50'
            }`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
            onClick={openFileDialog}
          >
            <input
              ref={fileInputRef}
              type="file"
              multiple
              accept="image/*"
              onChange={handleChange}
              className="hidden"
            />
            
            {uploading ? (
              <div className="flex flex-col items-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-4" />
                <p className="text-sm text-muted-foreground">
                  Téléchargement en cours...
                </p>
              </div>
            ) : (
              <div className="flex flex-col items-center">
                <Icon name="Upload" size={48} className="text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium text-foreground mb-2">
                  Glissez-déposez vos photos ici
                </h3>
                <p className="text-sm text-muted-foreground mb-4">
                  ou cliquez pour sélectionner des fichiers
                </p>
                <Button variant="outline" size="sm">
                  <Icon name="Camera" size={16} className="mr-2" />
                  Choisir des photos
                </Button>
              </div>
            )}
          </div>

          {/* Photos Grid */}
          {uploadedPhotos?.length > 0 && (
            <div className="mt-8">
              <h3 className="text-lg font-medium text-foreground mb-4">
                Photos téléchargées ({uploadedPhotos?.length})
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {uploadedPhotos?.map((photo) => (
                  <div key={photo?.id} className="bg-muted rounded-lg overflow-hidden">
                    {/* Photo Preview */}
                    <div className="relative aspect-video bg-muted">
                      <img
                        src={photo?.url}
                        alt="Preview"
                        className="w-full h-full object-cover"
                      />
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removePhoto(photo?.id)}
                        className="absolute top-2 right-2 bg-red-500 text-white hover:bg-red-600"
                      >
                        <Icon name="Trash2" size={16} />
                      </Button>
                    </div>
                    
                    {/* Photo Details */}
                    <div className="p-3 space-y-3">
                      <div>
                        <label className="block text-xs font-medium text-foreground mb-1">
                          Pièce
                        </label>
                        <select
                          value={photo?.room}
                          onChange={(e) => updatePhoto(photo?.id, 'room', e?.target?.value)}
                          className="w-full px-2 py-1 text-xs border border-input rounded focus:outline-none focus:ring-1 focus:ring-ring"
                        >
                          <option value="">Sélectionner une pièce</option>
                          {roomOptions?.map(room => (
                            <option key={room} value={room}>
                              {room}
                            </option>
                          ))}
                        </select>
                      </div>
                      
                      <div>
                        <label className="block text-xs font-medium text-foreground mb-1">
                          Description
                        </label>
                        <textarea
                          value={photo?.description}
                          onChange={(e) => updatePhoto(photo?.id, 'description', e?.target?.value)}
                          placeholder="Décrivez ce qui est visible sur cette photo..."
                          className="w-full px-2 py-1 text-xs border border-input rounded focus:outline-none focus:ring-1 focus:ring-ring resize-none"
                          rows={2}
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Instructions */}
          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <div className="flex items-start space-x-3">
              <Icon name="Info" size={20} className="text-blue-600 mt-0.5" />
              <div>
                <h4 className="text-sm font-medium text-blue-900 mb-2">
                  Conseils pour une documentation efficace
                </h4>
                <ul className="text-xs text-blue-800 space-y-1">
                  <li>• Prenez des photos de chaque pièce sous différents angles</li>
                  <li>• Documentez tous les défauts, dommages ou points d'attention</li>
                  <li>• Assurez-vous que les photos sont nettes et bien éclairées</li>
                  <li>• Ajoutez des descriptions détaillées pour chaque photo</li>
                  <li>• Organisez les photos par pièce pour faciliter le rapport</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-between p-6 border-t border-border">
          <div className="text-sm text-muted-foreground">
            {uploadedPhotos?.length} photo(s) prête(s) à sauvegarder
          </div>
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              onClick={onClose}
            >
              Annuler
            </Button>
            <Button
              variant="default"
              onClick={handleSave}
              disabled={uploadedPhotos?.length === 0}
            >
              Sauvegarder les photos
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PhotoUploadModal;