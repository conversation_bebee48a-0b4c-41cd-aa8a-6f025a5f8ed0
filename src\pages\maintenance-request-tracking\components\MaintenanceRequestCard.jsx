import React from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const MaintenanceRequestCard = ({ 
  request, 
  onStatusUpdate, 
  onAssignContractor, 
  onViewDetails,
  onScheduleInspection,
  onContactTenant 
}) => {
  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'urgent': return 'bg-error text-error-foreground';
      case 'high': return 'bg-warning text-warning-foreground';
      case 'medium': return 'bg-accent text-accent-foreground';
      case 'low': return 'bg-muted text-muted-foreground';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'open': return 'bg-error/10 text-error border-error/20';
      case 'in_progress': return 'bg-warning/10 text-warning border-warning/20';
      case 'scheduled': return 'bg-accent/10 text-accent border-accent/20';
      case 'completed': return 'bg-success/10 text-success border-success/20';
      case 'cancelled': return 'bg-muted text-muted-foreground border-border';
      default: return 'bg-muted text-muted-foreground border-border';
    }
  };

  const getStatusLabel = (status) => {
    switch (status) {
      case 'open': return 'Ouvert';
      case 'in_progress': return 'En cours';
      case 'scheduled': return 'Programmé';
      case 'completed': return 'Terminé';
      case 'cancelled': return 'Annulé';
      default: return status;
    }
  };

  const formatDate = (date) => {
    return new Date(date)?.toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  return (
    <div className="bg-card border border-border rounded-lg p-4 hover:elevation-2 transition-smooth">
      {/* Header */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center space-x-3">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(request?.priority)}`}>
            {request?.priority?.toUpperCase()}
          </span>
          <span className={`px-2 py-1 rounded border text-xs font-medium ${getStatusColor(request?.status)}`}>
            {getStatusLabel(request?.status)}
          </span>
        </div>
        <span className="text-xs text-muted-foreground">#{request?.id}</span>
      </div>
      {/* Request Details */}
      <div className="mb-4">
        <h3 className="font-semibold text-foreground mb-2">{request?.title}</h3>
        <p className="text-sm text-muted-foreground mb-3 line-clamp-2">{request?.description}</p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
          <div className="flex items-center space-x-2">
            <Icon name="MapPin" size={16} className="text-muted-foreground" />
            <span className="text-foreground">{request?.property?.address}</span>
          </div>
          <div className="flex items-center space-x-2">
            <Icon name="User" size={16} className="text-muted-foreground" />
            <span className="text-foreground">{request?.tenant?.name}</span>
          </div>
          <div className="flex items-center space-x-2">
            <Icon name="Calendar" size={16} className="text-muted-foreground" />
            <span className="text-foreground">{formatDate(request?.createdAt)}</span>
          </div>
          {request?.contractor && (
            <div className="flex items-center space-x-2">
              <Icon name="Wrench" size={16} className="text-muted-foreground" />
              <span className="text-foreground">{request?.contractor?.name}</span>
            </div>
          )}
        </div>
      </div>
      {/* Cost Information */}
      {request?.estimatedCost && (
        <div className="mb-4 p-3 bg-muted rounded-lg">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Coût estimé:</span>
            <span className="font-data font-medium text-foreground">{request?.estimatedCost}€</span>
          </div>
          {request?.actualCost && (
            <div className="flex items-center justify-between text-sm mt-1">
              <span className="text-muted-foreground">Coût réel:</span>
              <span className="font-data font-medium text-foreground">{request?.actualCost}€</span>
            </div>
          )}
        </div>
      )}
      {/* Photos */}
      {request?.photos && request?.photos?.length > 0 && (
        <div className="mb-4">
          <div className="flex items-center space-x-2 mb-2">
            <Icon name="Camera" size={16} className="text-muted-foreground" />
            <span className="text-sm text-muted-foreground">{request?.photos?.length} photo(s)</span>
          </div>
          <div className="flex space-x-2">
            {request?.photos?.slice(0, 3)?.map((photo, index) => (
              <div key={index} className="w-12 h-12 bg-muted rounded border overflow-hidden">
                <img 
                  src={photo} 
                  alt={`Photo ${index + 1}`}
                  className="w-full h-full object-cover"
                />
              </div>
            ))}
            {request?.photos?.length > 3 && (
              <div className="w-12 h-12 bg-muted rounded border flex items-center justify-center">
                <span className="text-xs text-muted-foreground">+{request?.photos?.length - 3}</span>
              </div>
            )}
          </div>
        </div>
      )}
      {/* Actions */}
      <div className="flex flex-wrap gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => onViewDetails(request?.id)}
        >
          <Icon name="Eye" size={14} className="mr-1" />
          Détails
        </Button>
        
        {request?.status !== 'completed' && request?.status !== 'cancelled' && (
          <>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onAssignContractor(request?.id)}
            >
              <Icon name="UserPlus" size={14} className="mr-1" />
              Assigner
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => onScheduleInspection(request?.id)}
            >
              <Icon name="Calendar" size={14} className="mr-1" />
              Programmer
            </Button>
          </>
        )}
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onContactTenant(request?.id)}
        >
          <Icon name="MessageCircle" size={14} className="mr-1" />
          Contacter
        </Button>
      </div>
    </div>
  );
};

export default MaintenanceRequestCard;