import React, { useState, useEffect } from 'react';
import AgencyHeader from '../../components/ui/AgencyHeader';
import PrimaryNavigation from '../../components/ui/PrimaryNavigation';
import FinancialMetricsCards from './components/FinancialMetricsCards';
import RevenueChart from './components/RevenueChart';
import ExpenseBreakdown from './components/ExpenseBreakdown';
import TransactionsList from './components/TransactionsList';
import UpcomingObligations from './components/UpcomingObligations';
import PropertyProfitability from './components/PropertyProfitability';
import FinancialToolbar from './components/FinancialToolbar';

const FinancialDashboardReporting = () => {
  const [currentPeriod, setCurrentPeriod] = useState('12m');
  const [isLoading, setIsLoading] = useState(true);

  // Mock financial metrics data
  const financialMetrics = [
    {
      id: 1,
      type: 'revenue',
      label: 'Revenus totaux',
      value: 125750,
      change: 8.2,
      period: currentPeriod
    },
    {
      id: 2,
      type: 'expenses',
      label: 'Dépenses totales',
      value: 42300,
      change: -3.1,
      period: currentPeriod
    },
    {
      id: 3,
      type: 'profit',
      label: 'Bénéfice net',
      value: 83450,
      change: 12.7,
      period: currentPeriod
    },
    {
      id: 4,
      type: 'collection',
      label: 'Taux de recouvrement',
      value: 94.2,
      change: 2.1,
      period: currentPeriod
    }
  ];

  // Mock revenue chart data
  const revenueChartData = [
    { month: 'Jan', revenue: 9500, expenses: 3200 },
    { month: 'Fév', revenue: 10200, expenses: 3800 },
    { month: 'Mar', revenue: 11800, expenses: 4100 },
    { month: 'Avr', revenue: 10900, expenses: 3600 },
    { month: 'Mai', revenue: 12300, expenses: 4200 },
    { month: 'Jun', revenue: 11700, expenses: 3900 },
    { month: 'Jul', revenue: 13100, expenses: 4500 },
    { month: 'Aoû', revenue: 12800, expenses: 4300 },
    { month: 'Sep', revenue: 11900, expenses: 3700 },
    { month: 'Oct', revenue: 12600, expenses: 4000 },
    { month: 'Nov', revenue: 13400, expenses: 4600 },
    { month: 'Déc', revenue: 14200, expenses: 4800 }
  ];

  // Mock expense breakdown data
  const expenseBreakdownData = [
    { category: 'Maintenance', amount: 15200 },
    { category: 'Assurance', amount: 8900 },
    { category: 'Taxes', amount: 7600 },
    { category: 'Gestion', amount: 6400 },
    { category: 'Marketing', amount: 2800 },
    { category: 'Autres', amount: 1400 }
  ];

  const totalExpenses = expenseBreakdownData?.reduce((sum, item) => sum + item?.amount, 0);

  // Mock transactions data
  const transactionsData = [
    {
      id: 1,
      type: 'rent',
      description: 'Loyer septembre - Appartement 3B',
      property: '15 Rue de la Paix',
      amount: 1250,
      date: new Date(2025, 8, 1),
      status: 'completed'
    },
    {
      id: 2,
      type: 'deposit',
      description: 'Dépôt de garantie - Nouveau locataire',
      property: '8 Avenue Mozart',
      amount: 1800,
      date: new Date(2025, 8, 2),
      status: 'completed'
    },
    {
      id: 3,
      type: 'maintenance',
      description: 'Réparation plomberie',
      property: '22 Rue Victor Hugo',
      amount: -320,
      date: new Date(2025, 8, 3),
      status: 'completed'
    },
    {
      id: 4,
      type: 'fee',
      description: 'Commission gestion',
      property: '15 Rue de la Paix',
      amount: 125,
      date: new Date(2025, 8, 4),
      status: 'pending'
    },
    {
      id: 5,
      type: 'penalty',
      description: 'Pénalité retard de paiement',
      property: '5 Place de la République',
      amount: 75,
      date: new Date(2025, 8, 5),
      status: 'completed'
    }
  ];

  // Mock upcoming obligations data
  const upcomingObligations = [
    {
      id: 1,
      type: 'rent_due',
      description: 'Loyer octobre - Jean Martin',
      property: '15 Rue de la Paix',
      tenant: 'Jean Martin',
      amount: 1250,
      dueDate: new Date(2025, 9, 1),
      actions: [
        { label: 'Rappel', icon: 'Mail' },
        { label: 'Appeler', icon: 'Phone' }
      ]
    },
    {
      id: 2,
      type: 'tax_payment',
      description: 'Taxe foncière Q4',
      property: '8 Avenue Mozart',
      amount: 890,
      dueDate: new Date(2025, 9, 15),
      actions: [
        { label: 'Payer', icon: 'CreditCard' }
      ]
    },
    {
      id: 3,
      type: 'insurance',
      description: 'Renouvellement assurance habitation',
      property: '22 Rue Victor Hugo',
      amount: 450,
      dueDate: new Date(2025, 8, 28),
      actions: [
        { label: 'Renouveler', icon: 'RefreshCw' }
      ]
    },
    {
      id: 4,
      type: 'lease_renewal',
      description: 'Renouvellement bail - Sophie Laurent',
      property: '5 Place de la République',
      tenant: 'Sophie Laurent',
      dueDate: new Date(2025, 9, 30),
      actions: [
        { label: 'Préparer', icon: 'FileText' },
        { label: 'Contacter', icon: 'Phone' }
      ]
    },
    {
      id: 5,
      type: 'inspection',
      description: 'État des lieux sortie',
      property: '12 Boulevard Saint-Germain',
      tenant: 'Pierre Moreau',
      dueDate: new Date(2025, 9, 10),
      actions: [
        { label: 'Programmer', icon: 'Calendar' }
      ]
    }
  ];

  // Mock property profitability data
  const propertyProfitabilityData = [
    {
      id: 1,
      property: 'Appartement 3B',
      address: '15 Rue de la Paix',
      revenue: 15000,
      expenses: 4200,
      profit: 10800,
      roi: 8.5
    },
    {
      id: 2,
      property: 'Studio Mozart',
      address: '8 Avenue Mozart',
      revenue: 10800,
      expenses: 2900,
      profit: 7900,
      roi: 7.2
    },
    {
      id: 3,
      property: 'T3 Victor Hugo',
      address: '22 Rue Victor Hugo',
      revenue: 18600,
      expenses: 5800,
      profit: 12800,
      roi: 9.1
    },
    {
      id: 4,
      property: 'T2 République',
      address: '5 Place de la République',
      revenue: 13200,
      expenses: 4100,
      profit: 9100,
      roi: 6.8
    },
    {
      id: 5,
      property: 'Loft Saint-Germain',
      address: '12 Boulevard Saint-Germain',
      revenue: 22400,
      expenses: 7200,
      profit: 15200,
      roi: 10.3
    }
  ];

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const handleDateRangeChange = (range) => {
    setCurrentPeriod(range);
    console.log('Date range changed to:', range);
  };

  const handleExport = (format) => {
    console.log('Exporting data in format:', format);
    // Here you would implement the actual export logic
  };

  const handleGenerateReport = (type) => {
    console.log('Generating report of type:', type);
    // Here you would implement the actual report generation logic
  };

  const handleLogout = () => {
    console.log('User logged out');
    // Here you would implement logout logic
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <AgencyHeader 
          agencyName="PropertyFlow"
          userName="Marie Dubois"
          userRole="Gestionnaire Financier"
          onLogout={handleLogout}
          notificationCount={3}
        />
        <PrimaryNavigation />
        <div className="pt-32 px-6">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Chargement des données financières...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <AgencyHeader 
        agencyName="PropertyFlow"
        userName="Marie Dubois"
        userRole="Gestionnaire Financier"
        onLogout={handleLogout}
        notificationCount={3}
      />
      <PrimaryNavigation />
      
      <main className="pt-32 px-6 pb-8">
        <div className="max-w-7xl mx-auto">
          {/* Page Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-foreground mb-2">
              Tableau de Bord Financier
            </h1>
            <p className="text-muted-foreground">
              Analyses financières et rapports détaillés de votre portefeuille immobilier
            </p>
          </div>

          {/* Financial Toolbar */}
          <FinancialToolbar
            onDateRangeChange={handleDateRangeChange}
            onExport={handleExport}
            onGenerateReport={handleGenerateReport}
          />

          {/* Financial Metrics Cards */}
          <FinancialMetricsCards 
            metrics={financialMetrics}
            period={currentPeriod}
          />

          {/* Charts Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <RevenueChart 
              data={revenueChartData}
              title="Évolution des revenus et dépenses"
            />
            <ExpenseBreakdown 
              data={expenseBreakdownData}
              totalExpenses={totalExpenses}
            />
          </div>

          {/* Property Profitability */}
          <div className="mb-8">
            <PropertyProfitability data={propertyProfitabilityData} />
          </div>

          {/* Transactions and Obligations */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <TransactionsList transactions={transactionsData} />
            <UpcomingObligations obligations={upcomingObligations} />
          </div>
        </div>
      </main>
    </div>
  );
};

export default FinancialDashboardReporting;