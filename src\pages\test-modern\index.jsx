import React from 'react';
import Button from '../../components/ui/Button';
import Icon from '../../components/AppIcon';

const TestModern = () => {
  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Test Modern Components</h1>
      
      <div className="space-y-4">
        <div>
          <h2 className="text-xl font-semibold mb-2">Buttons</h2>
          <div className="flex flex-wrap gap-2">
            <Button variant="default">
              <Icon name="Check" size={16} className="mr-2" />
              Default
            </Button>
            <Button variant="outline">
              <Icon name="Info" size={16} className="mr-2" />
              Outline
            </Button>
            <Button variant="ghost">
              <Icon name="Ghost" size={16} className="mr-2" />
              Ghost
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestModern;