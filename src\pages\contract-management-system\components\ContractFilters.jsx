import React from 'react';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';
import Select from '../../../components/ui/Select';
import Icon from '../../../components/AppIcon';

const ContractFilters = ({ 
  onFilterChange, 
  onSearchChange, 
  onClearFilters, 
  activeFilters = {}, 
  className = "" 
}) => {
  const handleFilterChange = (filterType) => (value) => {
    onFilterChange?.(filterType, value);
  };

  const handleSearchChange = (e) => {
    onSearchChange?.(e?.target?.value);
  };

  const hasActiveFilters = Object.values(activeFilters)?.some(value => value && value !== 'all');

  const typeOptions = [
    { value: 'all', label: 'Tous les types' },
    { value: 'lease', label: 'Bail de location' },
    { value: 'management', label: 'Mandat de gestion' },
    { value: 'service', label: 'Contrat de service' }
  ];

  const statusOptions = [
    { value: 'all', label: 'Tous les statuts' },
    { value: 'active', label: 'Actif' },
    { value: 'pending', label: 'En attente' },
    { value: 'draft', label: 'Brouillon' },
    { value: 'expired', label: 'Expiré' },
    { value: 'terminated', label: 'Résilié' }
  ];

  const complianceOptions = [
    { value: 'all', label: 'Toute conformité' },
    { value: 'compliant', label: 'Conforme' },
    { value: 'expiring_soon', label: 'Expire bientôt' },
    { value: 'pending_review', label: 'En révision' },
    { value: 'non_compliant', label: 'Non conforme' }
  ];

  const renewalTypeOptions = [
    { value: 'all', label: 'Tous renouvellements' },
    { value: 'automatic', label: 'Automatique' },
    { value: 'manual', label: 'Manuel' }
  ];

  return (
    <div className={`bg-card rounded-lg p-6 border border-border ${className}`}>
      <div className="flex flex-col lg:flex-row lg:items-center gap-4">
        {/* Search Input */}
        <div className="flex-1 min-w-0">
          <div className="relative">
            <Icon 
              name="Search" 
              size={20} 
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" 
            />
            <Input
              type="text"
              placeholder="Rechercher par numéro, titre, propriétaire, locataire..."
              onChange={handleSearchChange}
              className="pl-10 pr-4"
            />
          </div>
        </div>

        {/* Filters */}
        <div className="flex flex-wrap lg:flex-nowrap items-center gap-3">
          <Select
            value={activeFilters?.type || 'all'}
            onValueChange={handleFilterChange('type')}
            options={typeOptions}
            placeholder="Type"
            className="min-w-[140px]"
          />

          <Select
            value={activeFilters?.status || 'all'}
            onValueChange={handleFilterChange('status')}
            options={statusOptions}
            placeholder="Statut"
            className="min-w-[140px]"
          />

          <Select
            value={activeFilters?.complianceStatus || 'all'}
            onValueChange={handleFilterChange('complianceStatus')}
            options={complianceOptions}
            placeholder="Conformité"
            className="min-w-[140px]"
          />

          <Select
            value={activeFilters?.renewalType || 'all'}
            onValueChange={handleFilterChange('renewalType')}
            options={renewalTypeOptions}
            placeholder="Renouvellement"
            className="min-w-[140px]"
          />

          {/* Date Filter */}
          <Input
            type="date"
            placeholder="Expire avant"
            value={activeFilters?.expiringBefore || ''}
            onChange={(e) => handleFilterChange('expiringBefore')(e?.target?.value)}
            className="min-w-[160px]"
          />

          {/* Clear Filters */}
          {hasActiveFilters && (
            <Button
              variant="outline"
              onClick={onClearFilters}
              className="whitespace-nowrap"
              iconName="X"
            >
              Effacer
            </Button>
          )}
        </div>
      </div>
      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="mt-4 pt-4 border-t border-border">
          <div className="flex flex-wrap items-center gap-2">
            <span className="text-sm font-medium text-muted-foreground mr-2">
              Filtres actifs:
            </span>
            {Object.entries(activeFilters)?.map(([key, value]) => {
              if (!value || value === 'all') return null;
              
              let displayValue = value;
              if (key === 'type') {
                const option = typeOptions?.find(opt => opt?.value === value);
                displayValue = option?.label || value;
              } else if (key === 'status') {
                const option = statusOptions?.find(opt => opt?.value === value);
                displayValue = option?.label || value;
              } else if (key === 'complianceStatus') {
                const option = complianceOptions?.find(opt => opt?.value === value);
                displayValue = option?.label || value;
              } else if (key === 'renewalType') {
                const option = renewalTypeOptions?.find(opt => opt?.value === value);
                displayValue = option?.label || value;
              } else if (key === 'expiringBefore') {
                displayValue = `Expire avant ${new Date(value)?.toLocaleDateString('fr-FR')}`;
              }

              return (
                <span
                  key={key}
                  className="inline-flex items-center gap-1 px-2 py-1 bg-primary/10 text-primary rounded-md text-xs font-medium"
                >
                  {displayValue}
                  <button
                    onClick={() => handleFilterChange(key)('all')}
                    className="hover:bg-primary/20 rounded-sm p-0.5"
                  >
                    <Icon name="X" size={10} />
                  </button>
                </span>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

export default ContractFilters;