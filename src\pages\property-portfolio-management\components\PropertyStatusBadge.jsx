import React from 'react';
import Icon from '../../../components/AppIcon';

const PropertyStatusBadge = ({ status, className = "" }) => {
  const getStatusConfig = (status) => {
    switch (status) {
      case 'occupied':
        return {
          label: 'Occupé',
          icon: 'CheckCircle',
          className: 'bg-success/10 text-success border-success/20'
        };
      case 'vacant':
        return {
          label: 'Vacant',
          icon: 'Circle',
          className: 'bg-warning/10 text-warning border-warning/20'
        };
      case 'maintenance':
        return {
          label: 'En maintenance',
          icon: 'Wrench',
          className: 'bg-error/10 text-error border-error/20'
        };
      case 'reserved':
        return {
          label: 'Réservé',
          icon: 'Clock',
          className: 'bg-accent/10 text-accent border-accent/20'
        };
      default:
        return {
          label: 'Inconnu',
          icon: 'HelpCircle',
          className: 'bg-muted text-muted-foreground border-border'
        };
    }
  };

  const config = getStatusConfig(status);

  return (
    <span className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium border ${config?.className} ${className}`}>
      <Icon name={config?.icon} size={12} />
      <span>{config?.label}</span>
    </span>
  );
};

export default PropertyStatusBadge;