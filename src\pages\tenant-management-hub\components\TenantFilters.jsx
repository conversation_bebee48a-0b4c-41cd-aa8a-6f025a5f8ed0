import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';
import Select from '../../../components/ui/Select';

const TenantFilters = ({ 
  onFilterChange = () => {},
  onSearchChange = () => {},
  onClearFilters = () => {},
  activeFilters = {},
  className = ""
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  const leaseStatusOptions = [
    { value: 'all', label: 'Tous les statuts' },
    { value: 'active', label: 'Bail actif' },
    { value: 'expired', label: 'Bail expiré' },
    { value: 'expiring_soon', label: 'Expire bientôt' },
    { value: 'terminated', label: 'Résilié' },
    { value: 'pending', label: 'En attente' }
  ];

  const paymentStatusOptions = [
    { value: 'all', label: 'Tous les paiements' },
    { value: 'current', label: 'À jour' },
    { value: 'late', label: 'En retard' },
    { value: 'partial', label: 'Paiement partiel' },
    { value: 'pending', label: 'En attente' }
  ];

  const propertyTypeOptions = [
    { value: 'all', label: 'Tous les types' },
    { value: 'apartment', label: 'Appartement' },
    { value: 'house', label: 'Maison' },
    { value: 'studio', label: 'Studio' },
    { value: 'commercial', label: 'Commercial' }
  ];

  const cityOptions = [
    { value: 'all', label: 'Toutes les villes' },
    { value: 'paris', label: 'Paris' },
    { value: 'lyon', label: 'Lyon' },
    { value: 'marseille', label: 'Marseille' },
    { value: 'toulouse', label: 'Toulouse' },
    { value: 'nice', label: 'Nice' }
  ];

  const handleSearchChange = (e) => {
    const value = e?.target?.value;
    setSearchTerm(value);
    onSearchChange(value);
  };

  const handleFilterChange = (filterType, value) => {
    onFilterChange(filterType, value);
  };

  const clearAllFilters = () => {
    setSearchTerm('');
    onClearFilters();
  };

  const hasActiveFilters = Object.values(activeFilters)?.some(value => 
    value && value !== 'all' && value !== ''
  );

  return (
    <div className={`bg-card border border-border rounded-lg p-4 ${className}`}>
      {/* Search and Quick Actions */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-4">
        <div className="flex-1 max-w-md">
          <Input
            type="search"
            placeholder="Rechercher par nom, email, propriété..."
            value={searchTerm}
            onChange={handleSearchChange}
            className="w-full"
          />
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant={isExpanded ? "default" : "outline"}
            onClick={() => setIsExpanded(!isExpanded)}
            iconName="Filter"
            iconPosition="left"
          >
            Filtres avancés
          </Button>
          
          {hasActiveFilters && (
            <Button
              variant="ghost"
              onClick={clearAllFilters}
              iconName="X"
              iconPosition="left"
            >
              Effacer
            </Button>
          )}
        </div>
      </div>
      {/* Advanced Filters */}
      {isExpanded && (
        <div className="border-t border-border pt-4 animate-slide-in">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Select
              label="Statut du bail"
              options={leaseStatusOptions}
              value={activeFilters?.leaseStatus || 'all'}
              onChange={(value) => handleFilterChange('leaseStatus', value)}
            />
            
            <Select
              label="Statut de paiement"
              options={paymentStatusOptions}
              value={activeFilters?.paymentStatus || 'all'}
              onChange={(value) => handleFilterChange('paymentStatus', value)}
            />
            
            <Select
              label="Type de propriété"
              options={propertyTypeOptions}
              value={activeFilters?.propertyType || 'all'}
              onChange={(value) => handleFilterChange('propertyType', value)}
            />
            
            <Select
              label="Ville"
              options={cityOptions}
              value={activeFilters?.city || 'all'}
              onChange={(value) => handleFilterChange('city', value)}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
            <Input
              type="date"
              label="Bail expire après"
              value={activeFilters?.leaseExpiresAfter || ''}
              onChange={(e) => handleFilterChange('leaseExpiresAfter', e?.target?.value)}
            />
            
            <Input
              type="number"
              label="Loyer minimum (€)"
              placeholder="500"
              value={activeFilters?.minRent || ''}
              onChange={(e) => handleFilterChange('minRent', e?.target?.value)}
            />
            
            <Input
              type="number"
              label="Loyer maximum (€)"
              placeholder="2000"
              value={activeFilters?.maxRent || ''}
              onChange={(e) => handleFilterChange('maxRent', e?.target?.value)}
            />
          </div>
        </div>
      )}
      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2 mt-4 pt-4 border-t border-border">
          <span className="text-sm text-muted-foreground">Filtres actifs:</span>
          {Object.entries(activeFilters)?.map(([key, value]) => {
            if (!value || value === 'all' || value === '') return null;
            
            let displayValue = value;
            if (key === 'leaseStatus') {
              displayValue = leaseStatusOptions?.find(opt => opt?.value === value)?.label || value;
            } else if (key === 'paymentStatus') {
              displayValue = paymentStatusOptions?.find(opt => opt?.value === value)?.label || value;
            } else if (key === 'propertyType') {
              displayValue = propertyTypeOptions?.find(opt => opt?.value === value)?.label || value;
            } else if (key === 'city') {
              displayValue = cityOptions?.find(opt => opt?.value === value)?.label || value;
            }

            return (
              <span
                key={key}
                className="inline-flex items-center gap-1 px-2 py-1 bg-primary/10 text-primary text-xs rounded-full"
              >
                {displayValue}
                <button
                  onClick={() => handleFilterChange(key, '')}
                  className="hover:bg-primary/20 rounded-full p-0.5"
                >
                  <Icon name="X" size={12} />
                </button>
              </span>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default TenantFilters;