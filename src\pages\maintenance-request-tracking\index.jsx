import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet';
import AgencyHeader from '../../components/ui/AgencyHeader';
import PrimaryNavigation from '../../components/ui/PrimaryNavigation';
import NotificationCenter from '../../components/ui/NotificationCenter';
import QuickActionPanel from '../../components/ui/QuickActionPanel';
import MaintenanceStats from './components/MaintenanceStats';
import MaintenanceFilters from './components/MaintenanceFilters';
import MaintenanceTable from './components/MaintenanceTable';
import MaintenanceRequestCard from './components/MaintenanceRequestCard';
import CreateRequestModal from './components/CreateRequestModal';
import Icon from '../../components/AppIcon';
import Button from '../../components/ui/Button';

const MaintenanceRequestTracking = () => {
  const [viewMode, setViewMode] = useState('table');
  const [showNotifications, setShowNotifications] = useState(false);
  const [showQuickActions, setShowQuickActions] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    status: 'all',
    priority: 'all',
    property: 'all',
    contractor: 'all',
    dateFrom: '',
    dateTo: '',
    search: ''
  });

  // Mock data
  const [maintenanceRequests, setMaintenanceRequests] = useState([]);
  const [stats, setStats] = useState({});
  const [properties, setProperties] = useState([]);
  const [tenants, setTenants] = useState([]);
  const [contractors, setContractors] = useState([]);

  useEffect(() => {
    // Simulate data loading
    const loadData = async () => {
      setLoading(true);
      
      // Mock maintenance requests
      const mockRequests = [
        {
          id: 'MR001',
          title: 'Fuite d\'eau dans la salle de bain',
          description: 'Le robinet de la douche fuit constamment, causant une accumulation d\'eau sur le sol. Le problème s\'aggrave et nécessite une intervention rapide.',
          priority: 'urgent',
          status: 'open',
          category: 'plumbing',
          property: {
            id: 'P001',
            address: '15 Rue de la Paix',
            unit: 'Apt 3B'
          },
          tenant: {
            id: 'T001',
            name: 'Sophie Martin',
            phone: '06 12 34 56 78'
          },
          contractor: null,
          estimatedCost: 150,
          actualCost: null,
          createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
          photos: [
            'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400',
            'https://images.unsplash.com/photo-1584622650111-993a426fbf0a?w=400'
          ]
        },
        {
          id: 'MR002',
          title: 'Problème de chauffage',
          description: 'Le radiateur du salon ne chauffe plus depuis hier. La température dans l\'appartement est devenue inconfortable.',
          priority: 'high',
          status: 'in_progress',
          category: 'heating',
          property: {
            id: 'P002',
            address: '42 Avenue des Champs',
            unit: 'Apt 2A'
          },
          tenant: {
            id: 'T002',
            name: 'Jean Dubois',
            phone: '06 98 76 54 32'
          },
          contractor: {
            id: 'C001',
            name: 'Chauffage Pro',
            specialty: 'Chauffage'
          },
          estimatedCost: 200,
          actualCost: 180,
          createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
          photos: []
        },
        {
          id: 'MR003',
          title: 'Réparation électrique',
          description: 'Plusieurs prises électriques ne fonctionnent plus dans la cuisine. Problème potentiel avec le tableau électrique.',
          priority: 'high',
          status: 'scheduled',
          category: 'electrical',
          property: {
            id: 'P003',
            address: '8 Boulevard Saint-Michel',
            unit: 'Studio 1'
          },
          tenant: {
            id: 'T003',
            name: 'Marie Laurent',
            phone: '06 45 67 89 12'
          },
          contractor: {
            id: 'C002',
            name: 'Électricité Services',
            specialty: 'Électricité'
          },
          estimatedCost: 300,
          actualCost: null,
          createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
          photos: ['https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=400']
        },
        {
          id: 'MR004',
          title: 'Nettoyage après déménagement',
          description: 'Nettoyage complet de l\'appartement après le départ du locataire précédent. Préparation pour le nouveau locataire.',
          priority: 'medium',
          status: 'completed',
          category: 'cleaning',
          property: {
            id: 'P004',
            address: '23 Rue Victor Hugo',
            unit: 'Apt 4C'
          },
          tenant: {
            id: 'T004',
            name: 'Pierre Moreau',
            phone: '06 23 45 67 89'
          },
          contractor: {
            id: 'C003',
            name: 'Clean Services',
            specialty: 'Nettoyage'
          },
          estimatedCost: 120,
          actualCost: 120,
          createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
          photos: []
        },
        {
          id: 'MR005',
          title: 'Peinture salon',
          description: 'Rafraîchissement de la peinture du salon suite à des taches sur les murs. Couleur à définir avec le locataire.',
          priority: 'low',
          status: 'open',
          category: 'painting',
          property: {
            id: 'P005',
            address: '67 Rue de Rivoli',
            unit: 'Apt 1B'
          },
          tenant: {
            id: 'T005',
            name: 'Claire Rousseau',
            phone: '06 87 65 43 21'
          },
          contractor: null,
          estimatedCost: 250,
          actualCost: null,
          createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
          photos: ['https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=400']
        }
      ];

      // Mock properties
      const mockProperties = [
        { id: 'P001', address: '15 Rue de la Paix', unit: 'Apt 3B' },
        { id: 'P002', address: '42 Avenue des Champs', unit: 'Apt 2A' },
        { id: 'P003', address: '8 Boulevard Saint-Michel', unit: 'Studio 1' },
        { id: 'P004', address: '23 Rue Victor Hugo', unit: 'Apt 4C' },
        { id: 'P005', address: '67 Rue de Rivoli', unit: 'Apt 1B' }
      ];

      // Mock tenants
      const mockTenants = [
        { id: 'T001', name: 'Sophie Martin' },
        { id: 'T002', name: 'Jean Dubois' },
        { id: 'T003', name: 'Marie Laurent' },
        { id: 'T004', name: 'Pierre Moreau' },
        { id: 'T005', name: 'Claire Rousseau' }
      ];

      // Mock contractors
      const mockContractors = [
        { id: 'C001', name: 'Chauffage Pro', specialty: 'Chauffage' },
        { id: 'C002', name: 'Électricité Services', specialty: 'Électricité' },
        { id: 'C003', name: 'Clean Services', specialty: 'Nettoyage' },
        { id: 'C004', name: 'Plomberie Express', specialty: 'Plomberie' },
        { id: 'C005', name: 'Peinture Déco', specialty: 'Peinture' }
      ];

      // Mock stats
      const mockStats = {
        openRequests: 2,
        openRequestsChange: 15,
        inProgressRequests: 1,
        inProgressRequestsChange: -10,
        completedThisMonth: 8,
        completedThisMonthChange: 25,
        totalCost: 1250,
        totalCostChange: 8,
        urgentRequests: 1,
        highRequests: 2,
        mediumRequests: 1,
        lowRequests: 1,
        totalRequests: 5,
        avgResponseTime: {
          urgent: 2,
          high: 4,
          medium: 12,
          low: 24
        },
        activeContractors: 5,
        availableContractors: 3,
        busyContractors: 2,
        satisfactionRate: 94
      };

      setMaintenanceRequests(mockRequests);
      setProperties(mockProperties);
      setTenants(mockTenants);
      setContractors(mockContractors);
      setStats(mockStats);
      setLoading(false);
    };

    loadData();
  }, []);

  // Filter requests based on current filters
  const filteredRequests = maintenanceRequests?.filter(request => {
    if (filters?.status !== 'all' && request?.status !== filters?.status) return false;
    if (filters?.priority !== 'all' && request?.priority !== filters?.priority) return false;
    if (filters?.property !== 'all' && request?.property?.id !== filters?.property) return false;
    if (filters?.contractor !== 'all') {
      if (filters?.contractor === 'unassigned' && request?.contractor) return false;
      if (filters?.contractor !== 'unassigned' && (!request?.contractor || request?.contractor?.id !== filters?.contractor)) return false;
    }
    if (filters?.search && !request?.title?.toLowerCase()?.includes(filters?.search?.toLowerCase()) && 
        !request?.description?.toLowerCase()?.includes(filters?.search?.toLowerCase()) &&
        !request?.property?.address?.toLowerCase()?.includes(filters?.search?.toLowerCase())) return false;
    
    return true;
  });

  const handleFiltersChange = (newFilters) => {
    setFilters(newFilters);
  };

  const handleClearFilters = () => {
    setFilters({
      status: 'all',
      priority: 'all',
      property: 'all',
      contractor: 'all',
      dateFrom: '',
      dateTo: '',
      search: ''
    });
  };

  const handleStatusUpdate = (requestId) => {
    console.log('Update status for request:', requestId);
  };

  const handleAssignContractor = (requestId) => {
    console.log('Assign contractor to request:', requestId);
  };

  const handleViewDetails = (requestId) => {
    console.log('View details for request:', requestId);
  };

  const handleScheduleInspection = (requestId) => {
    console.log('Schedule inspection for request:', requestId);
  };

  const handleContactTenant = (requestId) => {
    console.log('Contact tenant for request:', requestId);
  };

  const handleBulkAction = (action, requestIds) => {
    console.log('Bulk action:', action, 'for requests:', requestIds);
  };

  const handleCreateRequest = async (requestData) => {
    console.log('Creating new request:', requestData);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const newRequest = {
      id: `MR${String(maintenanceRequests?.length + 1)?.padStart(3, '0')}`,
      ...requestData,
      status: 'open',
      contractor: null,
      actualCost: null,
      createdAt: new Date(),
      property: properties?.find(p => p?.id === requestData?.propertyId),
      tenant: tenants?.find(t => t?.id === requestData?.tenantId)
    };
    
    setMaintenanceRequests(prev => [newRequest, ...prev]);
  };

  const handleLogout = () => {
    console.log('Logout');
  };

  return (
    <div className="min-h-screen bg-background">
      <Helmet>
        <title>Suivi des Demandes de Maintenance - PropertyFlow</title>
        <meta name="description" content="Gérez efficacement toutes les demandes de maintenance de vos propriétés avec un suivi complet du processus." />
      </Helmet>
      {/* Header */}
      <AgencyHeader
        agencyName="PropertyFlow"
        userName="Marie Dubois"
        userRole="Gestionnaire"
        onLogout={handleLogout}
        notificationCount={3}
      />
      {/* Navigation */}
      <PrimaryNavigation />
      {/* Notification Center */}
      <NotificationCenter
        isOpen={showNotifications}
        onToggle={() => setShowNotifications(!showNotifications)}
      />
      {/* Quick Action Panel */}
      <QuickActionPanel
        isOpen={showQuickActions}
        onToggle={() => setShowQuickActions(!showQuickActions)}
      />
      {/* Main Content */}
      <main className="pt-32 pb-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Page Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-foreground">Suivi des Demandes de Maintenance</h1>
              <p className="text-muted-foreground mt-2">
                Gérez et suivez toutes les demandes de maintenance de vos propriétés
              </p>
            </div>
            
            <div className="flex items-center space-x-3">
              <Button
                variant="outline"
                onClick={() => setShowNotifications(!showNotifications)}
              >
                <Icon name="Bell" size={16} className="mr-2" />
                Notifications
              </Button>
              
              <Button
                variant="outline"
                onClick={() => setShowQuickActions(!showQuickActions)}
              >
                <Icon name="Zap" size={16} className="mr-2" />
                Actions rapides
              </Button>
              
              <Button onClick={() => setShowCreateModal(true)}>
                <Icon name="Plus" size={16} className="mr-2" />
                Nouvelle demande
              </Button>
            </div>
          </div>

          {/* Stats */}
          <div className="mb-8">
            <MaintenanceStats stats={stats} />
          </div>

          {/* Filters */}
          <div className="mb-6">
            <MaintenanceFilters
              filters={filters}
              onFiltersChange={handleFiltersChange}
              onClearFilters={handleClearFilters}
              properties={properties}
              contractors={contractors}
            />
          </div>

          {/* View Mode Toggle */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-2">
              <span className="text-sm text-muted-foreground">
                {filteredRequests?.length} demande(s) trouvée(s)
              </span>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button
                variant={viewMode === 'table' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('table')}
              >
                <Icon name="Table" size={16} className="mr-1" />
                Tableau
              </Button>
              <Button
                variant={viewMode === 'cards' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('cards')}
              >
                <Icon name="Grid3X3" size={16} className="mr-1" />
                Cartes
              </Button>
            </div>
          </div>

          {/* Content */}
          {viewMode === 'table' ? (
            <MaintenanceTable
              requests={filteredRequests}
              onStatusUpdate={handleStatusUpdate}
              onAssignContractor={handleAssignContractor}
              onViewDetails={handleViewDetails}
              onScheduleInspection={handleScheduleInspection}
              onBulkAction={handleBulkAction}
              loading={loading}
            />
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredRequests?.map((request) => (
                <MaintenanceRequestCard
                  key={request?.id}
                  request={request}
                  onStatusUpdate={handleStatusUpdate}
                  onAssignContractor={handleAssignContractor}
                  onViewDetails={handleViewDetails}
                  onScheduleInspection={handleScheduleInspection}
                  onContactTenant={handleContactTenant}
                />
              ))}
              
              {filteredRequests?.length === 0 && !loading && (
                <div className="col-span-full text-center py-12">
                  <Icon name="Wrench" size={48} className="text-muted-foreground mx-auto mb-4" />
                  <h3 className="font-semibold text-foreground mb-2">Aucune demande trouvée</h3>
                  <p className="text-muted-foreground">
                    Aucune demande de maintenance ne correspond à vos critères de recherche.
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      </main>
      {/* Create Request Modal */}
      <CreateRequestModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSubmit={handleCreateRequest}
        properties={properties}
        tenants={tenants}
      />
    </div>
  );
};

export default MaintenanceRequestTracking;