import React, { useState, useEffect } from 'react';
import Icon from '../AppIcon';
import Button from './Button';

const NotificationCenter = ({ 
  isOpen = false, 
  onToggle = () => {},
  className = "" 
}) => {
  const [notifications, setNotifications] = useState([]);
  const [filter, setFilter] = useState('all');
  const [unreadCount, setUnreadCount] = useState(0);

  // Mock notifications data
  useEffect(() => {
    const mockNotifications = [
      {
        id: 1,
        type: 'maintenance',
        priority: 'urgent',
        title: 'Demande de maintenance urgente',
        message: 'Fuite d\'eau signalée - Appartement 3B, 15 Rue de la Paix',
        timestamp: new Date(Date.now() - 5 * 60 * 1000),
        unread: true,
        propertyId: 'P001',
        tenantName: '<PERSON>'
      },
      {
        id: 2,
        type: 'payment',
        priority: 'high',
        title: 'Paiement en retard',
        message: 'Loyer de septembre non reçu - Échéance dépassée de 5 jours',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
        unread: true,
        propertyId: 'P002',
        tenantName: '<PERSON>',
        amount: '1,250€'
      },
      {
        id: 3,
        type: 'lease',
        priority: 'medium',
        title: 'Bail à renouveler',
        message: 'Expiration du bail dans 30 jours - Action requise',
        timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),
        unread: false,
        propertyId: 'P003',
        tenantName: 'Marie Laurent'
      },
      {
        id: 4,
        type: 'inspection',
        priority: 'low',
        title: 'Inspection programmée',
        message: 'Visite d\'état des lieux prévue demain à 14h00',
        timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
        unread: false,
        propertyId: 'P004',
        tenantName: 'Pierre Moreau'
      },
      {
        id: 5,
        type: 'document',
        priority: 'medium',
        title: 'Documents manquants',
        message: 'Attestation d\'assurance non fournie',
        timestamp: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
        unread: true,
        propertyId: 'P005',
        tenantName: 'Claire Rousseau'
      }
    ];

    setNotifications(mockNotifications);
    setUnreadCount(mockNotifications?.filter(n => n?.unread)?.length);
  }, []);

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'maintenance': return 'Wrench';
      case 'payment': return 'CreditCard';
      case 'lease': return 'FileText';
      case 'inspection': return 'Eye';
      case 'document': return 'File';
      default: return 'Bell';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'urgent': return 'text-error bg-error/10';
      case 'high': return 'text-warning bg-warning/10';
      case 'medium': return 'text-primary bg-primary/10';
      case 'low': return 'text-muted-foreground bg-muted';
      default: return 'text-muted-foreground bg-muted';
    }
  };

  const formatTimestamp = (timestamp) => {
    const now = new Date();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 60) return `${minutes} min`;
    if (hours < 24) return `${hours}h`;
    return `${days}j`;
  };

  const filteredNotifications = notifications?.filter(notification => {
    if (filter === 'all') return true;
    if (filter === 'unread') return notification?.unread;
    return notification?.type === filter;
  });

  const markAsRead = (id) => {
    setNotifications(prev => 
      prev?.map(notification => 
        notification?.id === id 
          ? { ...notification, unread: false }
          : notification
      )
    );
    setUnreadCount(prev => Math.max(0, prev - 1));
  };

  const markAllAsRead = () => {
    setNotifications(prev => 
      prev?.map(notification => ({ ...notification, unread: false }))
    );
    setUnreadCount(0);
  };

  const filterOptions = [
    { value: 'all', label: 'Toutes', icon: 'Bell' },
    { value: 'unread', label: 'Non lues', icon: 'BellRing' },
    { value: 'maintenance', label: 'Maintenance', icon: 'Wrench' },
    { value: 'payment', label: 'Paiements', icon: 'CreditCard' },
    { value: 'lease', label: 'Baux', icon: 'FileText' }
  ];

  if (!isOpen) return null;

  return (
    <div className={`fixed right-4 top-20 w-96 bg-card border border-border rounded-lg elevation-3 z-50 animate-slide-in ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <div className="flex items-center space-x-2">
          <Icon name="Bell" size={20} className="text-foreground" />
          <h3 className="font-semibold text-foreground">Notifications</h3>
          {unreadCount > 0 && (
            <span className="bg-error text-error-foreground text-xs px-2 py-1 rounded-full font-medium">
              {unreadCount}
            </span>
          )}
        </div>
        <div className="flex items-center space-x-2">
          {unreadCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={markAllAsRead}
              className="text-xs"
            >
              Tout marquer lu
            </Button>
          )}
          <Button
            variant="ghost"
            size="icon"
            onClick={onToggle}
          >
            <Icon name="X" size={16} />
          </Button>
        </div>
      </div>
      {/* Filters */}
      <div className="p-3 border-b border-border">
        <div className="flex flex-wrap gap-2">
          {filterOptions?.map((option) => (
            <Button
              key={option?.value}
              variant={filter === option?.value ? "default" : "ghost"}
              size="sm"
              onClick={() => setFilter(option?.value)}
              className="text-xs"
            >
              <Icon name={option?.icon} size={14} className="mr-1" />
              {option?.label}
            </Button>
          ))}
        </div>
      </div>
      {/* Notifications List */}
      <div className="max-h-96 overflow-y-auto">
        {filteredNotifications?.length === 0 ? (
          <div className="p-8 text-center">
            <Icon name="Bell" size={32} className="text-muted-foreground mx-auto mb-2" />
            <p className="text-muted-foreground">Aucune notification</p>
          </div>
        ) : (
          filteredNotifications?.map((notification) => (
            <div
              key={notification?.id}
              className={`p-4 border-b border-border last:border-b-0 hover:bg-muted transition-smooth cursor-pointer ${
                notification?.unread ? 'bg-accent/5' : ''
              }`}
              onClick={() => markAsRead(notification?.id)}
            >
              <div className="flex items-start space-x-3">
                <div className={`p-2 rounded-lg ${getPriorityColor(notification?.priority)}`}>
                  <Icon name={getNotificationIcon(notification?.type)} size={16} />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <p className="font-medium text-sm text-foreground truncate">
                      {notification?.title}
                    </p>
                    <span className="text-xs text-muted-foreground ml-2">
                      {formatTimestamp(notification?.timestamp)}
                    </span>
                  </div>
                  <p className="text-sm text-muted-foreground mb-2">
                    {notification?.message}
                  </p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                      <span>{notification?.tenantName}</span>
                      <span>•</span>
                      <span>{notification?.propertyId}</span>
                      {notification?.amount && (
                        <>
                          <span>•</span>
                          <span className="font-data">{notification?.amount}</span>
                        </>
                      )}
                    </div>
                    {notification?.unread && (
                      <div className="w-2 h-2 bg-accent rounded-full"></div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
      {/* Footer */}
      <div className="p-3 border-t border-border">
        <Button variant="ghost" size="sm" className="w-full">
          <Icon name="ExternalLink" size={14} className="mr-2" />
          Voir toutes les notifications
        </Button>
      </div>
    </div>
  );
};

export default NotificationCenter;