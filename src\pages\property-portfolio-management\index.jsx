import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import Icon from '../../components/AppIcon';
import Button from '../../components/ui/Button';
import Input from '../../components/ui/Input';
import AgencyHeader from '../../components/ui/AgencyHeader';
import PrimaryNavigation from '../../components/ui/PrimaryNavigation';
import PropertyFilters from './components/PropertyFilters';
import PropertyTable from './components/PropertyTable';
import PropertyCards from './components/PropertyCards';
import PropertyStats from './components/PropertyStats';
import BulkActions from './components/BulkActions';

const PropertyPortfolioManagement = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [viewMode, setViewMode] = useState('table'); // 'table' or 'cards'
  const [selectedProperties, setSelectedProperties] = useState([]);
  const [sortConfig, setSortConfig] = useState({ key: 'address', direction: 'asc' });
  const [filters, setFilters] = useState({
    search: '',
    propertyType: '',
    occupancyStatus: '',
    city: '',
    owner: '',
    minRent: '',
    maxRent: '',
    minRooms: '',
    maxRooms: '',
    hasParking: false,
    hasBalcony: false,
    furnished: false
  });

  // Mock properties data
  const [properties] = useState([
    {
      id: 'P001',
      address: '15 Rue de la Paix',
      city: 'Paris',
      postalCode: '75001',
      type: 'apartment',
      status: 'occupied',
      rooms: 3,
      surface: 75,
      monthlyRent: 2500,
      charges: 200,
      hasParking: true,
      hasBalcony: true,
      furnished: false,
      currentTenant: {
        name: 'Sophie Martin',
        leaseEnd: '2024-12-31'
      },
      lastInspection: {
        date: '2024-06-15',
        type: 'État des lieux de sortie'
      },
      owner: 'Jean Martin'
    },
    {
      id: 'P002',
      address: '42 Avenue des Champs',
      city: 'Lyon',
      postalCode: '69001',
      type: 'house',
      status: 'vacant',
      rooms: 5,
      surface: 120,
      monthlyRent: 1800,
      charges: 150,
      hasParking: true,
      hasBalcony: false,
      furnished: false,
      currentTenant: null,
      lastInspection: {
        date: '2024-08-20',
        type: 'Inspection de routine'
      },
      owner: 'Marie Dubois'
    },
    {
      id: 'P003',
      address: '8 Boulevard Saint-Michel',
      city: 'Paris',
      postalCode: '75005',
      type: 'studio',
      status: 'occupied',
      rooms: 1,
      surface: 25,
      monthlyRent: 1200,
      charges: 100,
      hasParking: false,
      hasBalcony: true,
      furnished: true,
      currentTenant: {
        name: 'Pierre Laurent',
        leaseEnd: '2025-03-31'
      },
      lastInspection: null,
      owner: 'Pierre Laurent'
    },
    {
      id: 'P004',
      address: '23 Rue Victor Hugo',
      city: 'Marseille',
      postalCode: '13001',
      type: 'duplex',
      status: 'maintenance',
      rooms: 4,
      surface: 95,
      monthlyRent: 1600,
      charges: 120,
      hasParking: true,
      hasBalcony: true,
      furnished: false,
      currentTenant: null,
      lastInspection: {
        date: '2024-07-10',
        type: 'Inspection de maintenance'
      },
      owner: 'Sophie Moreau'
    },
    {
      id: 'P005',
      address: '67 Place Bellecour',
      city: 'Lyon',
      postalCode: '69002',
      type: 'loft',
      status: 'occupied',
      rooms: 2,
      surface: 80,
      monthlyRent: 2200,
      charges: 180,
      hasParking: false,
      hasBalcony: false,
      furnished: true,
      currentTenant: {
        name: 'Claire Rousseau',
        leaseEnd: '2024-11-30'
      },
      lastInspection: {
        date: '2024-05-25',
        type: 'État des lieux d\'entrée'
      },
      owner: 'Luc Bernard'
    },
    {
      id: 'P006',
      address: '91 Cours Mirabeau',
      city: 'Toulouse',
      postalCode: '31000',
      type: 'apartment',
      status: 'reserved',
      rooms: 2,
      surface: 55,
      monthlyRent: 1400,
      charges: 110,
      hasParking: true,
      hasBalcony: true,
      furnished: false,
      currentTenant: null,
      lastInspection: {
        date: '2024-08-05',
        type: 'Pré-visite locataire'
      },
      owner: 'Jean Martin'
    }
  ]);

  const [filteredProperties, setFilteredProperties] = useState(properties);

  // Filter and search properties
  useEffect(() => {
    let filtered = properties;

    // Apply search
    if (searchQuery) {
      filtered = filtered?.filter(property =>
        property?.address?.toLowerCase()?.includes(searchQuery?.toLowerCase()) ||
        property?.city?.toLowerCase()?.includes(searchQuery?.toLowerCase()) ||
        property?.owner?.toLowerCase()?.includes(searchQuery?.toLowerCase()) ||
        (property?.currentTenant?.name || '')?.toLowerCase()?.includes(searchQuery?.toLowerCase())
      );
    }

    // Apply filters
    if (filters?.propertyType) {
      filtered = filtered?.filter(p => p?.type === filters?.propertyType);
    }
    if (filters?.occupancyStatus) {
      filtered = filtered?.filter(p => p?.status === filters?.occupancyStatus);
    }
    if (filters?.city) {
      filtered = filtered?.filter(p => p?.city?.toLowerCase() === filters?.city);
    }
    if (filters?.minRent) {
      filtered = filtered?.filter(p => p?.monthlyRent >= parseInt(filters?.minRent));
    }
    if (filters?.maxRent) {
      filtered = filtered?.filter(p => p?.monthlyRent <= parseInt(filters?.maxRent));
    }
    if (filters?.minRooms) {
      filtered = filtered?.filter(p => p?.rooms >= parseInt(filters?.minRooms));
    }
    if (filters?.maxRooms) {
      filtered = filtered?.filter(p => p?.rooms <= parseInt(filters?.maxRooms));
    }
    if (filters?.hasParking) {
      filtered = filtered?.filter(p => p?.hasParking);
    }
    if (filters?.hasBalcony) {
      filtered = filtered?.filter(p => p?.hasBalcony);
    }
    if (filters?.furnished) {
      filtered = filtered?.filter(p => p?.furnished);
    }

    setFilteredProperties(filtered);
  }, [searchQuery, filters, properties]);

  // Sort properties
  const sortedProperties = React.useMemo(() => {
    const sorted = [...filteredProperties];
    sorted?.sort((a, b) => {
      let aValue = a?.[sortConfig?.key];
      let bValue = b?.[sortConfig?.key];

      // Handle nested properties
      if (sortConfig?.key === 'lastInspection') {
        aValue = a?.lastInspection?.date || '';
        bValue = b?.lastInspection?.date || '';
      }

      if (aValue < bValue) {
        return sortConfig?.direction === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortConfig?.direction === 'asc' ? 1 : -1;
      }
      return 0;
    });
    return sorted;
  }, [filteredProperties, sortConfig]);

  const handleSort = (key) => {
    setSortConfig(prevConfig => ({
      key,
      direction: prevConfig?.key === key && prevConfig?.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const handlePropertyAction = (action, property) => {
    console.log(`Action: ${action} on property:`, property);
    // Implement specific actions here
    switch (action) {
      case 'view':
        // Navigate to property details
        break;
      case 'edit':
        // Open edit modal
        break;
      case 'tenant': navigate('/tenant-management-hub');
        break;
      case 'maintenance': navigate('/maintenance-request-tracking');
        break;
      default:
        break;
    }
  };

  const handleBulkAction = (action) => {
    console.log(`Bulk action: ${action} on properties:`, selectedProperties);
    // Implement bulk actions here
    setSelectedProperties([]);
  };

  const handleExport = () => {
    console.log('Exporting properties data...');
    // Implement export functionality
  };

  const handleAddProperty = () => {
    console.log('Opening add property form...');
    // Implement add property functionality
  };

  const handleLogout = () => {
    navigate('/agency-login');
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <AgencyHeader
        agencyName="PropertyFlow"
        userName="Marie Dubois"
        userRole="Gestionnaire"
        onLogout={handleLogout}
      />
      {/* Navigation */}
      <PrimaryNavigation />
      {/* Main Content */}
      <main className="pt-32 pb-8">
        <div className="max-w-7xl mx-auto px-6">
          {/* Page Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-2xl font-bold text-foreground mb-2">
                  Gestion du Portefeuille Immobilier
                </h1>
                <p className="text-muted-foreground">
                  Gérez et supervisez l'ensemble de vos propriétés immobilières
                </p>
              </div>
              <Button
                variant="default"
                onClick={handleAddProperty}
                iconName="Plus"
                iconPosition="left"
              >
                Ajouter une propriété
              </Button>
            </div>

            {/* Stats */}
            <PropertyStats properties={properties} className="mb-6" />
          </div>

          {/* Toolbar */}
          <div className="bg-card border border-border rounded-lg p-4 mb-6">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
              {/* Search and Filters */}
              <div className="flex items-center space-x-4 flex-1">
                <div className="relative flex-1 max-w-md">
                  <Icon name="Search" size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Rechercher par adresse, ville, propriétaire..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e?.target?.value)}
                    className="pl-10"
                  />
                </div>
                
                <Button
                  variant={showFilters ? "default" : "outline"}
                  onClick={() => setShowFilters(!showFilters)}
                  iconName="Filter"
                  iconPosition="left"
                >
                  Filtres
                  {Object.values(filters)?.filter(v => v !== '' && v !== false)?.length > 0 && (
                    <span className="ml-2 bg-primary text-primary-foreground text-xs px-2 py-1 rounded-full">
                      {Object.values(filters)?.filter(v => v !== '' && v !== false)?.length}
                    </span>
                  )}
                </Button>
              </div>

              {/* View Controls */}
              <div className="flex items-center space-x-4">
                <div className="flex items-center bg-muted rounded-lg p-1">
                  <Button
                    variant={viewMode === 'table' ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode('table')}
                  >
                    <Icon name="Table" size={16} />
                  </Button>
                  <Button
                    variant={viewMode === 'cards' ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode('cards')}
                  >
                    <Icon name="Grid3X3" size={16} />
                  </Button>
                </div>
                
                <Button
                  variant="outline"
                  onClick={handleExport}
                  iconName="Download"
                  iconPosition="left"
                >
                  Exporter
                </Button>
              </div>
            </div>
          </div>

          {/* Filters Panel */}
          {showFilters && (
            <div className="mb-6">
              <PropertyFilters
                isOpen={showFilters}
                onToggle={() => setShowFilters(!showFilters)}
                filters={filters}
                onFiltersChange={setFilters}
                onClearFilters={() => setFilters({
                  search: '',
                  propertyType: '',
                  occupancyStatus: '',
                  city: '',
                  owner: '',
                  minRent: '',
                  maxRent: '',
                  minRooms: '',
                  maxRooms: '',
                  hasParking: false,
                  hasBalcony: false,
                  furnished: false
                })}
              />
            </div>
          )}

          {/* Bulk Actions */}
          <BulkActions
            selectedCount={selectedProperties?.length}
            onAction={handleBulkAction}
            onClearSelection={() => setSelectedProperties([])}
            className="mb-6"
          />

          {/* Properties Display */}
          {viewMode === 'table' ? (
            <PropertyTable
              properties={sortedProperties}
              selectedProperties={selectedProperties}
              onSelectionChange={setSelectedProperties}
              onSort={handleSort}
              sortConfig={sortConfig}
              onPropertyAction={handlePropertyAction}
            />
          ) : (
            <PropertyCards
              properties={sortedProperties}
              selectedProperties={selectedProperties}
              onSelectionChange={setSelectedProperties}
              onPropertyAction={handlePropertyAction}
            />
          )}

          {/* Results Summary */}
          {filteredProperties?.length > 0 && (
            <div className="mt-6 text-center">
              <p className="text-sm text-muted-foreground">
                Affichage de {filteredProperties?.length} propriété{filteredProperties?.length > 1 ? 's' : ''} sur {properties?.length} au total
              </p>
            </div>
          )}
        </div>
      </main>
    </div>
  );
};

export default PropertyPortfolioManagement;