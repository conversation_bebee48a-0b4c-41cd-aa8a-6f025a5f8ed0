import React, { useState } from 'react';
import Icon from '../AppIcon';
import Button from './Button';

const AgencyHeader = ({ 
  agencyName = "PropertyFlow", 
  userName = "<PERSON>", 
  userRole = "Gestionnaire", 
  onLogout = () => {},
  notificationCount = 3 
}) => {
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);

  const notifications = [
    {
      id: 1,
      type: 'maintenance',
      title: 'Demande de maintenance urgente',
      message: 'Fuite d\'eau - Appartement 3B, Rue de la Paix',
      time: '5 min',
      unread: true
    },
    {
      id: 2,
      type: 'payment',
      title: 'Paiement en retard',
      message: '<PERSON><PERSON> de septembre - <PERSON>',
      time: '2h',
      unread: true
    },
    {
      id: 3,
      type: 'lease',
      title: 'Bail à renouveler',
      message: 'Expiration dans 30 jours - <PERSON>',
      time: '1j',
      unread: false
    }
  ];

  const handleNotificationClick = () => {
    setShowNotifications(!showNotifications);
    setShowUserMenu(false);
  };

  const handleUserMenuClick = () => {
    setShowUserMenu(!showUserMenu);
    setShowNotifications(false);
  };

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'maintenance': return 'Wrench';
      case 'payment': return 'CreditCard';
      case 'lease': return 'FileText';
      default: return 'Bell';
    }
  };

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-card border-b border-border">
      <div className="flex items-center justify-between h-16 px-6">
        {/* Logo and Agency Name */}
        <div className="flex items-center space-x-3">
          <div className="flex items-center justify-center w-8 h-8 bg-primary rounded-lg">
            <Icon name="Building2" size={20} color="white" />
          </div>
          <div>
            <h1 className="text-lg font-semibold text-foreground">{agencyName}</h1>
          </div>
        </div>

        {/* Right Side Actions */}
        <div className="flex items-center space-x-4">
          {/* Notifications */}
          <div className="relative">
            <Button
              variant="ghost"
              size="icon"
              onClick={handleNotificationClick}
              className="relative"
            >
              <Icon name="Bell" size={20} />
              {notificationCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-error text-error-foreground text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium">
                  {notificationCount}
                </span>
              )}
            </Button>

            {/* Notifications Dropdown */}
            {showNotifications && (
              <div className="absolute right-0 top-12 w-80 bg-popover border border-border rounded-lg elevation-3 animate-slide-in">
                <div className="p-4 border-b border-border">
                  <h3 className="font-medium text-popover-foreground">Notifications</h3>
                </div>
                <div className="max-h-96 overflow-y-auto">
                  {notifications?.map((notification) => (
                    <div
                      key={notification?.id}
                      className={`p-4 border-b border-border last:border-b-0 hover:bg-muted transition-smooth cursor-pointer ${
                        notification?.unread ? 'bg-accent/5' : ''
                      }`}
                    >
                      <div className="flex items-start space-x-3">
                        <div className={`p-2 rounded-lg ${
                          notification?.type === 'maintenance' ? 'bg-warning/10 text-warning' :
                          notification?.type === 'payment'? 'bg-error/10 text-error' : 'bg-primary/10 text-primary'
                        }`}>
                          <Icon name={getNotificationIcon(notification?.type)} size={16} />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <p className="font-medium text-sm text-popover-foreground truncate">
                              {notification?.title}
                            </p>
                            <span className="text-xs text-muted-foreground ml-2">
                              {notification?.time}
                            </span>
                          </div>
                          <p className="text-sm text-muted-foreground mt-1">
                            {notification?.message}
                          </p>
                          {notification?.unread && (
                            <div className="w-2 h-2 bg-accent rounded-full mt-2"></div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                <div className="p-3 border-t border-border">
                  <Button variant="ghost" size="sm" className="w-full">
                    Voir toutes les notifications
                  </Button>
                </div>
              </div>
            )}
          </div>

          {/* User Menu */}
          <div className="relative">
            <Button
              variant="ghost"
              onClick={handleUserMenuClick}
              className="flex items-center space-x-2 px-3"
            >
              <div className="w-8 h-8 bg-secondary rounded-full flex items-center justify-center">
                <span className="text-sm font-medium text-secondary-foreground">
                  {userName?.split(' ')?.map(n => n?.[0])?.join('')}
                </span>
              </div>
              <div className="hidden md:block text-left">
                <p className="text-sm font-medium text-foreground">{userName}</p>
                <p className="text-xs text-muted-foreground">{userRole}</p>
              </div>
              <Icon name="ChevronDown" size={16} className="text-muted-foreground" />
            </Button>

            {/* User Dropdown */}
            {showUserMenu && (
              <div className="absolute right-0 top-12 w-56 bg-popover border border-border rounded-lg elevation-3 animate-slide-in">
                <div className="p-3 border-b border-border">
                  <p className="font-medium text-popover-foreground">{userName}</p>
                  <p className="text-sm text-muted-foreground">{userRole}</p>
                </div>
                <div className="py-2">
                  <button className="flex items-center w-full px-3 py-2 text-sm text-popover-foreground hover:bg-muted transition-smooth">
                    <Icon name="User" size={16} className="mr-3" />
                    Mon profil
                  </button>
                  <button className="flex items-center w-full px-3 py-2 text-sm text-popover-foreground hover:bg-muted transition-smooth">
                    <Icon name="Settings" size={16} className="mr-3" />
                    Paramètres
                  </button>
                  <button className="flex items-center w-full px-3 py-2 text-sm text-popover-foreground hover:bg-muted transition-smooth">
                    <Icon name="HelpCircle" size={16} className="mr-3" />
                    Aide
                  </button>
                  <div className="border-t border-border my-2"></div>
                  <button 
                    onClick={onLogout}
                    className="flex items-center w-full px-3 py-2 text-sm text-error hover:bg-error/10 transition-smooth"
                  >
                    <Icon name="LogOut" size={16} className="mr-3" />
                    Se déconnecter
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      {/* Click outside to close dropdowns */}
      {(showNotifications || showUserMenu) && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => {
            setShowNotifications(false);
            setShowUserMenu(false);
          }}
        />
      )}
    </header>
  );
};

export default AgencyHeader;