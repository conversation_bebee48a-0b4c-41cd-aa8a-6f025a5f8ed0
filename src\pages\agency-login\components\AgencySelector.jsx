import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';

const AgencySelector = () => {
  const [selectedAgency, setSelectedAgency] = useState('');
  const [agencies, setAgencies] = useState([]);

  // Mock agencies data
  useEffect(() => {
    const mockAgencies = [
      {
        id: 'immobilier-paris-centre',
        name: 'Immobilier Paris Centre',
        subdomain: 'paris-centre',
        location: 'Paris 1er, France'
      },
      {
        id: 'agence-lyon-prestige',
        name: 'Agence Lyon Prestige',
        subdomain: 'lyon-prestige',
        location: 'Lyon, France'
      },
      {
        id: 'marseille-properties',
        name: 'Marseille Properties',
        subdomain: 'marseille-prop',
        location: 'Marseille, France'
      },
      {
        id: 'bordeaux-immobilier',
        name: 'Bordeaux Immobilier',
        subdomain: 'bordeaux-immo',
        location: 'Bordeaux, France'
      }
    ];

    setAgencies(mockAgencies);
    
    // Auto-detect agency from subdomain (mock)
    const currentSubdomain = window.location?.hostname?.split('.')?.[0];
    const detectedAgency = mockAgencies?.find(agency => agency?.subdomain === currentSubdomain);
    
    if (detectedAgency) {
      setSelectedAgency(detectedAgency?.id);
    } else {
      // Default to first agency for demo
      setSelectedAgency(mockAgencies?.[0]?.id);
    }
  }, []);

  const handleAgencyChange = (e) => {
    setSelectedAgency(e?.target?.value);
  };

  const selectedAgencyData = agencies?.find(agency => agency?.id === selectedAgency);

  return (
    <div className="w-full max-w-md mx-auto mb-6">
      <div className="bg-card border border-border rounded-lg p-4">
        <div className="flex items-center space-x-2 mb-3">
          <Icon name="Building2" size={18} className="text-primary" />
          <h3 className="font-medium text-foreground">Sélection de l'agence</h3>
        </div>
        
        <select
          value={selectedAgency}
          onChange={handleAgencyChange}
          className="w-full p-3 border border-border rounded-lg bg-input text-foreground focus:ring-2 focus:ring-ring focus:border-transparent transition-smooth"
        >
          <option value="">Choisir une agence...</option>
          {agencies?.map((agency) => (
            <option key={agency?.id} value={agency?.id}>
              {agency?.name}
            </option>
          ))}
        </select>

        {selectedAgencyData && (
          <div className="mt-3 p-3 bg-muted rounded-lg">
            <div className="flex items-center space-x-2">
              <Icon name="MapPin" size={14} className="text-muted-foreground" />
              <span className="text-sm text-muted-foreground">
                {selectedAgencyData?.location}
              </span>
            </div>
            <div className="flex items-center space-x-2 mt-1">
              <Icon name="Globe" size={14} className="text-muted-foreground" />
              <span className="text-sm text-muted-foreground font-data">
                {selectedAgencyData?.subdomain}.propertyflow.fr
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AgencySelector;