import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet';
import AgencyHeader from '../../components/ui/AgencyHeader';
import PrimaryNavigation from '../../components/ui/PrimaryNavigation';
import TenantStats from './components/TenantStats';
import TenantFilters from './components/TenantFilters';
import TenantTable from './components/TenantTable';
import TenantActionBar from './components/TenantActionBar';
import AddTenantModal from './components/AddTenantModal';

const TenantManagementHub = () => {
  const [tenants, setTenants] = useState([]);
  const [filteredTenants, setFilteredTenants] = useState([]);
  const [selectedTenants, setSelectedTenants] = useState([]);
  const [filters, setFilters] = useState({});
  const [searchTerm, setSearchTerm] = useState('');
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [stats, setStats] = useState({});
  const [properties, setProperties] = useState([]);

  // Mock data initialization
  useEffect(() => {
    const mockTenants = [
      {
        id: 'T001',
        firstName: 'Marie',
        lastName: 'Dubois',
        email: '<EMAIL>',
        phone: '+33 6 12 34 56 78',
        dateOfBirth: '1985-03-15',
        nationality: 'french',
        currentAddress: '15 Rue de la République, 75001 Paris',
        city: 'Paris',
        postalCode: '75001',
        occupation: 'Ingénieure',
        employer: 'Tech Solutions',
        monthlyIncome: 4500,
        property: {
          id: 'P001',
          address: '25 Avenue des Champs',
          city: 'Paris',
          type: 'Appartement'
        },
        leaseStatus: 'active',
        leaseStartDate: '2023-01-01',
        leaseExpiryDate: '2024-12-31',
        monthlyRent: 1800,
        securityDeposit: 3600,
        paymentStatus: 'current',
        emergencyContactName: 'Pierre Dubois',
        emergencyContactPhone: '+33 6 98 76 54 32',
        emergencyContactRelation: 'parent'
      },
      {
        id: 'T002',
        firstName: 'Jean',
        lastName: 'Martin',
        email: '<EMAIL>',
        phone: '+33 6 23 45 67 89',
        dateOfBirth: '1990-07-22',
        nationality: 'french',
        currentAddress: '8 Boulevard Saint-Germain, 75005 Paris',
        city: 'Paris',
        postalCode: '75005',
        occupation: 'Consultant',
        employer: 'Business Corp',
        monthlyIncome: 3800,
        property: {
          id: 'P002',
          address: '12 Rue de la Paix',
          city: 'Lyon',
          type: 'Studio'
        },
        leaseStatus: 'active',
        leaseStartDate: '2023-06-01',
        leaseExpiryDate: '2024-05-31',
        monthlyRent: 950,
        securityDeposit: 1900,
        paymentStatus: 'late',
        emergencyContactName: 'Sophie Martin',
        emergencyContactPhone: '+33 6 87 65 43 21',
        emergencyContactRelation: 'sibling'
      },
      {
        id: 'T003',
        firstName: 'Claire',
        lastName: 'Rousseau',
        email: '<EMAIL>',
        phone: '+33 6 34 56 78 90',
        dateOfBirth: '1988-11-08',
        nationality: 'french',
        currentAddress: '22 Place Bellecour, 69002 Lyon',
        city: 'Lyon',
        postalCode: '69002',
        occupation: 'Architecte',
        employer: 'Design Studio',
        monthlyIncome: 4200,
        property: {
          id: 'P003',
          address: '45 Cours Mirabeau',
          city: 'Marseille',
          type: 'Maison'
        },
        leaseStatus: 'expiring_soon',
        leaseStartDate: '2022-12-01',
        leaseExpiryDate: '2024-11-30',
        monthlyRent: 1650,
        securityDeposit: 3300,
        paymentStatus: 'current',
        emergencyContactName: 'Marc Rousseau',
        emergencyContactPhone: '+33 6 76 54 32 10',
        emergencyContactRelation: 'parent'
      },
      {
        id: 'T004',
        firstName: 'Thomas',
        lastName: 'Leroy',
        email: '<EMAIL>',
        phone: '+33 6 45 67 89 01',
        dateOfBirth: '1992-04-18',
        nationality: 'french',
        currentAddress: '7 Rue Victor Hugo, 31000 Toulouse',
        city: 'Toulouse',
        postalCode: '31000',
        occupation: 'Développeur',
        employer: 'StartupTech',
        monthlyIncome: 3500,
        property: {
          id: 'P004',
          address: '18 Place du Capitole',
          city: 'Toulouse',
          type: 'Appartement'
        },
        leaseStatus: 'active',
        leaseStartDate: '2023-09-01',
        leaseExpiryDate: '2024-08-31',
        monthlyRent: 1200,
        securityDeposit: 2400,
        paymentStatus: 'partial',
        emergencyContactName: 'Anne Leroy',
        emergencyContactPhone: '+33 6 65 43 21 09',
        emergencyContactRelation: 'parent'
      },
      {
        id: 'T005',
        firstName: 'Sophie',
        lastName: 'Laurent',
        email: '<EMAIL>',
        phone: '+33 6 56 78 90 12',
        dateOfBirth: '1987-09-25',
        nationality: 'french',
        currentAddress: '33 Promenade des Anglais, 06000 Nice',
        city: 'Nice',
        postalCode: '06000',
        occupation: 'Médecin',
        employer: 'Hôpital Central',
        monthlyIncome: 5200,
        property: {
          id: 'P005',
          address: '9 Avenue de la Liberté',
          city: 'Nice',
          type: 'Appartement'
        },
        leaseStatus: 'expired',
        leaseStartDate: '2022-01-01',
        leaseExpiryDate: '2023-12-31',
        monthlyRent: 2100,
        securityDeposit: 4200,
        paymentStatus: 'current',
        emergencyContactName: 'Paul Laurent',
        emergencyContactPhone: '+33 6 54 32 10 98',
        emergencyContactRelation: 'parent'
      }
    ];

    const mockProperties = [
      {
        id: 'P001',
        address: '25 Avenue des Champs',
        city: 'Paris',
        type: 'Appartement',
        rent: 1800
      },
      {
        id: 'P002',
        address: '12 Rue de la Paix',
        city: 'Lyon',
        type: 'Studio',
        rent: 950
      },
      {
        id: 'P003',
        address: '45 Cours Mirabeau',
        city: 'Marseille',
        type: 'Maison',
        rent: 1650
      },
      {
        id: 'P004',
        address: '18 Place du Capitole',
        city: 'Toulouse',
        type: 'Appartement',
        rent: 1200
      },
      {
        id: 'P005',
        address: '9 Avenue de la Liberté',
        city: 'Nice',
        type: 'Appartement',
        rent: 2100
      },
      {
        id: 'P006',
        address: '14 Rue de Rivoli',
        city: 'Paris',
        type: 'Studio',
        rent: 1100
      }
    ];

    const mockStats = {
      totalTenants: mockTenants?.length,
      activeLeases: mockTenants?.filter(t => t?.leaseStatus === 'active')?.length,
      expiringLeases: mockTenants?.filter(t => t?.leaseStatus === 'expiring_soon')?.length,
      latePayments: mockTenants?.filter(t => t?.paymentStatus === 'late')?.length,
      totalRentCollected: mockTenants?.reduce((sum, t) => sum + t?.monthlyRent, 0),
      occupancyRate: 85.5
    };

    setTenants(mockTenants);
    setFilteredTenants(mockTenants);
    setProperties(mockProperties);
    setStats(mockStats);
  }, []);

  // Filter and search logic
  useEffect(() => {
    let filtered = [...tenants];

    // Apply search filter
    if (searchTerm) {
      const searchLower = searchTerm?.toLowerCase();
      filtered = filtered?.filter(tenant =>
        tenant?.firstName?.toLowerCase()?.includes(searchLower) ||
        tenant?.lastName?.toLowerCase()?.includes(searchLower) ||
        tenant?.email?.toLowerCase()?.includes(searchLower) ||
        tenant?.property?.address?.toLowerCase()?.includes(searchLower) ||
        tenant?.property?.city?.toLowerCase()?.includes(searchLower)
      );
    }

    // Apply filters
    Object.entries(filters)?.forEach(([key, value]) => {
      if (value && value !== 'all' && value !== '') {
        switch (key) {
          case 'leaseStatus':
            filtered = filtered?.filter(tenant => tenant?.leaseStatus === value);
            break;
          case 'paymentStatus':
            filtered = filtered?.filter(tenant => tenant?.paymentStatus === value);
            break;
          case 'propertyType':
            filtered = filtered?.filter(tenant => tenant?.property?.type?.toLowerCase() === value);
            break;
          case 'city':
            filtered = filtered?.filter(tenant => tenant?.property?.city?.toLowerCase() === value);
            break;
          case 'minRent':
            filtered = filtered?.filter(tenant => tenant?.monthlyRent >= parseInt(value));
            break;
          case 'maxRent':
            filtered = filtered?.filter(tenant => tenant?.monthlyRent <= parseInt(value));
            break;
          case 'leaseExpiresAfter':
            filtered = filtered?.filter(tenant => 
              new Date(tenant.leaseExpiryDate) >= new Date(value)
            );
            break;
          default:
            break;
        }
      }
    });

    setFilteredTenants(filtered);
  }, [tenants, filters, searchTerm]);

  const handleFilterChange = (filterType, value) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: value
    }));
  };

  const handleSearchChange = (term) => {
    setSearchTerm(term);
  };

  const handleClearFilters = () => {
    setFilters({});
    setSearchTerm('');
  };

  const handleTenantSelect = (tenantId, action) => {
    console.log(`Action ${action} for tenant ${tenantId}`);
    if (action === 'add') {
      setIsAddModalOpen(true);
    }
    // Handle other actions like view, edit, message
  };

  const handleBulkAction = (action) => {
    console.log(`Bulk action ${action} for tenants:`, selectedTenants);
    // Handle bulk actions
  };

  const handleSelectionChange = (selectedIds) => {
    setSelectedTenants(selectedIds);
  };

  const handleAddTenant = () => {
    setIsAddModalOpen(true);
  };

  const handleExport = (format) => {
    console.log(`Exporting data in ${format} format`);
    // Handle export functionality
  };

  const handleAddTenantSubmit = (tenantData) => {
    const newTenant = {
      ...tenantData,
      id: `T${String(tenants?.length + 1)?.padStart(3, '0')}`,
      property: properties?.find(p => p?.id === tenantData?.propertyId) || {},
      leaseStatus: 'pending',
      paymentStatus: 'pending'
    };

    setTenants(prev => [...prev, newTenant]);
    console.log('New tenant added:', newTenant);
  };

  const handleLogout = () => {
    console.log('User logged out');
    // Handle logout logic
  };

  return (
    <div className="min-h-screen bg-background">
      <Helmet>
        <title>Gestion des Locataires - PropertyFlow</title>
        <meta name="description" content="Gérez efficacement vos locataires, baux et communications avec PropertyFlow" />
      </Helmet>
      {/* Header */}
      <AgencyHeader
        agencyName="PropertyFlow"
        userName="Marie Dubois"
        userRole="Gestionnaire"
        onLogout={handleLogout}
        notificationCount={3}
      />
      {/* Navigation */}
      <PrimaryNavigation />
      {/* Main Content */}
      <main className="pt-32 pb-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Page Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-foreground mb-2">
              Gestion des Locataires
            </h1>
            <p className="text-muted-foreground">
              Gérez vos locataires, baux et communications en un seul endroit
            </p>
          </div>

          {/* Stats */}
          <TenantStats stats={stats} className="mb-8" />

          {/* Filters */}
          <TenantFilters
            onFilterChange={handleFilterChange}
            onSearchChange={handleSearchChange}
            onClearFilters={handleClearFilters}
            activeFilters={filters}
            className="mb-6"
          />

          {/* Action Bar */}
          <TenantActionBar
            onAddTenant={handleAddTenant}
            onExport={handleExport}
            onBulkAction={handleBulkAction}
            selectedCount={selectedTenants?.length}
            totalCount={filteredTenants?.length}
            className="mb-6"
          />

          {/* Tenant Table */}
          <TenantTable
            tenants={filteredTenants}
            onTenantSelect={handleTenantSelect}
            onBulkAction={handleBulkAction}
            selectedTenants={selectedTenants}
            onSelectionChange={handleSelectionChange}
          />
        </div>
      </main>
      {/* Add Tenant Modal */}
      <AddTenantModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onSubmit={handleAddTenantSubmit}
        properties={properties}
      />
    </div>
  );
};

export default TenantManagementHub;