import React from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import { Checkbox } from '../../../components/ui/Checkbox';

const InspectionTable = ({
  inspections,
  selectedInspections,
  onSelectionChange,
  onSort,
  sortConfig,
  onInspectionAction
}) => {
  const handleSelectAll = (checked) => {
    if (checked) {
      onSelectionChange(inspections?.map(i => i?.id));
    } else {
      onSelectionChange([]);
    }
  };

  const handleSelectInspection = (inspectionId, checked) => {
    if (checked) {
      onSelectionChange([...selectedInspections, inspectionId]);
    } else {
      onSelectionChange(selectedInspections?.filter(id => id !== inspectionId));
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      scheduled: { label: 'Planifiée', color: 'bg-blue-100 text-blue-800 border-blue-200' },
      'in-progress': { label: 'En cours', color: 'bg-purple-100 text-purple-800 border-purple-200' },
      completed: { label: 'Terminée', color: 'bg-green-100 text-green-800 border-green-200' },
      overdue: { label: 'En retard', color: 'bg-red-100 text-red-800 border-red-200' },
      cancelled: { label: 'Annulée', color: 'bg-gray-100 text-gray-800 border-gray-200' }
    };

    const config = statusConfig?.[status] || statusConfig?.scheduled;
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${config?.color}`}>
        {config?.label}
      </span>
    );
  };

  const getTypeBadge = (type) => {
    const typeConfig = {
      entry: { label: 'Entrée', color: 'bg-emerald-100 text-emerald-800' },
      exit: { label: 'Sortie', color: 'bg-orange-100 text-orange-800' },
      periodic: { label: 'Périodique', color: 'bg-blue-100 text-blue-800' },
      maintenance: { label: 'Maintenance', color: 'bg-purple-100 text-purple-800' }
    };

    const config = typeConfig?.[type] || typeConfig?.periodic;
    
    return (
      <span className={`inline-flex items-center px-2 py-1 rounded text-xs font-medium ${config?.color}`}>
        {config?.label}
      </span>
    );
  };

  const getPriorityIcon = (priority) => {
    const priorityConfig = {
      high: { icon: 'AlertTriangle', color: 'text-red-500' },
      medium: { icon: 'AlertCircle', color: 'text-yellow-500' },
      low: { icon: 'Minus', color: 'text-green-500' }
    };

    const config = priorityConfig?.[priority] || priorityConfig?.medium;
    
    return <Icon name={config?.icon} size={16} className={config?.color} />;
  };

  const getSortIcon = (key) => {
    if (sortConfig?.key !== key) {
      return <Icon name="ArrowUpDown" size={16} className="text-muted-foreground" />;
    }
    return sortConfig?.direction === 'asc' 
      ? <Icon name="ArrowUp" size={16} className="text-foreground" />
      : <Icon name="ArrowDown" size={16} className="text-foreground" />;
  };

  const formatDate = (dateString) => {
    return new Date(dateString)?.toLocaleDateString('fr-FR');
  };

  return (
    <div className="bg-card border border-border rounded-lg overflow-hidden">
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-muted/50">
            <tr>
              <th className="px-4 py-3 text-left">
                <Checkbox
                  checked={selectedInspections?.length === inspections?.length && inspections?.length > 0}
                  indeterminate={selectedInspections?.length > 0 && selectedInspections?.length < inspections?.length}
                  onCheckedChange={handleSelectAll}
                />
              </th>
              <th
                className="px-4 py-3 text-left text-sm font-semibold text-foreground cursor-pointer hover:bg-muted"
                onClick={() => onSort('property')}
              >
                <div className="flex items-center space-x-2">
                  <span>Propriété</span>
                  {getSortIcon('property')}
                </div>
              </th>
              <th
                className="px-4 py-3 text-left text-sm font-semibold text-foreground cursor-pointer hover:bg-muted"
                onClick={() => onSort('type')}
              >
                <div className="flex items-center space-x-2">
                  <span>Type</span>
                  {getSortIcon('type')}
                </div>
              </th>
              <th
                className="px-4 py-3 text-left text-sm font-semibold text-foreground cursor-pointer hover:bg-muted"
                onClick={() => onSort('scheduledDate')}
              >
                <div className="flex items-center space-x-2">
                  <span>Date planifiée</span>
                  {getSortIcon('scheduledDate')}
                </div>
              </th>
              <th
                className="px-4 py-3 text-left text-sm font-semibold text-foreground cursor-pointer hover:bg-muted"
                onClick={() => onSort('inspector')}
              >
                <div className="flex items-center space-x-2">
                  <span>Inspecteur</span>
                  {getSortIcon('inspector')}
                </div>
              </th>
              <th className="px-4 py-3 text-left text-sm font-semibold text-foreground">
                Statut
              </th>
              <th className="px-4 py-3 text-left text-sm font-semibold text-foreground">
                Priorité
              </th>
              <th className="px-4 py-3 text-left text-sm font-semibold text-foreground">
                Photos
              </th>
              <th className="px-4 py-3 text-right text-sm font-semibold text-foreground">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-border">
            {inspections?.map((inspection) => (
              <tr 
                key={inspection?.id}
                className={`hover:bg-muted/20 transition-colors ${
                  selectedInspections?.includes(inspection?.id) ? 'bg-primary/5' : ''
                }`}
              >
                <td className="px-4 py-4">
                  <Checkbox
                    checked={selectedInspections?.includes(inspection?.id)}
                    onCheckedChange={(checked) => handleSelectInspection(inspection?.id, checked)}
                  />
                </td>
                <td className="px-4 py-4">
                  <div>
                    <p className="text-sm font-medium text-foreground">
                      {inspection?.property?.address}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {inspection?.property?.type}
                    </p>
                  </div>
                </td>
                <td className="px-4 py-4">
                  {getTypeBadge(inspection?.type)}
                </td>
                <td className="px-4 py-4">
                  <div>
                    <p className="text-sm text-foreground">
                      {formatDate(inspection?.scheduledDate)}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {inspection?.scheduledTime}
                    </p>
                  </div>
                </td>
                <td className="px-4 py-4">
                  <div>
                    <p className="text-sm font-medium text-foreground">
                      {inspection?.inspector?.name}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {inspection?.inspector?.phone}
                    </p>
                  </div>
                </td>
                <td className="px-4 py-4">
                  {getStatusBadge(inspection?.status)}
                </td>
                <td className="px-4 py-4">
                  <div className="flex items-center">
                    {getPriorityIcon(inspection?.priority)}
                  </div>
                </td>
                <td className="px-4 py-4">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-foreground">
                      {inspection?.photos?.length || 0}
                    </span>
                    <Icon name="Image" size={16} className="text-muted-foreground" />
                  </div>
                </td>
                <td className="px-4 py-4">
                  <div className="flex items-center justify-end space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onInspectionAction('view', inspection)}
                      title="Voir les détails"
                    >
                      <Icon name="Eye" size={16} />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onInspectionAction('photos', inspection)}
                      title="Gérer les photos"
                    >
                      <Icon name="Camera" size={16} />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onInspectionAction('edit', inspection)}
                      title="Modifier l'inspection"
                    >
                      <Icon name="Edit3" size={16} />
                    </Button>
                    {inspection?.status === 'scheduled' && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onInspectionAction('complete', inspection)}
                        title="Marquer comme terminé"
                      >
                        <Icon name="CheckCircle" size={16} />
                      </Button>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onInspectionAction('report', inspection)}
                      title="Générer le rapport"
                    >
                      <Icon name="FileText" size={16} />
                    </Button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {inspections?.length === 0 && (
        <div className="text-center py-12">
          <Icon name="ClipboardList" size={48} className="text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium text-foreground mb-2">
            Aucune inspection trouvée
          </h3>
          <p className="text-muted-foreground">
            Commencez par planifier votre première inspection
          </p>
        </div>
      )}
    </div>
  );
};

export default InspectionTable;