import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import PlatformLogo from './components/PlatformLogo';
import AgencySelector from './components/AgencySelector';
import LoginForm from './components/LoginForm';
import TrustSignals from './components/TrustSignals';

const AgencyLogin = () => {
  const navigate = useNavigate();

  // Check if user is already authenticated
  useEffect(() => {
    const isAuthenticated = localStorage.getItem('isAuthenticated');
    if (isAuthenticated === 'true') {
      navigate('/agency-dashboard');
    }
  }, [navigate]);

  return (
    <div className="min-h-screen bg-background">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-accent/5"></div>
      <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23e2e8f0%22%20fill-opacity%3D%220.1%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%221%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-50"></div>

      {/* Main Content */}
      <div className="relative z-10 flex items-center justify-center min-h-screen p-4">
        <div className="w-full max-w-md">
          {/* Platform Logo & Branding */}
          <PlatformLogo />

          {/* Agency Selection */}
          <AgencySelector />

          {/* Login Form */}
          <div className="bg-card border border-border rounded-xl p-6 elevation-2 mb-6">
            <LoginForm />
          </div>

          {/* Trust Signals */}
          <TrustSignals />

          {/* Footer */}
          <div className="text-center mt-8 text-xs text-muted-foreground">
            <p>© {new Date().getFullYear()} PropertyFlow. Tous droits réservés.</p>
            <div className="flex justify-center space-x-4 mt-2">
              <button className="hover:text-foreground transition-smooth">
                Support
              </button>
              <button className="hover:text-foreground transition-smooth">
                Documentation
              </button>
              <button className="hover:text-foreground transition-smooth">
                Contact
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Optimization Notice */}
      <div className="fixed bottom-4 left-4 right-4 md:hidden">
        <div className="bg-card border border-border rounded-lg p-3 text-center elevation-1">
          <p className="text-xs text-muted-foreground">
            Pour une expérience optimale, utilisez PropertyFlow sur ordinateur
          </p>
        </div>
      </div>
    </div>
  );
};

export default AgencyLogin;