import React from 'react';
import Icon from '../../../components/AppIcon';

const TrustSignals = () => {
  const certifications = [
    {
      id: 'rgpd',
      name: 'RGPD',
      description: 'Conforme RGPD',
      icon: 'Shield',
      color: 'text-success'
    },
    {
      id: 'fnaim',
      name: 'FNAIM',
      description: 'Membre FNAIM',
      icon: 'Award',
      color: 'text-primary'
    },
    {
      id: 'ssl',
      name: 'SSL',
      description: 'Connexion sécurisée',
      icon: 'Lock',
      color: 'text-success'
    }
  ];

  const testimonials = [
    {
      id: 1,
      text: "PropertyFlow a révolutionné notre gestion locative. Interface intuitive et fonctionnalités complètes.",
      author: "<PERSON>",
      agency: "Immobilier Paris Centre",
      rating: 5
    },
    {
      id: 2,
      text: "Excellent outil pour le suivi des paiements et la communication avec les locataires.",
      author: "<PERSON><PERSON><PERSON>",
      agency: "Agence Lyon Prestige",
      rating: 5
    }
  ];

  return (
    <div className="w-full max-w-md mx-auto space-y-6">
      {/* Certifications */}
      <div className="bg-card border border-border rounded-lg p-4">
        <h4 className="font-medium text-foreground mb-3 text-center">
          Certifications & Sécurité
        </h4>
        <div className="flex justify-center space-x-6">
          {certifications?.map((cert) => (
            <div key={cert?.id} className="text-center">
              <div className={`inline-flex items-center justify-center w-10 h-10 rounded-full bg-muted ${cert?.color} mb-2`}>
                <Icon name={cert?.icon} size={20} />
              </div>
              <div className="text-xs text-muted-foreground">
                <div className="font-medium">{cert?.name}</div>
                <div>{cert?.description}</div>
              </div>
            </div>
          ))}
        </div>
      </div>
      {/* Testimonials */}
      <div className="bg-card border border-border rounded-lg p-4">
        <h4 className="font-medium text-foreground mb-3 text-center">
          Témoignages Clients
        </h4>
        <div className="space-y-4">
          {testimonials?.map((testimonial) => (
            <div key={testimonial?.id} className="text-center">
              <div className="flex justify-center mb-2">
                {[...Array(testimonial?.rating)]?.map((_, i) => (
                  <Icon key={i} name="Star" size={14} className="text-warning fill-current" />
                ))}
              </div>
              <p className="text-sm text-muted-foreground italic mb-2">
                "{testimonial?.text}"
              </p>
              <div className="text-xs text-muted-foreground">
                <div className="font-medium">{testimonial?.author}</div>
                <div>{testimonial?.agency}</div>
              </div>
            </div>
          ))}
        </div>
      </div>
      {/* Legal Notice */}
      <div className="text-center">
        <p className="text-xs text-muted-foreground">
          En vous connectant, vous acceptez nos{' '}
          <button className="text-primary hover:text-primary/80 transition-smooth">
            Conditions d'utilisation
          </button>
          {' '}et notre{' '}
          <button className="text-primary hover:text-primary/80 transition-smooth">
            Politique de confidentialité
          </button>
        </p>
      </div>
    </div>
  );
};

export default TrustSignals;