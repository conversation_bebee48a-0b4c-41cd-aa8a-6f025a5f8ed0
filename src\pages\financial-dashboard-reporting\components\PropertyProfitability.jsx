import React, { useState } from 'react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const PropertyProfitability = ({ data, title = "Rentabilité par propriété" }) => {
  const [sortBy, setSortBy] = useState('profit');
  const [viewType, setViewType] = useState('chart');

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0
    })?.format(value);
  };

  const formatPercentage = (value) => {
    return `${value?.toFixed(1)}%`;
  };

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload?.length) {
      const data = payload?.[0]?.payload;
      return (
        <div className="bg-popover border border-border rounded-lg p-3 elevation-2">
          <p className="font-medium text-popover-foreground mb-2">{label}</p>
          <div className="space-y-1">
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Revenus:</span>
              <span className="text-sm font-medium text-success">{formatCurrency(data?.revenue)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Dépenses:</span>
              <span className="text-sm font-medium text-error">{formatCurrency(data?.expenses)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Profit:</span>
              <span className="text-sm font-medium text-foreground">{formatCurrency(data?.profit)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">ROI:</span>
              <span className="text-sm font-medium text-primary">{formatPercentage(data?.roi)}</span>
            </div>
          </div>
        </div>
      );
    }
    return null;
  };

  const sortedData = [...data]?.sort((a, b) => {
    switch (sortBy) {
      case 'profit':
        return b?.profit - a?.profit;
      case 'revenue':
        return b?.revenue - a?.revenue;
      case 'roi':
        return b?.roi - a?.roi;
      case 'name':
        return a?.property?.localeCompare(b?.property);
      default:
        return 0;
    }
  });

  const sortOptions = [
    { value: 'profit', label: 'Profit', icon: 'TrendingUp' },
    { value: 'revenue', label: 'Revenus', icon: 'DollarSign' },
    { value: 'roi', label: 'ROI', icon: 'Target' },
    { value: 'name', label: 'Nom', icon: 'AlphabeticalOrder' }
  ];

  const getPerformanceColor = (roi) => {
    if (roi >= 8) return 'text-success bg-success/10';
    if (roi >= 5) return 'text-accent bg-accent/10';
    if (roi >= 2) return 'text-warning bg-warning/10';
    return 'text-error bg-error/10';
  };

  const getPerformanceLabel = (roi) => {
    if (roi >= 8) return 'Excellent';
    if (roi >= 5) return 'Bon';
    if (roi >= 2) return 'Moyen';
    return 'Faible';
  };

  return (
    <div className="bg-card border border-border rounded-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-foreground">{title}</h3>
        <div className="flex items-center space-x-2">
          {/* Sort Options */}
          <div className="flex bg-muted rounded-lg p-1">
            {sortOptions?.map((option) => (
              <button
                key={option?.value}
                onClick={() => setSortBy(option?.value)}
                className={`px-3 py-1 text-xs font-medium rounded transition-smooth flex items-center space-x-1 ${
                  sortBy === option?.value
                    ? 'bg-background text-foreground shadow-sm'
                    : 'text-muted-foreground hover:text-foreground'
                }`}
              >
                <Icon name={option?.icon} size={12} />
                <span>{option?.label}</span>
              </button>
            ))}
          </div>
          
          {/* View Toggle */}
          <div className="flex bg-muted rounded-lg p-1">
            <button
              onClick={() => setViewType('chart')}
              className={`p-2 rounded transition-smooth ${
                viewType === 'chart' ?'bg-background text-foreground shadow-sm' :'text-muted-foreground hover:text-foreground'
              }`}
            >
              <Icon name="BarChart3" size={16} />
            </button>
            <button
              onClick={() => setViewType('table')}
              className={`p-2 rounded transition-smooth ${
                viewType === 'table' ?'bg-background text-foreground shadow-sm' :'text-muted-foreground hover:text-foreground'
              }`}
            >
              <Icon name="Table" size={16} />
            </button>
          </div>
        </div>
      </div>
      {viewType === 'chart' ? (
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={sortedData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="var(--color-border)" />
              <XAxis 
                dataKey="property" 
                stroke="var(--color-muted-foreground)"
                fontSize={12}
                angle={-45}
                textAnchor="end"
                height={80}
              />
              <YAxis 
                stroke="var(--color-muted-foreground)"
                fontSize={12}
                tickFormatter={formatCurrency}
              />
              <Tooltip content={<CustomTooltip />} />
              <Bar 
                dataKey="revenue" 
                fill="var(--color-success)" 
                radius={[2, 2, 0, 0]}
                name="Revenus"
              />
              <Bar 
                dataKey="expenses" 
                fill="var(--color-error)" 
                radius={[2, 2, 0, 0]}
                name="Dépenses"
              />
              <Bar 
                dataKey="profit" 
                fill="var(--color-primary)" 
                radius={[2, 2, 0, 0]}
                name="Profit"
              />
            </BarChart>
          </ResponsiveContainer>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-border">
                <th className="text-left py-3 px-2 font-medium text-muted-foreground">Propriété</th>
                <th className="text-right py-3 px-2 font-medium text-muted-foreground">Revenus</th>
                <th className="text-right py-3 px-2 font-medium text-muted-foreground">Dépenses</th>
                <th className="text-right py-3 px-2 font-medium text-muted-foreground">Profit</th>
                <th className="text-right py-3 px-2 font-medium text-muted-foreground">ROI</th>
                <th className="text-center py-3 px-2 font-medium text-muted-foreground">Performance</th>
              </tr>
            </thead>
            <tbody>
              {sortedData?.map((item, index) => (
                <tr key={item?.id} className="border-b border-border hover:bg-muted/30 transition-smooth">
                  <td className="py-3 px-2">
                    <div>
                      <p className="font-medium text-foreground">{item?.property}</p>
                      <p className="text-sm text-muted-foreground">{item?.address}</p>
                    </div>
                  </td>
                  <td className="py-3 px-2 text-right font-medium text-success">
                    {formatCurrency(item?.revenue)}
                  </td>
                  <td className="py-3 px-2 text-right font-medium text-error">
                    {formatCurrency(item?.expenses)}
                  </td>
                  <td className="py-3 px-2 text-right font-bold text-foreground">
                    {formatCurrency(item?.profit)}
                  </td>
                  <td className="py-3 px-2 text-right font-bold text-primary">
                    {formatPercentage(item?.roi)}
                  </td>
                  <td className="py-3 px-2 text-center">
                    <span className={`text-xs px-2 py-1 rounded-full font-medium ${getPerformanceColor(item?.roi)}`}>
                      {getPerformanceLabel(item?.roi)}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
      <div className="flex items-center justify-between mt-4 pt-4 border-t border-border">
        <p className="text-sm text-muted-foreground">
          {sortedData?.length} propriété{sortedData?.length > 1 ? 's' : ''}
        </p>
        <Button variant="ghost" size="sm">
          <Icon name="Download" size={14} className="mr-2" />
          Exporter rapport
        </Button>
      </div>
    </div>
  );
};

export default PropertyProfitability;