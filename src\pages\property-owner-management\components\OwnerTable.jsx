import React, { useState, useMemo } from 'react';
import Button from '../../../components/ui/Button';
import { Checkbox } from '../../../components/ui/Checkbox';
import Icon from '../../../components/AppIcon';

const OwnerTable = ({ 
  owners = [], 
  onOwnerSelect, 
  selectedOwners = [], 
  onSelectionChange,
  onBulkAction
}) => {
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    })?.format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString)?.toLocaleDateString('fr-FR');
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      active: { label: 'Actif', className: 'bg-green-100 text-green-800 border-green-200' },
      pending: { label: 'En attente', className: 'bg-yellow-100 text-yellow-800 border-yellow-200' },
      inactive: { label: 'Inactif', className: 'bg-red-100 text-red-800 border-red-200' }
    };

    const config = statusConfig?.[status] || statusConfig?.pending;
    
    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium border ${config?.className}`}>
        {config?.label}
      </span>
    );
  };

  const handleSort = (key) => {
    let direction = 'asc';
    if (sortConfig?.key === key && sortConfig?.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  const handleSelectAll = (checked) => {
    if (checked) {
      onSelectionChange?.(owners?.map(owner => owner?.id));
    } else {
      onSelectionChange?.([]);
    }
  };

  const handleSelectOwner = (ownerId, checked) => {
    if (checked) {
      onSelectionChange?.([...selectedOwners, ownerId]);
    } else {
      onSelectionChange?.(selectedOwners?.filter(id => id !== ownerId));
    }
  };

  const sortedOwners = React.useMemo(() => {
    if (!sortConfig?.key) return owners;

    return [...owners]?.sort((a, b) => {
      const aValue = a?.[sortConfig?.key];
      const bValue = b?.[sortConfig?.key];

      if (aValue < bValue) {
        return sortConfig?.direction === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortConfig?.direction === 'asc' ? 1 : -1;
      }
      return 0;
    });
  }, [owners, sortConfig]);

  const SortHeader = ({ sortKey, children }) => (
    <th
      className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider cursor-pointer hover:bg-muted/50 transition-colors"
      onClick={() => handleSort(sortKey)}
    >
      <div className="flex items-center gap-1">
        {children}
        <Icon
          name={sortConfig?.key === sortKey && sortConfig?.direction === 'desc' ? 'ChevronDown' : 'ChevronUp'}
          size={14}
          className={`transition-opacity ${sortConfig?.key === sortKey ? 'opacity-100' : 'opacity-0'}`}
        />
      </div>
    </th>
  );

  if (owners?.length === 0) {
    return (
      <div className="bg-card rounded-lg border border-border p-12 text-center">
        <Icon name="UserX" size={48} className="mx-auto text-muted-foreground mb-4" />
        <h3 className="text-lg font-medium text-foreground mb-2">Aucun bailleur trouvé</h3>
        <p className="text-muted-foreground mb-6">
          Commencez par ajouter votre premier bailleur au système.
        </p>
        <Button iconName="Plus" onClick={() => onOwnerSelect?.(null, 'add')}>
          Ajouter un Bailleur
        </Button>
      </div>
    );
  }

  return (
    <div className="bg-card rounded-lg border border-border overflow-hidden">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-border">
          <thead className="bg-muted/50">
            <tr>
              <th className="px-6 py-3 w-12">
                <Checkbox
                  checked={selectedOwners?.length === owners?.length && owners?.length > 0}
                  indeterminate={selectedOwners?.length > 0 && selectedOwners?.length < owners?.length}
                  onChange={handleSelectAll}
                />
              </th>
              <SortHeader sortKey="firstName">Bailleur</SortHeader>
              <SortHeader sortKey="city">Localisation</SortHeader>
              <SortHeader sortKey="totalProperties">Propriétés</SortHeader>
              <SortHeader sortKey="totalPortfolioValue">Valeur Portfolio</SortHeader>
              <SortHeader sortKey="managementRate">Taux Gérance</SortHeader>
              <SortHeader sortKey="paymentPreference">Paiement</SortHeader>
              <SortHeader sortKey="status">Statut</SortHeader>
              <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-border">
            {sortedOwners?.map((owner) => (
              <tr 
                key={owner?.id} 
                className="hover:bg-muted/30 transition-colors"
              >
                <td className="px-6 py-4 whitespace-nowrap">
                  <Checkbox
                    checked={selectedOwners?.includes(owner?.id)}
                    onChange={(checked) => handleSelectOwner(owner?.id, checked)}
                  />
                </td>
                
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center mr-3">
                      <span className="text-sm font-medium text-primary">
                        {owner?.firstName?.charAt(0)}{owner?.lastName?.charAt(0)}
                      </span>
                    </div>
                    <div>
                      <div className="text-sm font-medium text-foreground">
                        {owner?.firstName} {owner?.lastName}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {owner?.email}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {owner?.profession}
                      </div>
                    </div>
                  </div>
                </td>
                
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-foreground">{owner?.city}</div>
                  <div className="text-sm text-muted-foreground">{owner?.postalCode}</div>
                </td>
                
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <Icon name="Building2" size={16} className="text-muted-foreground mr-2" />
                    <div className="text-sm font-medium text-foreground">
                      {owner?.totalProperties}
                    </div>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {owner?.propertyTypes?.join(', ')}
                  </div>
                </td>
                
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-foreground">
                    {formatCurrency(owner?.totalPortfolioValue)}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {formatCurrency(owner?.averageRent)}/mois moy.
                  </div>
                </td>
                
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <Icon name="Percent" size={14} className="text-muted-foreground mr-1" />
                    <span className="text-sm font-medium text-foreground">
                      {owner?.managementRate}%
                    </span>
                  </div>
                </td>
                
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center text-sm text-foreground">
                    <Icon 
                      name={owner?.paymentPreference === 'virement' ? 'CreditCard' : owner?.paymentPreference === 'cheque' ? 'FileText' : 'Banknote'} 
                      size={14} 
                      className="text-muted-foreground mr-2" 
                    />
                    {owner?.paymentPreference === 'virement' ? 'Virement' : 
                     owner?.paymentPreference === 'cheque' ? 'Chèque' : 'Espèces'}
                  </div>
                </td>
                
                <td className="px-6 py-4 whitespace-nowrap">
                  {getStatusBadge(owner?.status)}
                </td>
                
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      iconName="Eye"
                      onClick={() => onOwnerSelect?.(owner?.id, 'view')}
                      title="Voir détails"
                    />
                    <Button
                      variant="ghost"
                      size="sm"
                      iconName="Edit"
                      onClick={() => onOwnerSelect?.(owner?.id, 'edit')}
                      title="Modifier"
                    />
                    <Button
                      variant="ghost"
                      size="sm"
                      iconName="Mail"
                      onClick={() => onOwnerSelect?.(owner?.id, 'message')}
                      title="Contacter"
                    />
                    <Button
                      variant="ghost"
                      size="sm"
                      iconName="FileText"
                      onClick={() => onOwnerSelect?.(owner?.id, 'contract')}
                      title="Contrats"
                    />
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default OwnerTable;