import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import Select from '../../../components/ui/Select';

const BulkActions = ({ 
  selectedCount = 0, 
  onAction = () => {},
  onClearSelection = () => {},
  className = "" 
}) => {
  const [selectedAction, setSelectedAction] = useState('');
  const [showConfirmation, setShowConfirmation] = useState(false);

  const bulkActions = [
    { value: 'update-status', label: 'Mettre à jour le statut' },
    { value: 'assign-owner', label: 'Assigner un propriétaire' },
    { value: 'schedule-inspection', label: 'Programmer une inspection' },
    { value: 'generate-report', label: 'Générer un rapport' },
    { value: 'export-data', label: 'Exporter les données' },
    { value: 'bulk-edit', label: 'Modification groupée' },
    { value: 'archive', label: 'Archiver' },
    { value: 'delete', label: 'Supprimer', destructive: true }
  ];

  const handleExecuteAction = () => {
    if (!selectedAction) return;
    
    const action = bulkActions?.find(a => a?.value === selectedAction);
    if (action?.destructive) {
      setShowConfirmation(true);
    } else {
      onAction(selectedAction);
      setSelectedAction('');
    }
  };

  const handleConfirmAction = () => {
    onAction(selectedAction);
    setSelectedAction('');
    setShowConfirmation(false);
  };

  const handleCancelAction = () => {
    setShowConfirmation(false);
  };

  if (selectedCount === 0) return null;

  return (
    <>
      <div className={`bg-primary/5 border border-primary/20 rounded-lg p-4 ${className}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Icon name="CheckSquare" size={20} className="text-primary" />
              <span className="font-medium text-foreground">
                {selectedCount} propriété{selectedCount > 1 ? 's' : ''} sélectionnée{selectedCount > 1 ? 's' : ''}
              </span>
            </div>
            
            <div className="flex items-center space-x-2">
              <Select
                placeholder="Choisir une action"
                options={bulkActions}
                value={selectedAction}
                onChange={setSelectedAction}
                className="w-48"
              />
              
              <Button
                variant="default"
                onClick={handleExecuteAction}
                disabled={!selectedAction}
              >
                <Icon name="Play" size={16} className="mr-2" />
                Exécuter
              </Button>
            </div>
          </div>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={onClearSelection}
          >
            <Icon name="X" size={16} className="mr-1" />
            Annuler la sélection
          </Button>
        </div>
      </div>

      {/* Confirmation Modal */}
      {showConfirmation && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-background/80 backdrop-blur-sm">
          <div className="bg-card border border-border rounded-lg p-6 w-full max-w-md mx-4 elevation-3">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 bg-error/10 rounded-full flex items-center justify-center">
                <Icon name="AlertTriangle" size={20} className="text-error" />
              </div>
              <div>
                <h3 className="font-semibold text-foreground">Confirmer l'action</h3>
                <p className="text-sm text-muted-foreground">Cette action ne peut pas être annulée</p>
              </div>
            </div>
            
            <p className="text-sm text-foreground mb-6">
              Êtes-vous sûr de vouloir {selectedAction === 'delete' ? 'supprimer' : 'modifier'} {selectedCount} propriété{selectedCount > 1 ? 's' : ''} ?
            </p>
            
            <div className="flex items-center justify-end space-x-3">
              <Button
                variant="ghost"
                onClick={handleCancelAction}
              >
                Annuler
              </Button>
              <Button
                variant="destructive"
                onClick={handleConfirmAction}
              >
                <Icon name="Trash2" size={16} className="mr-2" />
                Confirmer
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default BulkActions;