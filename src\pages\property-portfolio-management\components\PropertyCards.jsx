import React from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import { Checkbox } from '../../../components/ui/Checkbox';
import PropertyStatusBadge from './PropertyStatusBadge';
import PropertyActions from './PropertyActions';

const PropertyCards = ({ 
  properties = [], 
  selectedProperties = [], 
  onSelectionChange = () => {},
  onPropertyAction = () => {},
  className = "" 
}) => {
  const handleSelectProperty = (propertyId, checked) => {
    if (checked) {
      onSelectionChange([...selectedProperties, propertyId]);
    } else {
      onSelectionChange(selectedProperties?.filter(id => id !== propertyId));
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    })?.format(amount);
  };

  const formatDate = (date) => {
    return new Intl.DateTimeFormat('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    })?.format(new Date(date));
  };

  const getPropertyTypeLabel = (type) => {
    const types = {
      apartment: 'Appartement',
      house: 'Maison',
      studio: 'Studio',
      duplex: 'Duplex',
      loft: 'Loft',
      commercial: 'Commercial'
    };
    return types?.[type] || type;
  };

  if (properties?.length === 0) {
    return (
      <div className="text-center py-12">
        <Icon name="Building2" size={48} className="text-muted-foreground mx-auto mb-4" />
        <h3 className="text-lg font-medium text-foreground mb-2">Aucune propriété trouvée</h3>
        <p className="text-muted-foreground mb-6">
          Aucune propriété ne correspond aux critères de recherche actuels.
        </p>
        <Button variant="outline">
          <Icon name="Plus" size={16} className="mr-2" />
          Ajouter une propriété
        </Button>
      </div>
    );
  }

  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ${className}`}>
      {properties?.map((property) => (
        <div
          key={property?.id}
          className={`bg-card border border-border rounded-lg overflow-hidden hover:elevation-2 transition-smooth group ${
            selectedProperties?.includes(property?.id) ? 'ring-2 ring-primary' : ''
          }`}
        >
          {/* Property Image */}
          <div className="relative h-48 bg-muted">
            <div className="absolute inset-0 flex items-center justify-center">
              <Icon name="Building2" size={48} className="text-muted-foreground" />
            </div>
            
            {/* Selection Checkbox */}
            <div className="absolute top-3 left-3">
              <Checkbox
                checked={selectedProperties?.includes(property?.id)}
                onChange={(e) => handleSelectProperty(property?.id, e?.target?.checked)}
                className="bg-card/90 backdrop-blur-sm"
              />
            </div>
            
            {/* Actions */}
            <div className="absolute top-3 right-3">
              <PropertyActions
                property={property}
                onAction={onPropertyAction}
                isVisible={true}
              />
            </div>
            
            {/* Status Badge */}
            <div className="absolute bottom-3 left-3">
              <PropertyStatusBadge status={property?.status} />
            </div>
          </div>

          {/* Property Details */}
          <div className="p-4">
            {/* Address and Type */}
            <div className="mb-3">
              <h3 className="font-semibold text-foreground mb-1">{property?.address}</h3>
              <p className="text-sm text-muted-foreground">{property?.city}, {property?.postalCode}</p>
              <div className="flex items-center space-x-2 mt-2">
                <span className="text-xs bg-muted text-muted-foreground px-2 py-1 rounded">
                  {getPropertyTypeLabel(property?.type)}
                </span>
                <span className="text-xs bg-muted text-muted-foreground px-2 py-1 rounded">
                  {property?.rooms} pièces
                </span>
                <span className="text-xs bg-muted text-muted-foreground px-2 py-1 rounded">
                  {property?.surface}m²
                </span>
              </div>
            </div>

            {/* Current Tenant */}
            <div className="mb-3">
              <div className="flex items-center space-x-2 mb-1">
                <Icon name="User" size={14} className="text-muted-foreground" />
                <span className="text-sm font-medium text-foreground">Locataire actuel</span>
              </div>
              {property?.currentTenant ? (
                <div>
                  <p className="text-sm text-foreground">{property?.currentTenant?.name}</p>
                  <p className="text-xs text-muted-foreground">
                    Bail jusqu'au {formatDate(property?.currentTenant?.leaseEnd)}
                  </p>
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">Aucun locataire</p>
              )}
            </div>

            {/* Financial Info */}
            <div className="mb-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-foreground">Loyer mensuel</span>
                <span className="font-data font-semibold text-foreground">
                  {formatCurrency(property?.monthlyRent)}
                </span>
              </div>
              {property?.charges && (
                <div className="flex items-center justify-between mt-1">
                  <span className="text-xs text-muted-foreground">Charges</span>
                  <span className="text-xs font-data text-muted-foreground">
                    + {formatCurrency(property?.charges)}
                  </span>
                </div>
              )}
            </div>

            {/* Last Inspection */}
            <div className="border-t border-border pt-3">
              <div className="flex items-center space-x-2 mb-1">
                <Icon name="Eye" size={14} className="text-muted-foreground" />
                <span className="text-sm font-medium text-foreground">Dernière inspection</span>
              </div>
              {property?.lastInspection ? (
                <div>
                  <p className="text-sm text-foreground">{formatDate(property?.lastInspection?.date)}</p>
                  <p className="text-xs text-muted-foreground">{property?.lastInspection?.type}</p>
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">Aucune inspection</p>
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default PropertyCards;