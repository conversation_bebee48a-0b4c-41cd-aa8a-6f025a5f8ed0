-- PropertyFlow - Schéma de base de données PostgreSQL avec PostGIS
-- Multi-tenant avec Row Level Security (RLS)

-- Extensions nécessaires
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =============================================
-- TABLES CORE - MULTI-TENANT
-- =============================================

-- Table des agences (tenants)
CREATE TABLE agences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    nom VARCHAR(255) NOT NULL,
    adresse TEXT,
    telephone VARCHAR(20),
    email VARCHAR(255) UNIQUE NOT NULL,
    siret VARCHAR(14),
    taux_gerance_defaut DECIMAL(5,2) DEFAULT 8.00,
    modes_paiement JSONB DEFAULT '["especes", "virement", "orange_money", "wave"]',
    logo_url TEXT,
    parametres JSONB DEFAULT '{}',
    statut VARCHAR(20) DEFAULT 'actif' CHECK (statut IN ('actif', 'suspendu', 'inactif')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des utilisateurs avec RBAC
CREATE TABLE utilisateurs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agence_id UUID NOT NULL REFERENCES agences(id) ON DELETE CASCADE,
    email VARCHAR(255) UNIQUE NOT NULL,
    mot_de_passe_hash VARCHAR(255) NOT NULL,
    nom VARCHAR(255) NOT NULL,
    prenom VARCHAR(255) NOT NULL,
    telephone VARCHAR(20),
    role VARCHAR(50) NOT NULL CHECK (role IN ('super_admin', 'admin_agence', 'gestionnaire', 'lecture')),
    permissions JSONB DEFAULT '{}',
    derniere_connexion TIMESTAMP WITH TIME ZONE,
    mfa_enabled BOOLEAN DEFAULT FALSE,
    mfa_secret VARCHAR(255),
    statut VARCHAR(20) DEFAULT 'actif' CHECK (statut IN ('actif', 'suspendu', 'inactif')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des bailleurs (propriétaires)
CREATE TABLE bailleurs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agence_id UUID NOT NULL REFERENCES agences(id) ON DELETE CASCADE,
    type_personne VARCHAR(20) NOT NULL CHECK (type_personne IN ('physique', 'morale')),
    nom VARCHAR(255) NOT NULL,
    prenom VARCHAR(255),
    raison_sociale VARCHAR(255),
    adresse TEXT NOT NULL,
    telephone VARCHAR(20),
    email VARCHAR(255),
    date_naissance DATE,
    lieu_naissance VARCHAR(255),
    nationalite VARCHAR(100),
    profession VARCHAR(255),
    -- Informations bancaires (chiffrées)
    iban_chiffre TEXT,
    bic VARCHAR(11),
    banque VARCHAR(255),
    -- Fiscalité
    numero_fiscal VARCHAR(50),
    regime_fiscal VARCHAR(50),
    -- Paramètres de gérance
    taux_gerance DECIMAL(5,2),
    mode_paiement_prefere VARCHAR(50),
    -- Documents
    documents JSONB DEFAULT '[]',
    notes TEXT,
    statut VARCHAR(20) DEFAULT 'actif' CHECK (statut IN ('actif', 'suspendu', 'inactif')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des prêts immobiliers
CREATE TABLE prets_immobiliers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agence_id UUID NOT NULL REFERENCES agences(id) ON DELETE CASCADE,
    bailleur_id UUID NOT NULL REFERENCES bailleurs(id) ON DELETE CASCADE,
    banque VARCHAR(255) NOT NULL,
    montant_initial DECIMAL(12,2) NOT NULL,
    montant_restant DECIMAL(12,2) NOT NULL,
    taux_interet DECIMAL(5,4) NOT NULL,
    duree_mois INTEGER NOT NULL,
    mensualite DECIMAL(10,2) NOT NULL,
    date_debut DATE NOT NULL,
    date_fin DATE NOT NULL,
    numero_pret VARCHAR(100),
    type_pret VARCHAR(50) DEFAULT 'amortissable',
    statut VARCHAR(20) DEFAULT 'actif' CHECK (statut IN ('actif', 'termine', 'suspendu')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des biens immobiliers
CREATE TABLE biens (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agence_id UUID NOT NULL REFERENCES agences(id) ON DELETE CASCADE,
    bailleur_id UUID NOT NULL REFERENCES bailleurs(id) ON DELETE CASCADE,
    reference VARCHAR(50) UNIQUE NOT NULL,
    type_bien VARCHAR(50) NOT NULL CHECK (type_bien IN ('appartement', 'maison', 'studio', 'bureau', 'commerce', 'garage', 'terrain')),
    adresse TEXT NOT NULL,
    ville VARCHAR(255) NOT NULL,
    code_postal VARCHAR(10) NOT NULL,
    pays VARCHAR(100) DEFAULT 'Sénégal',
    -- Géolocalisation
    coordonnees GEOMETRY(POINT, 4326),
    -- Caractéristiques
    surface_habitable DECIMAL(8,2),
    nombre_pieces INTEGER,
    nombre_chambres INTEGER,
    nombre_salles_bain INTEGER,
    etage INTEGER,
    ascenseur BOOLEAN DEFAULT FALSE,
    balcon BOOLEAN DEFAULT FALSE,
    parking BOOLEAN DEFAULT FALSE,
    jardin BOOLEAN DEFAULT FALSE,
    -- Financier
    loyer_mensuel DECIMAL(10,2) NOT NULL,
    charges_mensuelles DECIMAL(10,2) DEFAULT 0,
    depot_garantie DECIMAL(10,2),
    -- Équipements
    equipements JSONB DEFAULT '[]',
    -- Médias
    photos JSONB DEFAULT '[]',
    documents JSONB DEFAULT '[]',
    -- État et disponibilité
    statut VARCHAR(20) DEFAULT 'disponible' CHECK (statut IN ('disponible', 'occupe', 'travaux', 'indisponible')),
    date_disponibilite DATE,
    description TEXT,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des locataires
CREATE TABLE locataires (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agence_id UUID NOT NULL REFERENCES agences(id) ON DELETE CASCADE,
    nom VARCHAR(255) NOT NULL,
    prenom VARCHAR(255) NOT NULL,
    date_naissance DATE,
    lieu_naissance VARCHAR(255),
    nationalite VARCHAR(100),
    profession VARCHAR(255),
    employeur VARCHAR(255),
    revenus_mensuels DECIMAL(10,2),
    -- Contact
    telephone VARCHAR(20) NOT NULL,
    email VARCHAR(255),
    adresse_precedente TEXT,
    -- Documents d'identité
    type_piece_identite VARCHAR(50),
    numero_piece_identite VARCHAR(100),
    date_expiration_piece DATE,
    -- Références
    contact_urgence_nom VARCHAR(255),
    contact_urgence_telephone VARCHAR(20),
    -- Historique locatif
    ancien_proprietaire VARCHAR(255),
    ancien_proprietaire_telephone VARCHAR(20),
    motif_depart TEXT,
    -- Documents
    documents JSONB DEFAULT '[]',
    notes TEXT,
    statut VARCHAR(20) DEFAULT 'actif' CHECK (statut IN ('actif', 'ancien', 'blackliste')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des contrats de location
CREATE TABLE contrats (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agence_id UUID NOT NULL REFERENCES agences(id) ON DELETE CASCADE,
    bien_id UUID NOT NULL REFERENCES biens(id) ON DELETE CASCADE,
    locataire_id UUID NOT NULL REFERENCES locataires(id) ON DELETE CASCADE,
    numero_contrat VARCHAR(100) UNIQUE NOT NULL,
    type_contrat VARCHAR(50) DEFAULT 'habitation' CHECK (type_contrat IN ('habitation', 'commercial', 'saisonnier')),
    date_debut DATE NOT NULL,
    date_fin DATE NOT NULL,
    duree_mois INTEGER NOT NULL,
    -- Conditions financières
    loyer_mensuel DECIMAL(10,2) NOT NULL,
    charges_mensuelles DECIMAL(10,2) DEFAULT 0,
    depot_garantie DECIMAL(10,2) NOT NULL,
    frais_agence DECIMAL(10,2) DEFAULT 0,
    -- Révision du loyer
    indexation_loyer BOOLEAN DEFAULT TRUE,
    indice_reference VARCHAR(50) DEFAULT 'IRL',
    date_revision DATE,
    -- Clauses spéciales
    clauses_particulieres TEXT,
    conditions_resiliation TEXT,
    -- Documents
    document_pdf_url TEXT,
    documents_annexes JSONB DEFAULT '[]',
    -- Signatures
    date_signature_locataire TIMESTAMP WITH TIME ZONE,
    date_signature_bailleur TIMESTAMP WITH TIME ZONE,
    signature_numerique JSONB DEFAULT '{}',
    -- État
    statut VARCHAR(20) DEFAULT 'actif' CHECK (statut IN ('brouillon', 'actif', 'expire', 'resilie', 'renouvele')),
    date_resiliation DATE,
    motif_resiliation TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    -- Contrainte d'unicité : un seul contrat actif par bien
    CONSTRAINT unique_contrat_actif_bien UNIQUE (bien_id, statut) DEFERRABLE INITIALLY DEFERRED
);

-- =============================================
-- GESTION FINANCIÈRE
-- =============================================

-- Table des paiements de loyer
CREATE TABLE paiements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agence_id UUID NOT NULL REFERENCES agences(id) ON DELETE CASCADE,
    contrat_id UUID NOT NULL REFERENCES contrats(id) ON DELETE CASCADE,
    locataire_id UUID NOT NULL REFERENCES locataires(id) ON DELETE CASCADE,
    -- Période couverte
    periode_debut DATE NOT NULL,
    periode_fin DATE NOT NULL,
    mois_annee VARCHAR(7) NOT NULL, -- Format: YYYY-MM
    -- Montants
    montant_loyer DECIMAL(10,2) NOT NULL,
    montant_charges DECIMAL(10,2) DEFAULT 0,
    montant_total DECIMAL(10,2) NOT NULL,
    montant_paye DECIMAL(10,2) NOT NULL,
    -- Méthode de paiement
    mode_paiement VARCHAR(50) NOT NULL CHECK (mode_paiement IN ('especes', 'virement', 'cheque', 'orange_money', 'wave', 'carte')),
    reference_transaction VARCHAR(255),
    -- Dates
    date_echeance DATE NOT NULL,
    date_paiement TIMESTAMP WITH TIME ZONE,
    date_encaissement TIMESTAMP WITH TIME ZONE,
    -- Réconciliation
    reconcilie BOOLEAN DEFAULT FALSE,
    reference_bancaire VARCHAR(255),
    -- État
    statut VARCHAR(20) DEFAULT 'en_attente' CHECK (statut IN ('en_attente', 'paye', 'partiel', 'en_retard', 'impaye')),
    retard_jours INTEGER DEFAULT 0,
    -- Quittance
    numero_quittance VARCHAR(100),
    quittance_pdf_url TEXT,
    date_generation_quittance TIMESTAMP WITH TIME ZONE,
    -- Métadonnées
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des dépenses
CREATE TABLE depenses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agence_id UUID NOT NULL REFERENCES agences(id) ON DELETE CASCADE,
    bien_id UUID REFERENCES biens(id) ON DELETE CASCADE,
    bailleur_id UUID REFERENCES bailleurs(id) ON DELETE CASCADE,
    -- Classification
    categorie VARCHAR(100) NOT NULL,
    sous_categorie VARCHAR(100),
    type_depense VARCHAR(50) NOT NULL CHECK (type_depense IN ('travaux', 'entretien', 'assurance', 'taxe', 'frais_gestion', 'autre')),
    -- Description
    libelle VARCHAR(255) NOT NULL,
    description TEXT,
    -- Montants
    montant_ht DECIMAL(10,2),
    montant_tva DECIMAL(10,2) DEFAULT 0,
    montant_ttc DECIMAL(10,2) NOT NULL,
    -- Imputation
    imputable_bailleur BOOLEAN DEFAULT TRUE,
    imputable_locataire BOOLEAN DEFAULT FALSE,
    pourcentage_bailleur DECIMAL(5,2) DEFAULT 100.00,
    -- Fournisseur
    fournisseur VARCHAR(255),
    numero_facture VARCHAR(100),
    date_facture DATE,
    date_paiement DATE,
    mode_paiement VARCHAR(50),
    -- Documents
    justificatifs JSONB DEFAULT '[]',
    -- Validation
    valide BOOLEAN DEFAULT FALSE,
    validee_par UUID REFERENCES utilisateurs(id),
    date_validation TIMESTAMP WITH TIME ZONE,
    -- État
    statut VARCHAR(20) DEFAULT 'en_attente' CHECK (statut IN ('en_attente', 'validee', 'payee', 'refusee')),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des reversements aux bailleurs
CREATE TABLE reversements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agence_id UUID NOT NULL REFERENCES agences(id) ON DELETE CASCADE,
    bailleur_id UUID NOT NULL REFERENCES bailleurs(id) ON DELETE CASCADE,
    -- Période
    mois_annee VARCHAR(7) NOT NULL, -- Format: YYYY-MM
    date_calcul TIMESTAMP WITH TIME ZONE NOT NULL,
    -- Calculs
    total_loyers_encaisses DECIMAL(12,2) NOT NULL DEFAULT 0,
    total_charges_encaissees DECIMAL(12,2) NOT NULL DEFAULT 0,
    total_depenses_imputees DECIMAL(12,2) NOT NULL DEFAULT 0,
    frais_gestion DECIMAL(12,2) NOT NULL DEFAULT 0,
    remboursement_pret DECIMAL(12,2) NOT NULL DEFAULT 0,
    autres_deductions DECIMAL(12,2) NOT NULL DEFAULT 0,
    montant_net DECIMAL(12,2) NOT NULL,
    -- Détails des calculs
    detail_calcul JSONB NOT NULL DEFAULT '{}',
    biens_concernes JSONB NOT NULL DEFAULT '[]',
    -- Versement
    date_versement TIMESTAMP WITH TIME ZONE,
    mode_versement VARCHAR(50),
    reference_virement VARCHAR(255),
    -- Documents
    bulletin_versement_pdf_url TEXT,
    -- État
    statut VARCHAR(20) DEFAULT 'calcule' CHECK (statut IN ('calcule', 'valide', 'verse', 'annule')),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    -- Contrainte d'unicité par bailleur et mois
    UNIQUE(bailleur_id, mois_annee)
);

-- =============================================
-- ÉTATS DES LIEUX ET INSPECTIONS
-- =============================================

-- Table des états des lieux
CREATE TABLE etats_lieux (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agence_id UUID NOT NULL REFERENCES agences(id) ON DELETE CASCADE,
    contrat_id UUID NOT NULL REFERENCES contrats(id) ON DELETE CASCADE,
    bien_id UUID NOT NULL REFERENCES biens(id) ON DELETE CASCADE,
    locataire_id UUID NOT NULL REFERENCES locataires(id) ON DELETE CASCADE,
    -- Type et date
    type_etat VARCHAR(20) NOT NULL CHECK (type_etat IN ('entree', 'sortie', 'periodique')),
    date_etat DATE NOT NULL,
    heure_debut TIME,
    heure_fin TIME,
    -- Participants
    present_locataire BOOLEAN DEFAULT FALSE,
    present_bailleur BOOLEAN DEFAULT FALSE,
    present_agence BOOLEAN DEFAULT TRUE,
    representant_agence VARCHAR(255),
    -- Observations par pièce
    observations JSONB NOT NULL DEFAULT '{}',
    -- Photos
    photos JSONB DEFAULT '[]',
    -- Signatures
    signature_locataire JSONB,
    signature_bailleur JSONB,
    signature_agence JSONB,
    date_signature TIMESTAMP WITH TIME ZONE,
    -- Documents
    rapport_pdf_url TEXT,
    -- État général
    etat_general VARCHAR(50) DEFAULT 'bon',
    observations_generales TEXT,
    recommandations TEXT,
    -- Suivi
    statut VARCHAR(20) DEFAULT 'en_cours' CHECK (statut IN ('en_cours', 'termine', 'signe', 'archive')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- PÉNALITÉS ET RECOUVREMENTS
-- =============================================

-- Table des pénalités
CREATE TABLE penalites (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agence_id UUID NOT NULL REFERENCES agences(id) ON DELETE CASCADE,
    contrat_id UUID NOT NULL REFERENCES contrats(id) ON DELETE CASCADE,
    locataire_id UUID NOT NULL REFERENCES locataires(id) ON DELETE CASCADE,
    paiement_id UUID REFERENCES paiements(id) ON DELETE CASCADE,
    -- Type de pénalité
    type_penalite VARCHAR(50) NOT NULL CHECK (type_penalite IN ('retard_paiement', 'degradation', 'non_respect_contrat', 'frais_relance', 'autre')),
    motif TEXT NOT NULL,
    -- Calcul
    base_calcul DECIMAL(10,2),
    taux_penalite DECIMAL(5,2),
    montant_penalite DECIMAL(10,2) NOT NULL,
    -- Dates
    date_application DATE NOT NULL,
    date_echeance DATE,
    -- Recouvrement
    montant_recouvre DECIMAL(10,2) DEFAULT 0,
    date_recouvrement TIMESTAMP WITH TIME ZONE,
    mode_recouvrement VARCHAR(50),
    -- État
    statut VARCHAR(20) DEFAULT 'appliquee' CHECK (statut IN ('appliquee', 'en_recouvrement', 'recouvrée', 'annulee', 'prescrite')),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- NOTIFICATIONS ET COMMUNICATIONS
-- =============================================

-- Table des notifications
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agence_id UUID NOT NULL REFERENCES agences(id) ON DELETE CASCADE,
    utilisateur_id UUID REFERENCES utilisateurs(id) ON DELETE CASCADE,
    -- Contenu
    type_notification VARCHAR(50) NOT NULL,
    titre VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    -- Cibles
    destinataire_email VARCHAR(255),
    destinataire_telephone VARCHAR(20),
    -- Canaux
    canal VARCHAR(20) NOT NULL CHECK (canal IN ('email', 'sms', 'push', 'interne')),
    -- Envoi
    date_programmee TIMESTAMP WITH TIME ZONE,
    date_envoi TIMESTAMP WITH TIME ZONE,
    nombre_tentatives INTEGER DEFAULT 0,
    -- État
    statut VARCHAR(20) DEFAULT 'en_attente' CHECK (statut IN ('en_attente', 'envoye', 'echec', 'lu')),
    erreur_message TEXT,
    -- Métadonnées
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- LOGS ET AUDIT
-- =============================================

-- Table des logs d'audit
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agence_id UUID REFERENCES agences(id) ON DELETE CASCADE,
    utilisateur_id UUID REFERENCES utilisateurs(id) ON DELETE SET NULL,
    -- Action
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(100),
    record_id UUID,
    -- Données
    old_values JSONB,
    new_values JSONB,
    -- Contexte
    ip_address INET,
    user_agent TEXT,
    -- Horodatage
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- INDEXES POUR PERFORMANCE
-- =============================================

-- Indexes principaux
CREATE INDEX idx_agences_email ON agences(email);
CREATE INDEX idx_utilisateurs_agence_id ON utilisateurs(agence_id);
CREATE INDEX idx_utilisateurs_email ON utilisateurs(email);
CREATE INDEX idx_bailleurs_agence_id ON bailleurs(agence_id);
CREATE INDEX idx_biens_agence_id ON biens(agence_id);
CREATE INDEX idx_biens_bailleur_id ON biens(bailleur_id);
CREATE INDEX idx_biens_reference ON biens(reference);
CREATE INDEX idx_biens_coordonnees ON biens USING GIST(coordonnees);
CREATE INDEX idx_locataires_agence_id ON locataires(agence_id);
CREATE INDEX idx_contrats_agence_id ON contrats(agence_id);
CREATE INDEX idx_contrats_bien_id ON contrats(bien_id);
CREATE INDEX idx_contrats_locataire_id ON contrats(locataire_id);
CREATE INDEX idx_contrats_numero ON contrats(numero_contrat);
CREATE INDEX idx_paiements_agence_id ON paiements(agence_id);
CREATE INDEX idx_paiements_contrat_id ON paiements(contrat_id);
CREATE INDEX idx_paiements_mois_annee ON paiements(mois_annee);
CREATE INDEX idx_paiements_statut ON paiements(statut);
CREATE INDEX idx_depenses_agence_id ON depenses(agence_id);
CREATE INDEX idx_depenses_bien_id ON depenses(bien_id);
CREATE INDEX idx_reversements_agence_id ON reversements(agence_id);
CREATE INDEX idx_reversements_bailleur_id ON reversements(bailleur_id);
CREATE INDEX idx_reversements_mois_annee ON reversements(mois_annee);
CREATE INDEX idx_etats_lieux_agence_id ON etats_lieux(agence_id);
CREATE INDEX idx_etats_lieux_contrat_id ON etats_lieux(contrat_id);
CREATE INDEX idx_penalites_agence_id ON penalites(agence_id);
CREATE INDEX idx_penalites_contrat_id ON penalites(contrat_id);
CREATE INDEX idx_notifications_agence_id ON notifications(agence_id);
CREATE INDEX idx_notifications_utilisateur_id ON notifications(utilisateur_id);
CREATE INDEX idx_audit_logs_agence_id ON audit_logs(agence_id);
CREATE INDEX idx_audit_logs_utilisateur_id ON audit_logs(utilisateur_id);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);

-- =============================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =============================================

-- Activer RLS sur toutes les tables
ALTER TABLE agences ENABLE ROW LEVEL SECURITY;
ALTER TABLE utilisateurs ENABLE ROW LEVEL SECURITY;
ALTER TABLE bailleurs ENABLE ROW LEVEL SECURITY;
ALTER TABLE prets_immobiliers ENABLE ROW LEVEL SECURITY;
ALTER TABLE biens ENABLE ROW LEVEL SECURITY;
ALTER TABLE locataires ENABLE ROW LEVEL SECURITY;
ALTER TABLE contrats ENABLE ROW LEVEL SECURITY;
ALTER TABLE paiements ENABLE ROW LEVEL SECURITY;
ALTER TABLE depenses ENABLE ROW LEVEL SECURITY;
ALTER TABLE reversements ENABLE ROW LEVEL SECURITY;
ALTER TABLE etats_lieux ENABLE ROW LEVEL SECURITY;
ALTER TABLE penalites ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- Fonction pour obtenir l'agence_id du token JWT
CREATE OR REPLACE FUNCTION auth.get_agence_id() RETURNS UUID AS $$
BEGIN
    RETURN COALESCE(
        (current_setting('request.jwt.claims', true)::json->>'agence_id')::UUID,
        NULL
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Fonction pour obtenir le role de l'utilisateur
CREATE OR REPLACE FUNCTION auth.get_user_role() RETURNS TEXT AS $$
BEGIN
    RETURN COALESCE(
        current_setting('request.jwt.claims', true)::json->>'role',
        'anonymous'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Policies pour les agences (super_admin seulement)
CREATE POLICY "Super admin can manage all agencies" ON agences
    FOR ALL USING (auth.get_user_role() = 'super_admin');

CREATE POLICY "Users can view their own agency" ON agences
    FOR SELECT USING (id = auth.get_agence_id());

-- Policies pour les utilisateurs
CREATE POLICY "Users can view users from their agency" ON utilisateurs
    FOR SELECT USING (agence_id = auth.get_agence_id());

CREATE POLICY "Admin can manage users in their agency" ON utilisateurs
    FOR ALL USING (
        agence_id = auth.get_agence_id() AND 
        auth.get_user_role() IN ('super_admin', 'admin_agence')
    );

-- Policies génériques pour toutes les autres tables (isolation par agence_id)
CREATE POLICY "Agency isolation policy" ON bailleurs
    FOR ALL USING (agence_id = auth.get_agence_id());

CREATE POLICY "Agency isolation policy" ON prets_immobiliers
    FOR ALL USING (agence_id = auth.get_agence_id());

CREATE POLICY "Agency isolation policy" ON biens
    FOR ALL USING (agence_id = auth.get_agence_id());

CREATE POLICY "Agency isolation policy" ON locataires
    FOR ALL USING (agence_id = auth.get_agence_id());

CREATE POLICY "Agency isolation policy" ON contrats
    FOR ALL USING (agence_id = auth.get_agence_id());

CREATE POLICY "Agency isolation policy" ON paiements
    FOR ALL USING (agence_id = auth.get_agence_id());

CREATE POLICY "Agency isolation policy" ON depenses
    FOR ALL USING (agence_id = auth.get_agence_id());

CREATE POLICY "Agency isolation policy" ON reversements
    FOR ALL USING (agence_id = auth.get_agence_id());

CREATE POLICY "Agency isolation policy" ON etats_lieux
    FOR ALL USING (agence_id = auth.get_agence_id());

CREATE POLICY "Agency isolation policy" ON penalites
    FOR ALL USING (agence_id = auth.get_agence_id());

CREATE POLICY "Agency isolation policy" ON notifications
    FOR ALL USING (agence_id = auth.get_agence_id());

CREATE POLICY "Agency isolation policy" ON audit_logs
    FOR ALL USING (agence_id = auth.get_agence_id());

-- =============================================
-- TRIGGERS POUR AUDIT ET TIMESTAMPS
-- =============================================

-- Fonction pour mettre à jour updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers pour updated_at
CREATE TRIGGER update_agences_updated_at BEFORE UPDATE ON agences
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_utilisateurs_updated_at BEFORE UPDATE ON utilisateurs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_bailleurs_updated_at BEFORE UPDATE ON bailleurs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_prets_immobiliers_updated_at BEFORE UPDATE ON prets_immobiliers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_biens_updated_at BEFORE UPDATE ON biens
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_locataires_updated_at BEFORE UPDATE ON locataires
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_contrats_updated_at BEFORE UPDATE ON contrats
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_paiements_updated_at BEFORE UPDATE ON paiements
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_depenses_updated_at BEFORE UPDATE ON depenses
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_reversements_updated_at BEFORE UPDATE ON reversements
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_etats_lieux_updated_at BEFORE UPDATE ON etats_lieux
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_penalites_updated_at BEFORE UPDATE ON penalites
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_notifications_updated_at BEFORE UPDATE ON notifications
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Fonction d'audit générique
CREATE OR REPLACE FUNCTION audit_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        INSERT INTO audit_logs (
            agence_id, utilisateur_id, action, table_name, record_id, old_values, ip_address, user_agent
        ) VALUES (
            OLD.agence_id,
            COALESCE((current_setting('request.jwt.claims', true)::json->>'sub')::UUID, NULL),
            TG_OP,
            TG_TABLE_NAME,
            OLD.id,
            to_jsonb(OLD),
            COALESCE(inet_client_addr(), '0.0.0.0'::inet),
            current_setting('request.headers', true)::json->>'user-agent'
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO audit_logs (
            agence_id, utilisateur_id, action, table_name, record_id, old_values, new_values, ip_address, user_agent
        ) VALUES (
            NEW.agence_id,
            COALESCE((current_setting('request.jwt.claims', true)::json->>'sub')::UUID, NULL),
            TG_OP,
            TG_TABLE_NAME,
            NEW.id,
            to_jsonb(OLD),
            to_jsonb(NEW),
            COALESCE(inet_client_addr(), '0.0.0.0'::inet),
            current_setting('request.headers', true)::json->>'user-agent'
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO audit_logs (
            agence_id, utilisateur_id, action, table_name, record_id, new_values, ip_address, user_agent
        ) VALUES (
            NEW.agence_id,
            COALESCE((current_setting('request.jwt.claims', true)::json->>'sub')::UUID, NULL),
            TG_OP,
            TG_TABLE_NAME,
            NEW.id,
            to_jsonb(NEW),
            COALESCE(inet_client_addr(), '0.0.0.0'::inet),
            current_setting('request.headers', true)::json->>'user-agent'
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Triggers d'audit pour les tables principales
CREATE TRIGGER audit_agences_trigger
    AFTER INSERT OR UPDATE OR DELETE ON agences
    FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER audit_utilisateurs_trigger
    AFTER INSERT OR UPDATE OR DELETE ON utilisateurs
    FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER audit_bailleurs_trigger
    AFTER INSERT OR UPDATE OR DELETE ON bailleurs
    FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER audit_biens_trigger
    AFTER INSERT OR UPDATE OR DELETE ON biens
    FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER audit_contrats_trigger
    AFTER INSERT OR UPDATE OR DELETE ON contrats
    FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER audit_paiements_trigger
    AFTER INSERT OR UPDATE OR DELETE ON paiements
    FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

-- =============================================
-- FONCTIONS MÉTIER
-- =============================================

-- Fonction pour calculer les reversements mensuels
CREATE OR REPLACE FUNCTION calculate_monthly_reversement(
    p_bailleur_id UUID,
    p_mois_annee VARCHAR(7)
) RETURNS DECIMAL(12,2) AS $$
DECLARE
    v_total_loyers DECIMAL(12,2) := 0;
    v_total_charges DECIMAL(12,2) := 0;
    v_total_depenses DECIMAL(12,2) := 0;
    v_frais_gestion DECIMAL(12,2) := 0;
    v_remboursement_pret DECIMAL(12,2) := 0;
    v_taux_gerance DECIMAL(5,2);
    v_montant_net DECIMAL(12,2);
BEGIN
    -- Récupérer le taux de gérance du bailleur
    SELECT COALESCE(taux_gerance, 8.00) INTO v_taux_gerance
    FROM bailleurs WHERE id = p_bailleur_id;
    
    -- Calculer les loyers encaissés
    SELECT COALESCE(SUM(montant_paye), 0) INTO v_total_loyers
    FROM paiements p
    JOIN contrats c ON p.contrat_id = c.id
    JOIN biens b ON c.bien_id = b.id
    WHERE b.bailleur_id = p_bailleur_id
    AND p.mois_annee = p_mois_annee
    AND p.statut = 'paye';
    
    -- Calculer les dépenses imputables
    SELECT COALESCE(SUM(montant_ttc * pourcentage_bailleur / 100), 0) INTO v_total_depenses
    FROM depenses d
    WHERE d.bailleur_id = p_bailleur_id
    AND TO_CHAR(d.date_paiement, 'YYYY-MM') = p_mois_annee
    AND d.imputable_bailleur = TRUE
    AND d.statut = 'payee';
    
    -- Calculer les frais de gestion
    v_frais_gestion := v_total_loyers * v_taux_gerance / 100;
    
    -- Calculer le remboursement de prêt (si applicable)
    SELECT COALESCE(SUM(mensualite), 0) INTO v_remboursement_pret
    FROM prets_immobiliers
    WHERE bailleur_id = p_bailleur_id
    AND statut = 'actif'
    AND DATE_TRUNC('month', CURRENT_DATE) BETWEEN date_debut AND date_fin;
    
    -- Calcul final
    v_montant_net := v_total_loyers - v_frais_gestion - v_total_depenses - v_remboursement_pret;
    
    RETURN v_montant_net;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Fonction pour générer un numéro de contrat unique
CREATE OR REPLACE FUNCTION generate_contract_number(p_agence_id UUID) 
RETURNS VARCHAR(100) AS $$
DECLARE
    v_year VARCHAR(4);
    v_sequence INTEGER;
    v_contract_number VARCHAR(100);
BEGIN
    v_year := EXTRACT(YEAR FROM CURRENT_DATE)::VARCHAR;
    
    -- Obtenir le prochain numéro de séquence pour l'année
    SELECT COALESCE(MAX(
        CASE 
            WHEN numero_contrat ~ ('^CTR-' || v_year || '-[0-9]+$') 
            THEN SUBSTRING(numero_contrat FROM LENGTH('CTR-' || v_year || '-') + 1)::INTEGER
            ELSE 0
        END
    ), 0) + 1 INTO v_sequence
    FROM contrats 
    WHERE agence_id = p_agence_id;
    
    v_contract_number := 'CTR-' || v_year || '-' || LPAD(v_sequence::VARCHAR, 4, '0');
    
    RETURN v_contract_number;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Fonction pour générer un numéro de quittance unique
CREATE OR REPLACE FUNCTION generate_receipt_number(p_agence_id UUID)
RETURNS VARCHAR(100) AS $$
DECLARE
    v_year VARCHAR(4);
    v_month VARCHAR(2);
    v_sequence INTEGER;
    v_receipt_number VARCHAR(100);
BEGIN
    v_year := EXTRACT(YEAR FROM CURRENT_DATE)::VARCHAR;
    v_month := LPAD(EXTRACT(MONTH FROM CURRENT_DATE)::VARCHAR, 2, '0');
    
    -- Obtenir le prochain numéro de séquence pour le mois
    SELECT COALESCE(MAX(
        CASE 
            WHEN numero_quittance ~ ('^QUI-' || v_year || v_month || '-[0-9]+$') 
            THEN SUBSTRING(numero_quittance FROM LENGTH('QUI-' || v_year || v_month || '-') + 1)::INTEGER
            ELSE 0
        END
    ), 0) + 1 INTO v_sequence
    FROM paiements 
    WHERE agence_id = p_agence_id;
    
    v_receipt_number := 'QUI-' || v_year || v_month || '-' || LPAD(v_sequence::VARCHAR, 4, '0');
    
    RETURN v_receipt_number;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- VUES POUR REPORTING
-- =============================================

-- Vue pour le dashboard financier
CREATE OR REPLACE VIEW v_dashboard_financier AS
SELECT 
    a.id as agence_id,
    a.nom as agence_nom,
    -- Métriques du mois courant
    COALESCE(SUM(CASE WHEN p.mois_annee = TO_CHAR(CURRENT_DATE, 'YYYY-MM') AND p.statut = 'paye' THEN p.montant_paye ELSE 0 END), 0) as revenus_mois_courant,
    COALESCE(SUM(CASE WHEN TO_CHAR(d.date_paiement, 'YYYY-MM') = TO_CHAR(CURRENT_DATE, 'YYYY-MM') AND d.statut = 'payee' THEN d.montant_ttc ELSE 0 END), 0) as depenses_mois_courant,
    -- Métriques annuelles
    COALESCE(SUM(CASE WHEN EXTRACT(YEAR FROM p.created_at) = EXTRACT(YEAR FROM CURRENT_DATE) AND p.statut = 'paye' THEN p.montant_paye ELSE 0 END), 0) as revenus_annuels,
    COALESCE(SUM(CASE WHEN EXTRACT(YEAR FROM d.date_paiement) = EXTRACT(YEAR FROM CURRENT_DATE) AND d.statut = 'payee' THEN d.montant_ttc ELSE 0 END), 0) as depenses_annuelles,
    -- Statistiques des biens
    COUNT(DISTINCT b.id) as total_biens,
    COUNT(DISTINCT CASE WHEN b.statut = 'occupe' THEN b.id END) as biens_occupes,
    COUNT(DISTINCT CASE WHEN b.statut = 'disponible' THEN b.id END) as biens_disponibles,
    -- Statistiques des contrats
    COUNT(DISTINCT CASE WHEN c.statut = 'actif' THEN c.id END) as contrats_actifs,
    -- Impayés
    COALESCE(SUM(CASE WHEN p.statut IN ('en_retard', 'impaye') THEN p.montant_total - p.montant_paye ELSE 0 END), 0) as total_impayes
FROM agences a
LEFT JOIN biens b ON a.id = b.agence_id
LEFT JOIN contrats c ON b.id = c.bien_id AND c.statut = 'actif'
LEFT JOIN paiements p ON c.id = p.contrat_id
LEFT JOIN depenses d ON a.id = d.agence_id
GROUP BY a.id, a.nom;

-- Vue pour les biens avec informations complètes
CREATE OR REPLACE VIEW v_biens_complets AS
SELECT 
    b.*,
    ba.nom as bailleur_nom,
    ba.prenom as bailleur_prenom,
    ba.telephone as bailleur_telephone,
    ba.email as bailleur_email,
    c.id as contrat_actif_id,
    c.numero_contrat,
    c.date_debut as contrat_debut,
    c.date_fin as contrat_fin,
    l.nom as locataire_nom,
    l.prenom as locataire_prenom,
    l.telephone as locataire_telephone,
    l.email as locataire_email,
    -- Calcul du taux d'occupation
    CASE 
        WHEN c.id IS NOT NULL THEN 'Occupé'
        WHEN b.statut = 'disponible' THEN 'Disponible'
        ELSE INITCAP(b.statut)
    END as statut_occupation
FROM biens b
JOIN bailleurs ba ON b.bailleur_id = ba.id
LEFT JOIN contrats c ON b.id = c.bien_id AND c.statut = 'actif'
LEFT JOIN locataires l ON c.locataire_id = l.id;

-- =============================================
-- DONNÉES DE TEST (OPTIONNEL)
-- =============================================

-- Insérer une agence de test
INSERT INTO agences (nom, email, telephone, adresse, siret) VALUES 
('Agence Immobilière Dakar', '<EMAIL>', '+221 33 123 45 67', 'Avenue Léopold Sédar Senghor, Dakar', '12345678901234')
ON CONFLICT (email) DO NOTHING;

-- Insérer un utilisateur admin de test
INSERT INTO utilisateurs (agence_id, email, mot_de_passe_hash, nom, prenom, role) 
SELECT 
    a.id,
    '<EMAIL>',
    crypt('admin123', gen_salt('bf')),
    'Admin',
    'Système',
    'admin_agence'
FROM agences a 
WHERE a.email = '<EMAIL>'
ON CONFLICT (email) DO NOTHING;
