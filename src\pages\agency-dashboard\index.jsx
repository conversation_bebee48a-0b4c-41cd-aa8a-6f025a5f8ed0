import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import AgencyHeader from '../../components/ui/AgencyHeader';
import PrimaryNavigation from '../../components/ui/PrimaryNavigation';
import NotificationCenter from '../../components/ui/NotificationCenter';
import QuickActionPanel from '../../components/ui/QuickActionPanel';
import MetricCard from './components/MetricCard';
import ActivityFeed from './components/ActivityFeed';
import UpcomingTasks from './components/UpcomingTasks';
import RevenueChart from './components/RevenueChart';
import PropertyPerformance from './components/PropertyPerformance';
import QuickActions from './components/QuickActions';
import Icon from '../../components/AppIcon';
import Button from '../../components/ui/Button';

const AgencyDashboard = () => {
  const navigate = useNavigate();
  const [showNotifications, setShowNotifications] = useState(false);
  const [showQuickActions, setShowQuickActions] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());

  // Update current time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);

    return () => clearInterval(timer);
  }, []);

  const handleLogout = () => {
    navigate('/agency-login');
  };

  const toggleNotifications = () => {
    setShowNotifications(!showNotifications);
    setShowQuickActions(false);
  };

  const toggleQuickActions = () => {
    setShowQuickActions(!showQuickActions);
    setShowNotifications(false);
  };

  const formatCurrentTime = () => {
    return currentTime?.toLocaleDateString('fr-FR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const handleMetricClick = (metric) => {
    switch (metric) {
      case 'properties': navigate('/property-portfolio-management');
        break;
      case 'tenants': navigate('/tenant-management-hub');
        break;
      case 'revenue': navigate('/financial-dashboard-reporting');
        break;
      case 'maintenance': navigate('/maintenance-request-tracking');
        break;
      default:
        break;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <AgencyHeader
        agencyName="PropertyFlow"
        userName="Marie Dubois"
        userRole="Gestionnaire Principal"
        onLogout={handleLogout}
        notificationCount={5}
      />

      {/* Navigation */}
      <PrimaryNavigation />

      {/* Main Content */}
      <main className="pt-32 md:pt-28 pb-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Welcome Section */}
          <div className="mb-8">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
              <div>
                <h1 className="text-2xl font-bold text-foreground mb-2">
                  Bonjour Marie 👋
                </h1>
                <p className="text-muted-foreground">
                  {formatCurrentTime()} • Voici un aperçu de votre portefeuille
                </p>
              </div>
              <div className="flex items-center space-x-3 mt-4 sm:mt-0">
                <Button
                  variant="outline"
                  onClick={toggleNotifications}
                  className="relative"
                >
                  <Icon name="Bell" size={16} className="mr-2" />
                  Notifications
                  <span className="absolute -top-1 -right-1 bg-error text-error-foreground text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium">
                    5
                  </span>
                </Button>
                <Button
                  variant="default"
                  onClick={toggleQuickActions}
                >
                  <Icon name="Zap" size={16} className="mr-2" />
                  Actions rapides
                </Button>
              </div>
            </div>
          </div>

          {/* Key Metrics */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <MetricCard
              title="Propriétés gérées"
              value="44"
              change="+2"
              changeType="positive"
              icon="Building2"
              color="primary"
              onClick={() => handleMetricClick('properties')}
            />
            <MetricCard
              title="Taux d'occupation"
              value="87,8%"
              change="+3,2%"
              changeType="positive"
              icon="Users"
              color="success"
              onClick={() => handleMetricClick('tenants')}
            />
            <MetricCard
              title="Revenus mensuels"
              value="95 700 €"
              change="+12,5%"
              changeType="positive"
              icon="TrendingUp"
              color="accent"
              onClick={() => handleMetricClick('revenue')}
            />
            <MetricCard
              title="Demandes maintenance"
              value="7"
              change="-2"
              changeType="positive"
              icon="Wrench"
              color="warning"
              onClick={() => handleMetricClick('maintenance')}
            />
          </div>

          {/* Main Dashboard Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
            {/* Activity Feed */}
            <div className="lg:col-span-1">
              <ActivityFeed />
            </div>

            {/* Upcoming Tasks */}
            <div className="lg:col-span-1">
              <UpcomingTasks />
            </div>

            {/* Quick Actions */}
            <div className="lg:col-span-1">
              <QuickActions />
            </div>
          </div>

          {/* Charts Section */}
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
            {/* Revenue Chart */}
            <RevenueChart />

            {/* Property Performance */}
            <PropertyPerformance />
          </div>

          {/* Additional Insights */}
          <div className="mt-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-card border border-border rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-success/10 text-success rounded-lg">
                  <Icon name="CheckCircle" size={20} />
                </div>
                <span className="text-xs text-muted-foreground">Ce mois</span>
              </div>
              <p className="text-2xl font-bold text-foreground mb-1">23</p>
              <p className="text-sm text-muted-foreground">Paiements reçus</p>
            </div>

            <div className="bg-card border border-border rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-warning/10 text-warning rounded-lg">
                  <Icon name="Clock" size={20} />
                </div>
                <span className="text-xs text-muted-foreground">En retard</span>
              </div>
              <p className="text-2xl font-bold text-foreground mb-1">3</p>
              <p className="text-sm text-muted-foreground">Paiements dus</p>
            </div>

            <div className="bg-card border border-border rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-accent/10 text-accent rounded-lg">
                  <Icon name="FileText" size={20} />
                </div>
                <span className="text-xs text-muted-foreground">À renouveler</span>
              </div>
              <p className="text-2xl font-bold text-foreground mb-1">5</p>
              <p className="text-sm text-muted-foreground">Baux expirant</p>
            </div>

            <div className="bg-card border border-border rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-primary/10 text-primary rounded-lg">
                  <Icon name="Star" size={20} />
                </div>
                <span className="text-xs text-muted-foreground">Moyenne</span>
              </div>
              <p className="text-2xl font-bold text-foreground mb-1">4,8</p>
              <p className="text-sm text-muted-foreground">Satisfaction client</p>
            </div>
          </div>
        </div>
      </main>

      {/* Notification Center */}
      <NotificationCenter
        isOpen={showNotifications}
        onToggle={toggleNotifications}
      />

      {/* Quick Action Panel */}
      <QuickActionPanel
        isOpen={showQuickActions}
        onToggle={toggleQuickActions}
      />

      {/* Click outside to close panels */}
      {(showNotifications || showQuickActions) && (
        <div 
          className="fixed inset-0 z-30" 
          onClick={() => {
            setShowNotifications(false);
            setShowQuickActions(false);
          }}
        />
      )}
    </div>
  );
};

export default AgencyDashboard;