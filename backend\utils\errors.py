"""
PropertyFlow Backend - Gestionnaire d'erreurs centralisé
"""

from flask import jsonify, request
import logging
from werkzeug.exceptions import HTTPException
from marshmallow import ValidationError
from supabase import ClientError

logger = logging.getLogger(__name__)

class PropertyFlowError(Exception):
    """Exception de base pour PropertyFlow"""
    def __init__(self, message, status_code=400, payload=None):
        super().__init__()
        self.message = message
        self.status_code = status_code
        self.payload = payload

class AuthenticationError(PropertyFlowError):
    """Erreur d'authentification"""
    def __init__(self, message="Authentication required"):
        super().__init__(message, 401)

class AuthorizationError(PropertyFlowError):
    """Erreur d'autorisation"""
    def __init__(self, message="Insufficient permissions"):
        super().__init__(message, 403)

class ValidationError(PropertyFlowError):
    """Erreur de validation"""
    def __init__(self, message, errors=None):
        super().__init__(message, 422, errors)

class NotFoundError(PropertyFlowError):
    """Ressource non trouvée"""
    def __init__(self, message="Resource not found"):
        super().__init__(message, 404)

class ConflictError(PropertyFlowError):
    """Conflit de données"""
    def __init__(self, message="Data conflict"):
        super().__init__(message, 409)

class BusinessLogicError(PropertyFlowError):
    """Erreur de logique métier"""
    def __init__(self, message):
        super().__init__(message, 422)

def register_error_handlers(app):
    """Enregistrer les gestionnaires d'erreurs"""
    
    @app.errorhandler(PropertyFlowError)
    def handle_propertyflow_error(error):
        """Gestionnaire pour les erreurs PropertyFlow"""
        logger.error(f"PropertyFlow Error: {error.message}")
        response = {
            'error': True,
            'message': error.message,
            'status_code': error.status_code
        }
        if error.payload:
            response['details'] = error.payload
        return jsonify(response), error.status_code
    
    @app.errorhandler(ValidationError)
    def handle_validation_error(error):
        """Gestionnaire pour les erreurs de validation Marshmallow"""
        logger.error(f"Validation Error: {error.messages}")
        return jsonify({
            'error': True,
            'message': 'Validation failed',
            'details': error.messages,
            'status_code': 422
        }), 422
    
    @app.errorhandler(ClientError)
    def handle_supabase_error(error):
        """Gestionnaire pour les erreurs Supabase"""
        logger.error(f"Supabase Error: {error}")
        return jsonify({
            'error': True,
            'message': 'Database operation failed',
            'status_code': 500
        }), 500
    
    @app.errorhandler(HTTPException)
    def handle_http_exception(error):
        """Gestionnaire pour les erreurs HTTP standard"""
        logger.error(f"HTTP Error {error.code}: {error.description}")
        return jsonify({
            'error': True,
            'message': error.description,
            'status_code': error.code
        }), error.code
    
    @app.errorhandler(Exception)
    def handle_generic_error(error):
        """Gestionnaire pour les erreurs génériques"""
        logger.error(f"Unexpected Error: {str(error)}", exc_info=True)
        return jsonify({
            'error': True,
            'message': 'An unexpected error occurred',
            'status_code': 500
        }), 500

def log_error(error, context=None):
    """Logger une erreur avec contexte"""
    error_info = {
        'error': str(error),
        'type': type(error).__name__,
        'url': request.url if request else None,
        'method': request.method if request else None,
        'user_agent': request.headers.get('User-Agent') if request else None,
        'ip': request.remote_addr if request else None
    }
    
    if context:
        error_info['context'] = context
    
    logger.error(f"Error logged: {error_info}")
