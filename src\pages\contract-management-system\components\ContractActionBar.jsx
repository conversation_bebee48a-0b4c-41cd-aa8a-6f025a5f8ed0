import React from 'react';
import Button from '../../../components/ui/Button';
import Select from '../../../components/ui/Select';
import Icon from '../../../components/AppIcon';

const ContractActionBar = ({ 
  onCreateContract, 
  onExport, 
  onBulkAction, 
  selectedCount = 0, 
  totalCount = 0, 
  className = "" 
}) => {
  const exportOptions = [
    { value: 'excel', label: 'Export Excel' },
    { value: 'pdf', label: 'Export PDF' },
    { value: 'csv', label: 'Export CSV' },
    { value: 'legal_archive', label: 'Archive Légale' }
  ];

  const bulkActionOptions = [
    { value: 'renew', label: 'Renouveler' },
    { value: 'send_reminder', label: 'Rappel Échéance' },
    { value: 'request_signature', label: 'Demander Signature' },
    { value: 'generate_amendment', label: 'Générer Avenant' },
    { value: 'archive', label: 'Archiver' },
    { value: 'compliance_check', label: 'Vérifier Conformité' }
  ];

  const templateOptions = [
    { value: 'residential_lease', label: 'Bail Habitation' },
    { value: 'commercial_lease', label: 'Bail Commercial' },
    { value: 'management_mandate', label: 'Mandat Gestion' },
    { value: 'service_contract', label: 'Contrat Service' }
  ];

  const handleExportSelect = (format) => {
    onExport?.(format);
  };

  const handleBulkActionSelect = (action) => {
    if (selectedCount > 0) {
      onBulkAction?.(action);
    }
  };

  const handleTemplateSelect = (template) => {
    console.log(`Creating contract from template: ${template}`);
    onCreateContract?.();
  };

  return (
    <div className={`bg-card rounded-lg p-4 border border-border ${className}`}>
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        {/* Left Side - Selection Info */}
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Icon name="FileText" size={20} className="text-muted-foreground" />
            <span className="text-sm font-medium text-foreground">
              {totalCount} contrats
            </span>
            {selectedCount > 0 && (
              <span className="text-sm text-muted-foreground">
                ({selectedCount} sélectionnés)
              </span>
            )}
          </div>

          {/* Bulk Actions */}
          {selectedCount > 0 && (
            <div className="flex items-center gap-2">
              <Select
                onValueChange={handleBulkActionSelect}
                options={bulkActionOptions}
                placeholder={`Actions (${selectedCount})`}
                className="min-w-[160px]"
              />
            </div>
          )}
        </div>

        {/* Right Side - Actions */}
        <div className="flex items-center gap-3">
          {/* Export */}
          <Select
            onValueChange={handleExportSelect}
            options={exportOptions}
            placeholder="Exporter"
            className="min-w-[120px]"
          />

          {/* Templates */}
          <Select
            onValueChange={handleTemplateSelect}
            options={templateOptions}
            placeholder="Modèles"
            className="min-w-[140px]"
          />

          {/* Quick Actions */}
          <Button
            variant="outline"
            iconName="Calendar"
            onClick={() => console.log('View renewal calendar')}
          >
            Échéances
          </Button>

          <Button
            variant="outline"
            iconName="AlertTriangle"
            onClick={() => console.log('Compliance audit')}
          >
            Audit
          </Button>

          {/* Primary Action */}
          <Button
            iconName="Plus"
            onClick={onCreateContract}
            className="whitespace-nowrap"
          >
            Nouveau Contrat
          </Button>
        </div>
      </div>

      {/* Quick Stats Bar */}
      <div className="mt-4 pt-4 border-t border-border">
        <div className="grid grid-cols-2 sm:grid-cols-4 lg:grid-cols-6 gap-4">
          <div className="text-center">
            <div className="text-lg font-semibold text-foreground">92.5%</div>
            <div className="text-xs text-muted-foreground">Signature Numérique</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-foreground">8</div>
            <div className="text-xs text-muted-foreground">À Renouveler</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-foreground">99.2%</div>
            <div className="text-xs text-muted-foreground">Conformité</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-foreground">2.1j</div>
            <div className="text-xs text-muted-foreground">Délai Moyen</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-foreground">€1.8K</div>
            <div className="text-xs text-muted-foreground">Valeur Moyenne</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-foreground">24.5%</div>
            <div className="text-xs text-muted-foreground">Renouvellement Auto</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContractActionBar;