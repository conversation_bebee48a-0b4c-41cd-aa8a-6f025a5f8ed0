import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';

const MaintenanceFilters = ({ 
  filters, 
  onFiltersChange, 
  onClearFilters,
  properties = [],
  contractors = []
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const statusOptions = [
    { value: 'all', label: 'Tous les statuts', count: 0 },
    { value: 'open', label: 'Ouvert', count: 0 },
    { value: 'in_progress', label: 'En cours', count: 0 },
    { value: 'scheduled', label: 'Programmé', count: 0 },
    { value: 'completed', label: 'Terminé', count: 0 },
    { value: 'cancelled', label: 'Annulé', count: 0 }
  ];

  const priorityOptions = [
    { value: 'all', label: 'Toutes priorités', color: 'text-muted-foreground' },
    { value: 'urgent', label: 'Urgent', color: 'text-error' },
    { value: 'high', label: 'Élevé', color: 'text-warning' },
    { value: 'medium', label: 'Moyen', color: 'text-accent' },
    { value: 'low', label: 'Faible', color: 'text-muted-foreground' }
  ];

  const handleFilterChange = (key, value) => {
    onFiltersChange({
      ...filters,
      [key]: value
    });
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters?.status !== 'all') count++;
    if (filters?.priority !== 'all') count++;
    if (filters?.property !== 'all') count++;
    if (filters?.contractor !== 'all') count++;
    if (filters?.dateFrom) count++;
    if (filters?.dateTo) count++;
    if (filters?.search) count++;
    return count;
  };

  const activeFiltersCount = getActiveFiltersCount();

  return (
    <div className="bg-card border border-border rounded-lg">
      {/* Quick Filters */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-semibold text-foreground">Filtres</h3>
          <div className="flex items-center space-x-2">
            {activeFiltersCount > 0 && (
              <span className="bg-primary text-primary-foreground text-xs px-2 py-1 rounded-full font-medium">
                {activeFiltersCount}
              </span>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              <Icon name={isExpanded ? "ChevronUp" : "ChevronDown"} size={16} />
            </Button>
          </div>
        </div>

        {/* Search */}
        <div className="mb-4">
          <Input
            type="search"
            placeholder="Rechercher par titre, description, adresse..."
            value={filters?.search || ''}
            onChange={(e) => handleFilterChange('search', e?.target?.value)}
            className="w-full"
          />
        </div>

        {/* Status Filter */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2">
          {statusOptions?.map((option) => (
            <button
              key={option?.value}
              onClick={() => handleFilterChange('status', option?.value)}
              className={`p-2 rounded-lg text-sm font-medium transition-smooth ${
                filters?.status === option?.value
                  ? 'bg-primary text-primary-foreground'
                  : 'bg-muted text-muted-foreground hover:bg-muted/80'
              }`}
            >
              {option?.label}
            </button>
          ))}
        </div>
      </div>
      {/* Advanced Filters */}
      {isExpanded && (
        <div className="p-4 space-y-4">
          {/* Priority Filter */}
          <div>
            <label className="block text-sm font-medium text-foreground mb-2">Priorité</label>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
              {priorityOptions?.map((option) => (
                <button
                  key={option?.value}
                  onClick={() => handleFilterChange('priority', option?.value)}
                  className={`p-2 rounded-lg text-sm font-medium transition-smooth flex items-center space-x-2 ${
                    filters?.priority === option?.value
                      ? 'bg-primary text-primary-foreground'
                      : 'bg-muted text-muted-foreground hover:bg-muted/80'
                  }`}
                >
                  <div className={`w-2 h-2 rounded-full ${
                    option?.value === 'urgent' ? 'bg-error' :
                    option?.value === 'high' ? 'bg-warning' :
                    option?.value === 'medium' ? 'bg-accent' :
                    option?.value === 'low' ? 'bg-muted-foreground' :
                    'bg-transparent'
                  }`} />
                  <span>{option?.label}</span>
                </button>
              ))}
            </div>
          </div>

          {/* Property Filter */}
          <div>
            <label className="block text-sm font-medium text-foreground mb-2">Propriété</label>
            <select
              value={filters?.property || 'all'}
              onChange={(e) => handleFilterChange('property', e?.target?.value)}
              className="w-full p-2 border border-border rounded-lg bg-input text-foreground"
            >
              <option value="all">Toutes les propriétés</option>
              {properties?.map((property) => (
                <option key={property?.id} value={property?.id}>
                  {property?.address}
                </option>
              ))}
            </select>
          </div>

          {/* Contractor Filter */}
          <div>
            <label className="block text-sm font-medium text-foreground mb-2">Prestataire</label>
            <select
              value={filters?.contractor || 'all'}
              onChange={(e) => handleFilterChange('contractor', e?.target?.value)}
              className="w-full p-2 border border-border rounded-lg bg-input text-foreground"
            >
              <option value="all">Tous les prestataires</option>
              <option value="unassigned">Non assigné</option>
              {contractors?.map((contractor) => (
                <option key={contractor?.id} value={contractor?.id}>
                  {contractor?.name}
                </option>
              ))}
            </select>
          </div>

          {/* Date Range */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">Date de début</label>
              <Input
                type="date"
                value={filters?.dateFrom || ''}
                onChange={(e) => handleFilterChange('dateFrom', e?.target?.value)}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">Date de fin</label>
              <Input
                type="date"
                value={filters?.dateTo || ''}
                onChange={(e) => handleFilterChange('dateTo', e?.target?.value)}
              />
            </div>
          </div>

          {/* Clear Filters */}
          {activeFiltersCount > 0 && (
            <div className="pt-4 border-t border-border">
              <Button
                variant="outline"
                onClick={onClearFilters}
                className="w-full"
              >
                <Icon name="X" size={16} className="mr-2" />
                Effacer tous les filtres
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default MaintenanceFilters;