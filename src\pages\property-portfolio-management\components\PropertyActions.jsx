import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const PropertyActions = ({ 
  property, 
  onAction = () => {},
  isVisible = false,
  className = "" 
}) => {
  const [showDropdown, setShowDropdown] = useState(false);

  const actions = [
    {
      id: 'view',
      label: 'Voir les détails',
      icon: 'Eye',
      primary: true
    },
    {
      id: 'edit',
      label: 'Modifier',
      icon: 'Edit3'
    },
    {
      id: 'tenant',
      label: 'Gestion locataire',
      icon: 'Users'
    },
    {
      id: 'maintenance',
      label: 'Maintenance',
      icon: 'Wrench'
    },
    {
      id: 'documents',
      label: 'Documents',
      icon: 'FileText'
    },
    {
      id: 'inspection',
      label: 'Programmer inspection',
      icon: 'Calendar'
    }
  ];

  const handleAction = (actionId) => {
    onAction(actionId, property);
    setShowDropdown(false);
  };

  const primaryAction = actions?.find(action => action?.primary);
  const secondaryActions = actions?.filter(action => !action?.primary);

  return (
    <div className={`flex items-center space-x-1 ${className}`}>
      {/* Primary Action - Always visible on hover */}
      {isVisible && primaryAction && (
        <Button
          variant="ghost"
          size="icon"
          onClick={() => handleAction(primaryAction?.id)}
          title={primaryAction?.label}
        >
          <Icon name={primaryAction?.icon} size={16} />
        </Button>
      )}
      {/* More Actions Dropdown */}
      <div className="relative">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setShowDropdown(!showDropdown)}
          className={isVisible ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'}
        >
          <Icon name="MoreVertical" size={16} />
        </Button>

        {showDropdown && (
          <>
            <div className="absolute right-0 top-8 w-48 bg-popover border border-border rounded-lg elevation-3 z-50 animate-slide-in">
              <div className="py-2">
                {actions?.map((action) => (
                  <button
                    key={action?.id}
                    onClick={() => handleAction(action?.id)}
                    className="flex items-center w-full px-3 py-2 text-sm text-popover-foreground hover:bg-muted transition-smooth"
                  >
                    <Icon name={action?.icon} size={16} className="mr-3" />
                    {action?.label}
                  </button>
                ))}
              </div>
            </div>
            
            {/* Backdrop */}
            <div 
              className="fixed inset-0 z-40" 
              onClick={() => setShowDropdown(false)}
            />
          </>
        )}
      </div>
    </div>
  );
};

export default PropertyActions;