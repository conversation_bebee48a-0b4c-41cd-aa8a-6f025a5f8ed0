import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const MaintenanceTable = ({ 
  requests, 
  onStatusUpdate, 
  onAssignContractor, 
  onViewDetails,
  onScheduleInspection,
  onBulkAction,
  loading = false 
}) => {
  const [selectedRequests, setSelectedRequests] = useState([]);
  const [sortConfig, setSortConfig] = useState({ key: 'createdAt', direction: 'desc' });

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'urgent': return 'bg-error text-error-foreground';
      case 'high': return 'bg-warning text-warning-foreground';
      case 'medium': return 'bg-accent text-accent-foreground';
      case 'low': return 'bg-muted text-muted-foreground';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'open': return 'bg-error/10 text-error border-error/20';
      case 'in_progress': return 'bg-warning/10 text-warning border-warning/20';
      case 'scheduled': return 'bg-accent/10 text-accent border-accent/20';
      case 'completed': return 'bg-success/10 text-success border-success/20';
      case 'cancelled': return 'bg-muted text-muted-foreground border-border';
      default: return 'bg-muted text-muted-foreground border-border';
    }
  };

  const getStatusLabel = (status) => {
    switch (status) {
      case 'open': return 'Ouvert';
      case 'in_progress': return 'En cours';
      case 'scheduled': return 'Programmé';
      case 'completed': return 'Terminé';
      case 'cancelled': return 'Annulé';
      default: return status;
    }
  };

  const formatDate = (date) => {
    return new Date(date)?.toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const handleSort = (key) => {
    let direction = 'asc';
    if (sortConfig?.key === key && sortConfig?.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedRequests(requests?.map(r => r?.id));
    } else {
      setSelectedRequests([]);
    }
  };

  const handleSelectRequest = (requestId, checked) => {
    if (checked) {
      setSelectedRequests([...selectedRequests, requestId]);
    } else {
      setSelectedRequests(selectedRequests?.filter(id => id !== requestId));
    }
  };

  const handleBulkAction = (action) => {
    if (selectedRequests?.length > 0) {
      onBulkAction(action, selectedRequests);
      setSelectedRequests([]);
    }
  };

  const sortedRequests = [...requests]?.sort((a, b) => {
    const aValue = a?.[sortConfig?.key];
    const bValue = b?.[sortConfig?.key];
    
    if (sortConfig?.direction === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  if (loading) {
    return (
      <div className="bg-card border border-border rounded-lg p-8">
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-3 text-muted-foreground">Chargement des demandes...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-card border border-border rounded-lg overflow-hidden">
      {/* Bulk Actions */}
      {selectedRequests?.length > 0 && (
        <div className="p-4 bg-primary/5 border-b border-border">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-foreground">
              {selectedRequests?.length} demande(s) sélectionnée(s)
            </span>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleBulkAction('assign')}
              >
                <Icon name="UserPlus" size={14} className="mr-1" />
                Assigner
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleBulkAction('status')}
              >
                <Icon name="Edit" size={14} className="mr-1" />
                Statut
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSelectedRequests([])}
              >
                <Icon name="X" size={14} />
              </Button>
            </div>
          </div>
        </div>
      )}
      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-muted">
            <tr>
              <th className="p-3 text-left">
                <input
                  type="checkbox"
                  checked={selectedRequests?.length === requests?.length && requests?.length > 0}
                  onChange={(e) => handleSelectAll(e?.target?.checked)}
                  className="rounded border-border"
                />
              </th>
              <th className="p-3 text-left">
                <button
                  onClick={() => handleSort('id')}
                  className="flex items-center space-x-1 text-sm font-medium text-foreground hover:text-primary"
                >
                  <span>ID</span>
                  <Icon name="ArrowUpDown" size={14} />
                </button>
              </th>
              <th className="p-3 text-left">
                <button
                  onClick={() => handleSort('title')}
                  className="flex items-center space-x-1 text-sm font-medium text-foreground hover:text-primary"
                >
                  <span>Demande</span>
                  <Icon name="ArrowUpDown" size={14} />
                </button>
              </th>
              <th className="p-3 text-left">
                <button
                  onClick={() => handleSort('priority')}
                  className="flex items-center space-x-1 text-sm font-medium text-foreground hover:text-primary"
                >
                  <span>Priorité</span>
                  <Icon name="ArrowUpDown" size={14} />
                </button>
              </th>
              <th className="p-3 text-left">
                <button
                  onClick={() => handleSort('status')}
                  className="flex items-center space-x-1 text-sm font-medium text-foreground hover:text-primary"
                >
                  <span>Statut</span>
                  <Icon name="ArrowUpDown" size={14} />
                </button>
              </th>
              <th className="p-3 text-left">Propriété</th>
              <th className="p-3 text-left">Locataire</th>
              <th className="p-3 text-left">Prestataire</th>
              <th className="p-3 text-left">
                <button
                  onClick={() => handleSort('createdAt')}
                  className="flex items-center space-x-1 text-sm font-medium text-foreground hover:text-primary"
                >
                  <span>Date</span>
                  <Icon name="ArrowUpDown" size={14} />
                </button>
              </th>
              <th className="p-3 text-left">Coût</th>
              <th className="p-3 text-left">Actions</th>
            </tr>
          </thead>
          <tbody>
            {sortedRequests?.map((request) => (
              <tr key={request?.id} className="border-b border-border hover:bg-muted/50">
                <td className="p-3">
                  <input
                    type="checkbox"
                    checked={selectedRequests?.includes(request?.id)}
                    onChange={(e) => handleSelectRequest(request?.id, e?.target?.checked)}
                    className="rounded border-border"
                  />
                </td>
                <td className="p-3">
                  <span className="font-data text-sm text-muted-foreground">#{request?.id}</span>
                </td>
                <td className="p-3">
                  <div>
                    <p className="font-medium text-foreground text-sm">{request?.title}</p>
                    <p className="text-xs text-muted-foreground line-clamp-1">{request?.description}</p>
                  </div>
                </td>
                <td className="p-3">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(request?.priority)}`}>
                    {request?.priority?.toUpperCase()}
                  </span>
                </td>
                <td className="p-3">
                  <span className={`px-2 py-1 rounded border text-xs font-medium ${getStatusColor(request?.status)}`}>
                    {getStatusLabel(request?.status)}
                  </span>
                </td>
                <td className="p-3">
                  <div className="text-sm">
                    <p className="text-foreground">{request?.property?.address}</p>
                    <p className="text-xs text-muted-foreground">{request?.property?.unit}</p>
                  </div>
                </td>
                <td className="p-3">
                  <div className="text-sm">
                    <p className="text-foreground">{request?.tenant?.name}</p>
                    <p className="text-xs text-muted-foreground">{request?.tenant?.phone}</p>
                  </div>
                </td>
                <td className="p-3">
                  {request?.contractor ? (
                    <div className="text-sm">
                      <p className="text-foreground">{request?.contractor?.name}</p>
                      <p className="text-xs text-muted-foreground">{request?.contractor?.specialty}</p>
                    </div>
                  ) : (
                    <span className="text-xs text-muted-foreground">Non assigné</span>
                  )}
                </td>
                <td className="p-3">
                  <span className="text-sm text-foreground">{formatDate(request?.createdAt)}</span>
                </td>
                <td className="p-3">
                  {request?.estimatedCost ? (
                    <div className="text-sm">
                      <p className="font-data text-foreground">{request?.estimatedCost}€</p>
                      {request?.actualCost && (
                        <p className="font-data text-xs text-muted-foreground">Réel: {request?.actualCost}€</p>
                      )}
                    </div>
                  ) : (
                    <span className="text-xs text-muted-foreground">-</span>
                  )}
                </td>
                <td className="p-3">
                  <div className="flex items-center space-x-1">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => onViewDetails(request?.id)}
                      className="h-8 w-8"
                    >
                      <Icon name="Eye" size={14} />
                    </Button>
                    {request?.status !== 'completed' && request?.status !== 'cancelled' && (
                      <>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => onAssignContractor(request?.id)}
                          className="h-8 w-8"
                        >
                          <Icon name="UserPlus" size={14} />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => onScheduleInspection(request?.id)}
                          className="h-8 w-8"
                        >
                          <Icon name="Calendar" size={14} />
                        </Button>
                      </>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      {requests?.length === 0 && (
        <div className="p-8 text-center">
          <Icon name="Wrench" size={48} className="text-muted-foreground mx-auto mb-4" />
          <h3 className="font-semibold text-foreground mb-2">Aucune demande de maintenance</h3>
          <p className="text-muted-foreground">Les demandes de maintenance apparaîtront ici.</p>
        </div>
      )}
    </div>
  );
};

export default MaintenanceTable;