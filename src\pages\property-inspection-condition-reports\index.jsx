import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import Icon from '../../components/AppIcon';
import Button from '../../components/ui/Button';
import Input from '../../components/ui/Input';
import AgencyHeader from '../../components/ui/AgencyHeader';
import PrimaryNavigation from '../../components/ui/PrimaryNavigation';
import InspectionFilters from './components/InspectionFilters';
import InspectionTable from './components/InspectionTable';
import InspectionStats from './components/InspectionStats';
import ScheduleInspectionModal from './components/ScheduleInspectionModal';
import PhotoUploadModal from './components/PhotoUploadModal';
import ReportGenerationModal from './components/ReportGenerationModal';

const PropertyInspectionConditionReports = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedInspections, setSelectedInspections] = useState([]);
  const [sortConfig, setSortConfig] = useState({ key: 'scheduledDate', direction: 'desc' });
  const [showScheduleModal, setShowScheduleModal] = useState(false);
  const [showPhotoModal, setShowPhotoModal] = useState(false);
  const [showReportModal, setShowReportModal] = useState(false);
  const [selectedInspection, setSelectedInspection] = useState(null);
  const [filters, setFilters] = useState({
    inspectionType: '',
    status: '',
    property: '',
    inspector: '',
    dateRange: { start: '', end: '' },
    priority: ''
  });

  // Mock inspections data
  const [inspections] = useState([
    {
      id: 'INS001',
      property: {
        id: 'P001',
        address: '15 Rue de la Paix, Paris 75001',
        type: 'apartment'
      },
      type: 'entry', // entry, exit, periodic, maintenance
      scheduledDate: '2024-09-15',
      scheduledTime: '10:00',
      status: 'scheduled', // scheduled, in-progress, completed, overdue, cancelled
      inspector: {
        id: 'U001',
        name: 'Marie Dubois',
        phone: '+33 6 12 34 56 78'
      },
      tenant: {
        name: 'Sophie Martin',
        phone: '+33 6 87 65 43 21'
      },
      priority: 'high',
      notes: 'État des lieux d\'entrée pour nouveau locataire',
      photos: [],
      reportGenerated: false,
      createdAt: '2024-09-01T10:00:00Z',
      completedAt: null
    },
    {
      id: 'INS002',
      property: {
        id: 'P002',
        address: '42 Avenue des Champs, Lyon 69001',
        type: 'house'
      },
      type: 'exit',
      scheduledDate: '2024-09-12',
      scheduledTime: '14:30',
      status: 'completed',
      inspector: {
        id: 'U002',
        name: 'Pierre Laurent',
        phone: '+33 6 23 45 67 89'
      },
      tenant: {
        name: 'Claire Rousseau',
        phone: '+33 6 98 76 54 32'
      },
      priority: 'medium',
      notes: 'État des lieux de sortie - quelques réparations nécessaires',
      photos: [
        { id: 'photo1', room: 'cuisine', url: '/photos/ins002_kitchen.jpg', description: 'Carrelage endommagé' },
        { id: 'photo2', room: 'salon', url: '/photos/ins002_living.jpg', description: 'Mur à repeindre' }
      ],
      reportGenerated: true,
      createdAt: '2024-08-25T14:00:00Z',
      completedAt: '2024-09-12T15:45:00Z'
    },
    {
      id: 'INS003',
      property: {
        id: 'P003',
        address: '8 Boulevard Saint-Michel, Paris 75005',
        type: 'studio'
      },
      type: 'periodic',
      scheduledDate: '2024-09-20',
      scheduledTime: '09:00',
      status: 'overdue',
      inspector: {
        id: 'U001',
        name: 'Marie Dubois',
        phone: '+33 6 12 34 56 78'
      },
      tenant: {
        name: 'Pierre Laurent',
        phone: '+33 6 12 34 56 78'
      },
      priority: 'low',
      notes: 'Inspection trimestrielle de routine',
      photos: [],
      reportGenerated: false,
      createdAt: '2024-08-20T09:00:00Z',
      completedAt: null
    },
    {
      id: 'INS004',
      property: {
        id: 'P004',
        address: '23 Rue Victor Hugo, Marseille 13001',
        type: 'duplex'
      },
      type: 'maintenance',
      scheduledDate: '2024-09-18',
      scheduledTime: '11:00',
      status: 'in-progress',
      inspector: {
        id: 'U003',
        name: 'Jean Martin',
        phone: '+33 6 34 56 78 90'
      },
      tenant: null,
      priority: 'high',
      notes: 'Inspection suite à travaux de plomberie',
      photos: [
        { id: 'photo3', room: 'salle de bain', url: '/photos/ins004_bathroom.jpg', description: 'Réparation plomberie' }
      ],
      reportGenerated: false,
      createdAt: '2024-09-10T08:00:00Z',
      completedAt: null
    }
  ]);

  const [filteredInspections, setFilteredInspections] = useState(inspections);

  // Filter and search inspections
  useEffect(() => {
    let filtered = inspections;

    // Apply search
    if (searchQuery) {
      filtered = filtered?.filter(inspection =>
        inspection?.property?.address?.toLowerCase()?.includes(searchQuery?.toLowerCase()) ||
        inspection?.inspector?.name?.toLowerCase()?.includes(searchQuery?.toLowerCase()) ||
        inspection?.tenant?.name?.toLowerCase()?.includes(searchQuery?.toLowerCase()) ||
        inspection?.notes?.toLowerCase()?.includes(searchQuery?.toLowerCase())
      );
    }

    // Apply filters
    if (filters?.inspectionType) {
      filtered = filtered?.filter(i => i?.type === filters?.inspectionType);
    }
    if (filters?.status) {
      filtered = filtered?.filter(i => i?.status === filters?.status);
    }
    if (filters?.inspector) {
      filtered = filtered?.filter(i => i?.inspector?.name?.toLowerCase()?.includes(filters?.inspector?.toLowerCase()));
    }
    if (filters?.priority) {
      filtered = filtered?.filter(i => i?.priority === filters?.priority);
    }
    if (filters?.dateRange?.start) {
      filtered = filtered?.filter(i => i?.scheduledDate >= filters?.dateRange?.start);
    }
    if (filters?.dateRange?.end) {
      filtered = filtered?.filter(i => i?.scheduledDate <= filters?.dateRange?.end);
    }

    setFilteredInspections(filtered);
  }, [searchQuery, filters, inspections]);

  // Sort inspections
  const sortedInspections = React.useMemo(() => {
    const sorted = [...filteredInspections];
    sorted?.sort((a, b) => {
      let aValue = a?.[sortConfig?.key];
      let bValue = b?.[sortConfig?.key];

      // Handle nested properties
      if (sortConfig?.key === 'property') {
        aValue = a?.property?.address;
        bValue = b?.property?.address;
      } else if (sortConfig?.key === 'inspector') {
        aValue = a?.inspector?.name;
        bValue = b?.inspector?.name;
      }

      if (aValue < bValue) {
        return sortConfig?.direction === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortConfig?.direction === 'asc' ? 1 : -1;
      }
      return 0;
    });
    return sorted;
  }, [filteredInspections, sortConfig]);

  const handleSort = (key) => {
    setSortConfig(prevConfig => ({
      key,
      direction: prevConfig?.key === key && prevConfig?.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const handleInspectionAction = (action, inspection) => {
    console.log(`Action: ${action} on inspection:`, inspection);
    switch (action) {
      case 'view':
        setSelectedInspection(inspection);
        setShowReportModal(true);
        break;
      case 'edit':
        setSelectedInspection(inspection);
        setShowScheduleModal(true);
        break;
      case 'photos':
        setSelectedInspection(inspection);
        setShowPhotoModal(true);
        break;
      case 'complete':
        // Mark inspection as completed
        break;
      case 'report':
        setSelectedInspection(inspection);
        setShowReportModal(true);
        break;
      default:
        break;
    }
  };

  const handleScheduleInspection = () => {
    setSelectedInspection(null);
    setShowScheduleModal(true);
  };

  const handleExport = () => {
    console.log('Exporting inspection reports...');
    // Implement export functionality
  };

  const handleLogout = () => {
    navigate('/agency-login');
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <AgencyHeader
        agencyName="PropertyFlow"
        userName="Marie Dubois"
        userRole="Gestionnaire"
        onLogout={handleLogout}
      />

      {/* Navigation */}
      <PrimaryNavigation />

      {/* Main Content */}
      <main className="pt-32 pb-8">
        <div className="max-w-7xl mx-auto px-6">
          {/* Page Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-2xl font-bold text-foreground mb-2">
                  États des Lieux & Inspections
                </h1>
                <p className="text-muted-foreground">
                  Planifiez et gérez les inspections de propriétés et la documentation des conditions
                </p>
              </div>
              <Button
                variant="default"
                onClick={handleScheduleInspection}
                iconName="Calendar"
                iconPosition="left"
              >
                Planifier une inspection
              </Button>
            </div>

            {/* Stats */}
            <InspectionStats inspections={inspections} className="mb-6" />
          </div>

          {/* Toolbar */}
          <div className="bg-card border border-border rounded-lg p-4 mb-6">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
              {/* Search and Filters */}
              <div className="flex items-center space-x-4 flex-1">
                <div className="relative flex-1 max-w-md">
                  <Icon name="Search" size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Rechercher par propriété, inspecteur, notes..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e?.target?.value)}
                    className="pl-10"
                  />
                </div>
                
                <Button
                  variant={showFilters ? "default" : "outline"}
                  onClick={() => setShowFilters(!showFilters)}
                  iconName="Filter"
                  iconPosition="left"
                >
                  Filtres
                  {Object.values(filters)?.filter(v => v !== '' && v !== false)?.length > 0 && (
                    <span className="ml-2 bg-primary text-primary-foreground text-xs px-2 py-1 rounded-full">
                      {Object.values(filters)?.filter(v => v !== '' && v !== false)?.length}
                    </span>
                  )}
                </Button>
              </div>

              {/* Actions */}
              <div className="flex items-center space-x-4">
                <Button
                  variant="outline"
                  onClick={handleExport}
                  iconName="Download"
                  iconPosition="left"
                >
                  Exporter
                </Button>
              </div>
            </div>
          </div>

          {/* Filters Panel */}
          {showFilters && (
            <div className="mb-6">
              <InspectionFilters
                isOpen={showFilters}
                onToggle={() => setShowFilters(!showFilters)}
                filters={filters}
                onFiltersChange={setFilters}
                onClearFilters={() => setFilters({
                  inspectionType: '',
                  status: '',
                  property: '',
                  inspector: '',
                  dateRange: { start: '', end: '' },
                  priority: ''
                })}
              />
            </div>
          )}

          {/* Inspections Table */}
          <InspectionTable
            inspections={sortedInspections}
            selectedInspections={selectedInspections}
            onSelectionChange={setSelectedInspections}
            onSort={handleSort}
            sortConfig={sortConfig}
            onInspectionAction={handleInspectionAction}
          />

          {/* Results Summary */}
          {filteredInspections?.length > 0 && (
            <div className="mt-6 text-center">
              <p className="text-sm text-muted-foreground">
                Affichage de {filteredInspections?.length} inspection{filteredInspections?.length > 1 ? 's' : ''} sur {inspections?.length} au total
              </p>
            </div>
          )}
        </div>
      </main>

      {/* Modals */}
      {showScheduleModal && (
        <ScheduleInspectionModal
          isOpen={showScheduleModal}
          onClose={() => {
            setShowScheduleModal(false);
            setSelectedInspection(null);
          }}
          inspection={selectedInspection}
          onSave={(inspectionData) => {
            console.log('Saving inspection:', inspectionData);
            // Implement save logic here
            setShowScheduleModal(false);
            setSelectedInspection(null);
          }}
        />
      )}

      {showPhotoModal && selectedInspection && (
        <PhotoUploadModal
          isOpen={showPhotoModal}
          onClose={() => {
            setShowPhotoModal(false);
            setSelectedInspection(null);
          }}
          inspection={selectedInspection}
          onUpload={(photos) => {
            console.log('Uploading photos:', photos);
            // Implement photo upload logic here
            setShowPhotoModal(false);
            setSelectedInspection(null);
          }}
        />
      )}

      {showReportModal && selectedInspection && (
        <ReportGenerationModal
          isOpen={showReportModal}
          onClose={() => {
            setShowReportModal(false);
            setSelectedInspection(null);
          }}
          inspection={selectedInspection}
          onGenerate={(reportOptions) => {
            console.log('Generating report:', reportOptions);
            // Implement report generation logic here
            setShowReportModal(false);
            setSelectedInspection(null);
          }}
        />
      )}
    </div>
  );
};

export default PropertyInspectionConditionReports;