"""
PropertyFlow Backend - Middleware personnalisé
"""

from flask import request, g, jsonify
from flask_jwt_extended import get_jwt_identity, get_jwt, verify_jwt_in_request
from functools import wraps
import logging
import time
from database import utilisateur_service
from utils.errors import AuthenticationError, AuthorizationError

logger = logging.getLogger(__name__)

def setup_middleware(app):
    """Configurer les middlewares de l'application"""
    
    @app.before_request
    def before_request():
        """Middleware exécuté avant chaque requête"""
        g.start_time = time.time()
        
        # Logger la requête
        logger.info(f"{request.method} {request.path} - IP: {request.remote_addr}")
        
        # Vérifier l'authentification pour les routes protégées
        if request.endpoint and request.endpoint.startswith('api'):
            # Exclure les routes publiques
            public_routes = ['auth.login', 'auth.register', 'health_check']
            if request.endpoint not in public_routes:
                try:
                    verify_jwt_in_request()
                    # Charger les informations utilisateur
                    current_user_id = get_jwt_identity()
                    claims = get_jwt()
                    
                    g.current_user_id = current_user_id
                    g.agence_id = claims.get('agence_id')
                    g.user_role = claims.get('role')
                    
                except Exception as e:
                    logger.warning(f"JWT verification failed: {e}")
    
    @app.after_request
    def after_request(response):
        """Middleware exécuté après chaque requête"""
        # Calculer le temps de traitement
        if hasattr(g, 'start_time'):
            duration = time.time() - g.start_time
            response.headers['X-Response-Time'] = f"{duration:.3f}s"
        
        # Headers de sécurité
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        
        # Logger la réponse
        logger.info(f"{request.method} {request.path} - {response.status_code} - {response.headers.get('X-Response-Time', 'N/A')}")
        
        return response

def require_role(*allowed_roles):
    """Décorateur pour vérifier les rôles utilisateur"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not hasattr(g, 'user_role') or g.user_role not in allowed_roles:
                raise AuthorizationError(f"Role required: {', '.join(allowed_roles)}")
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def require_agence_access(f):
    """Décorateur pour vérifier l'accès à l'agence"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not hasattr(g, 'agence_id') or not g.agence_id:
            raise AuthorizationError("Agency access required")
        return f(*args, **kwargs)
    return decorated_function

def validate_json(required_fields=None):
    """Décorateur pour valider les données JSON"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not request.is_json:
                return jsonify({'error': 'Content-Type must be application/json'}), 400
            
            data = request.get_json()
            if not data:
                return jsonify({'error': 'No JSON data provided'}), 400
            
            if required_fields:
                missing_fields = [field for field in required_fields if field not in data]
                if missing_fields:
                    return jsonify({
                        'error': 'Missing required fields',
                        'missing_fields': missing_fields
                    }), 400
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def rate_limit(max_requests=100, window=3600):
    """Décorateur pour limiter le taux de requêtes (simple implémentation)"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # TODO: Implémenter avec Redis pour une vraie limitation
            # Pour l'instant, on laisse passer toutes les requêtes
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def log_activity(action):
    """Décorateur pour logger les activités utilisateur"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                result = f(*args, **kwargs)
                
                # Logger l'activité réussie
                if hasattr(g, 'current_user_id'):
                    logger.info(f"User {g.current_user_id} performed action: {action}")
                
                return result
            except Exception as e:
                # Logger l'activité échouée
                if hasattr(g, 'current_user_id'):
                    logger.warning(f"User {g.current_user_id} failed action: {action} - Error: {str(e)}")
                raise
        return decorated_function
    return decorator
