import React from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const QuickActions = ({ className = "" }) => {
  const quickActions = [
    {
      id: 'add-property',
      label: 'Nouvelle propriété',
      description: 'Ajouter une propriété au portefeuille',
      icon: 'Building2',
      color: 'primary',
      onClick: () => console.log('Add property')
    },
    {
      id: 'add-tenant',
      label: 'Nouveau locataire',
      description: 'Enregistrer un nouveau locataire',
      icon: 'UserPlus',
      color: 'success',
      onClick: () => console.log('Add tenant')
    },
    {
      id: 'record-payment',
      label: 'Enregistrer paiement',
      description: 'Saisir un paiement de loyer',
      icon: 'CreditCard',
      color: 'accent',
      onClick: () => console.log('Record payment')
    },
    {
      id: 'maintenance-request',
      label: 'Demande maintenance',
      description: 'Créer une demande de maintenance',
      icon: 'Wrench',
      color: 'warning',
      onClick: () => console.log('Maintenance request')
    },
    {
      id: 'generate-report',
      label: 'Générer rapport',
      description: 'Créer un rapport personnalisé',
      icon: 'FileText',
      color: 'secondary',
      onClick: () => console.log('Generate report')
    },
    {
      id: 'schedule-inspection',
      label: 'Programmer inspection',
      description: 'Planifier une visite d\'état des lieux',
      icon: 'Calendar',
      color: 'accent',
      onClick: () => console.log('Schedule inspection')
    }
  ];

  const getColorClasses = (color) => {
    switch (color) {
      case 'success':
        return 'bg-success/10 text-success hover:bg-success/20';
      case 'warning':
        return 'bg-warning/10 text-warning hover:bg-warning/20';
      case 'error':
        return 'bg-error/10 text-error hover:bg-error/20';
      case 'accent':
        return 'bg-accent/10 text-accent hover:bg-accent/20';
      case 'secondary':
        return 'bg-secondary/10 text-secondary hover:bg-secondary/20';
      default:
        return 'bg-primary/10 text-primary hover:bg-primary/20';
    }
  };

  return (
    <div className={`bg-card border border-border rounded-lg ${className}`}>
      <div className="p-6 border-b border-border">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-foreground">Actions rapides</h3>
            <p className="text-sm text-muted-foreground">Accès direct aux tâches courantes</p>
          </div>
          <Button variant="ghost" size="sm">
            <Icon name="Settings" size={16} className="mr-2" />
            Personnaliser
          </Button>
        </div>
      </div>
      <div className="p-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {quickActions?.map((action) => (
            <button
              key={action?.id}
              onClick={action?.onClick}
              className="flex items-start space-x-3 p-4 rounded-lg border border-border hover:border-primary/20 hover:elevation-1 transition-smooth text-left group"
            >
              <div className={`p-3 rounded-lg transition-smooth ${getColorClasses(action?.color)}`}>
                <Icon name={action?.icon} size={20} />
              </div>
              <div className="flex-1 min-w-0">
                <p className="font-medium text-sm text-foreground group-hover:text-primary transition-smooth">
                  {action?.label}
                </p>
                <p className="text-xs text-muted-foreground mt-1">
                  {action?.description}
                </p>
              </div>
              <Icon 
                name="ChevronRight" 
                size={16} 
                className="text-muted-foreground group-hover:text-primary transition-smooth" 
              />
            </button>
          ))}
        </div>

        <div className="mt-6 pt-6 border-t border-border">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Icon name="Zap" size={16} className="text-primary" />
              <span className="text-sm font-medium text-foreground">Raccourcis clavier</span>
            </div>
            <Button variant="ghost" size="sm">
              <Icon name="Keyboard" size={16} className="mr-2" />
              Voir tous
            </Button>
          </div>
          <div className="mt-3 grid grid-cols-2 md:grid-cols-3 gap-2 text-xs">
            <div className="flex items-center justify-between p-2 bg-muted rounded">
              <span className="text-muted-foreground">Nouvelle propriété</span>
              <kbd className="px-2 py-1 bg-background border border-border rounded text-xs">Ctrl+P</kbd>
            </div>
            <div className="flex items-center justify-between p-2 bg-muted rounded">
              <span className="text-muted-foreground">Nouveau locataire</span>
              <kbd className="px-2 py-1 bg-background border border-border rounded text-xs">Ctrl+T</kbd>
            </div>
            <div className="flex items-center justify-between p-2 bg-muted rounded">
              <span className="text-muted-foreground">Recherche globale</span>
              <kbd className="px-2 py-1 bg-background border border-border rounded text-xs">Ctrl+K</kbd>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuickActions;