import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const TransactionsList = ({ transactions, title = "Transactions récentes" }) => {
  const [filter, setFilter] = useState('all');
  const [sortBy, setSortBy] = useState('date');

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    })?.format(amount);
  };

  const formatDate = (date) => {
    return new Intl.DateTimeFormat('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    })?.format(new Date(date));
  };

  const getTransactionIcon = (type) => {
    switch (type) {
      case 'rent': return 'Home';
      case 'deposit': return 'Shield';
      case 'maintenance': return 'Wrench';
      case 'fee': return 'CreditCard';
      case 'refund': return 'RotateCcw';
      case 'penalty': return 'AlertTriangle';
      default: return 'DollarSign';
    }
  };

  const getTransactionColor = (type, amount) => {
    if (amount > 0) {
      return 'text-success bg-success/10';
    }
    return 'text-error bg-error/10';
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'text-success bg-success/10';
      case 'pending': return 'text-warning bg-warning/10';
      case 'failed': return 'text-error bg-error/10';
      default: return 'text-muted-foreground bg-muted';
    }
  };

  const getStatusLabel = (status) => {
    switch (status) {
      case 'completed': return 'Terminé';
      case 'pending': return 'En attente';
      case 'failed': return 'Échec';
      default: return 'Inconnu';
    }
  };

  const filterOptions = [
    { value: 'all', label: 'Toutes' },
    { value: 'income', label: 'Revenus' },
    { value: 'expense', label: 'Dépenses' },
    { value: 'pending', label: 'En attente' }
  ];

  const filteredTransactions = transactions?.filter(transaction => {
    if (filter === 'all') return true;
    if (filter === 'income') return transaction?.amount > 0;
    if (filter === 'expense') return transaction?.amount < 0;
    if (filter === 'pending') return transaction?.status === 'pending';
    return true;
  });

  const sortedTransactions = [...filteredTransactions]?.sort((a, b) => {
    if (sortBy === 'date') {
      return new Date(b.date) - new Date(a.date);
    }
    if (sortBy === 'amount') {
      return Math.abs(b?.amount) - Math.abs(a?.amount);
    }
    return 0;
  });

  return (
    <div className="bg-card border border-border rounded-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-foreground">{title}</h3>
        <div className="flex items-center space-x-2">
          {/* Filter */}
          <div className="flex bg-muted rounded-lg p-1">
            {filterOptions?.map((option) => (
              <button
                key={option?.value}
                onClick={() => setFilter(option?.value)}
                className={`px-3 py-1 text-xs font-medium rounded transition-smooth ${
                  filter === option?.value
                    ? 'bg-background text-foreground shadow-sm'
                    : 'text-muted-foreground hover:text-foreground'
                }`}
              >
                {option?.label}
              </button>
            ))}
          </div>
          
          {/* Sort */}
          <Button variant="ghost" size="sm">
            <Icon name="Filter" size={16} />
          </Button>
        </div>
      </div>
      <div className="space-y-3 max-h-96 overflow-y-auto">
        {sortedTransactions?.length === 0 ? (
          <div className="text-center py-8">
            <Icon name="Receipt" size={32} className="text-muted-foreground mx-auto mb-2" />
            <p className="text-muted-foreground">Aucune transaction trouvée</p>
          </div>
        ) : (
          sortedTransactions?.map((transaction) => (
            <div
              key={transaction?.id}
              className="flex items-center justify-between p-4 bg-muted/30 rounded-lg hover:bg-muted/50 transition-smooth"
            >
              <div className="flex items-center space-x-4">
                <div className={`p-2 rounded-lg ${getTransactionColor(transaction?.type, transaction?.amount)}`}>
                  <Icon name={getTransactionIcon(transaction?.type)} size={16} />
                </div>
                <div>
                  <p className="font-medium text-foreground">{transaction?.description}</p>
                  <div className="flex items-center space-x-2 mt-1">
                    <p className="text-sm text-muted-foreground">{transaction?.property}</p>
                    <span className="text-muted-foreground">•</span>
                    <p className="text-sm text-muted-foreground">{formatDate(transaction?.date)}</p>
                  </div>
                </div>
              </div>
              
              <div className="text-right">
                <p className={`font-bold ${
                  transaction?.amount > 0 ? 'text-success' : 'text-error'
                }`}>
                  {transaction?.amount > 0 ? '+' : ''}{formatCurrency(transaction?.amount)}
                </p>
                <div className="flex items-center justify-end mt-1">
                  <span className={`text-xs px-2 py-1 rounded-full font-medium ${getStatusColor(transaction?.status)}`}>
                    {getStatusLabel(transaction?.status)}
                  </span>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
      {sortedTransactions?.length > 0 && (
        <div className="flex items-center justify-between mt-4 pt-4 border-t border-border">
          <p className="text-sm text-muted-foreground">
            {sortedTransactions?.length} transaction{sortedTransactions?.length > 1 ? 's' : ''}
          </p>
          <Button variant="ghost" size="sm">
            <Icon name="ExternalLink" size={14} className="mr-2" />
            Voir toutes
          </Button>
        </div>
      )}
    </div>
  );
};

export default TransactionsList;